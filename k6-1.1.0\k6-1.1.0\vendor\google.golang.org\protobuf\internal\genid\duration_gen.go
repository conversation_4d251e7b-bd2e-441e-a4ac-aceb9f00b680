// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_google_protobuf_duration_proto = "google/protobuf/duration.proto"

// Names for google.protobuf.Duration.
const (
	Duration_message_name     protoreflect.Name     = "Duration"
	Duration_message_fullname protoreflect.FullName = "google.protobuf.Duration"
)

// Field names for google.protobuf.Duration.
const (
	Duration_Seconds_field_name protoreflect.Name = "seconds"
	Duration_Nanos_field_name   protoreflect.Name = "nanos"

	Duration_Seconds_field_fullname protoreflect.FullName = "google.protobuf.Duration.seconds"
	Duration_Nanos_field_fullname   protoreflect.FullName = "google.protobuf.Duration.nanos"
)

// Field numbers for google.protobuf.Duration.
const (
	Duration_Seconds_field_number protoreflect.FieldNumber = 1
	Duration_Nanos_field_number   protoreflect.FieldNumber = 2
)
