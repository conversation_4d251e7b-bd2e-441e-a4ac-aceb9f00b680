// go run mkasm.go openbsd amd64
// Code generated by the command above; DO NOT EDIT.

#include "textflag.h"

TEXT libc_getgroups_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getgroups(SB)
GLOBL	·libc_getgroups_trampoline_addr(SB), RODATA, $8
DATA	·libc_getgroups_trampoline_addr(SB)/8, $libc_getgroups_trampoline<>(SB)

TEXT libc_setgroups_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setgroups(SB)
GLOBL	·libc_setgroups_trampoline_addr(SB), RODATA, $8
DATA	·libc_setgroups_trampoline_addr(SB)/8, $libc_setgroups_trampoline<>(SB)

TEXT libc_wait4_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_wait4(SB)
GLOBL	·libc_wait4_trampoline_addr(SB), RODATA, $8
DATA	·libc_wait4_trampoline_addr(SB)/8, $libc_wait4_trampoline<>(SB)

TEXT libc_accept_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_accept(SB)
GLOBL	·libc_accept_trampoline_addr(SB), RODATA, $8
DATA	·libc_accept_trampoline_addr(SB)/8, $libc_accept_trampoline<>(SB)

TEXT libc_bind_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_bind(SB)
GLOBL	·libc_bind_trampoline_addr(SB), RODATA, $8
DATA	·libc_bind_trampoline_addr(SB)/8, $libc_bind_trampoline<>(SB)

TEXT libc_connect_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_connect(SB)
GLOBL	·libc_connect_trampoline_addr(SB), RODATA, $8
DATA	·libc_connect_trampoline_addr(SB)/8, $libc_connect_trampoline<>(SB)

TEXT libc_socket_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_socket(SB)
GLOBL	·libc_socket_trampoline_addr(SB), RODATA, $8
DATA	·libc_socket_trampoline_addr(SB)/8, $libc_socket_trampoline<>(SB)

TEXT libc_getsockopt_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getsockopt(SB)
GLOBL	·libc_getsockopt_trampoline_addr(SB), RODATA, $8
DATA	·libc_getsockopt_trampoline_addr(SB)/8, $libc_getsockopt_trampoline<>(SB)

TEXT libc_setsockopt_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setsockopt(SB)
GLOBL	·libc_setsockopt_trampoline_addr(SB), RODATA, $8
DATA	·libc_setsockopt_trampoline_addr(SB)/8, $libc_setsockopt_trampoline<>(SB)

TEXT libc_getpeername_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getpeername(SB)
GLOBL	·libc_getpeername_trampoline_addr(SB), RODATA, $8
DATA	·libc_getpeername_trampoline_addr(SB)/8, $libc_getpeername_trampoline<>(SB)

TEXT libc_getsockname_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getsockname(SB)
GLOBL	·libc_getsockname_trampoline_addr(SB), RODATA, $8
DATA	·libc_getsockname_trampoline_addr(SB)/8, $libc_getsockname_trampoline<>(SB)

TEXT libc_shutdown_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_shutdown(SB)
GLOBL	·libc_shutdown_trampoline_addr(SB), RODATA, $8
DATA	·libc_shutdown_trampoline_addr(SB)/8, $libc_shutdown_trampoline<>(SB)

TEXT libc_socketpair_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_socketpair(SB)
GLOBL	·libc_socketpair_trampoline_addr(SB), RODATA, $8
DATA	·libc_socketpair_trampoline_addr(SB)/8, $libc_socketpair_trampoline<>(SB)

TEXT libc_recvfrom_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_recvfrom(SB)
GLOBL	·libc_recvfrom_trampoline_addr(SB), RODATA, $8
DATA	·libc_recvfrom_trampoline_addr(SB)/8, $libc_recvfrom_trampoline<>(SB)

TEXT libc_sendto_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_sendto(SB)
GLOBL	·libc_sendto_trampoline_addr(SB), RODATA, $8
DATA	·libc_sendto_trampoline_addr(SB)/8, $libc_sendto_trampoline<>(SB)

TEXT libc_recvmsg_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_recvmsg(SB)
GLOBL	·libc_recvmsg_trampoline_addr(SB), RODATA, $8
DATA	·libc_recvmsg_trampoline_addr(SB)/8, $libc_recvmsg_trampoline<>(SB)

TEXT libc_sendmsg_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_sendmsg(SB)
GLOBL	·libc_sendmsg_trampoline_addr(SB), RODATA, $8
DATA	·libc_sendmsg_trampoline_addr(SB)/8, $libc_sendmsg_trampoline<>(SB)

TEXT libc_kevent_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_kevent(SB)
GLOBL	·libc_kevent_trampoline_addr(SB), RODATA, $8
DATA	·libc_kevent_trampoline_addr(SB)/8, $libc_kevent_trampoline<>(SB)

TEXT libc_utimes_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_utimes(SB)
GLOBL	·libc_utimes_trampoline_addr(SB), RODATA, $8
DATA	·libc_utimes_trampoline_addr(SB)/8, $libc_utimes_trampoline<>(SB)

TEXT libc_futimes_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_futimes(SB)
GLOBL	·libc_futimes_trampoline_addr(SB), RODATA, $8
DATA	·libc_futimes_trampoline_addr(SB)/8, $libc_futimes_trampoline<>(SB)

TEXT libc_poll_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_poll(SB)
GLOBL	·libc_poll_trampoline_addr(SB), RODATA, $8
DATA	·libc_poll_trampoline_addr(SB)/8, $libc_poll_trampoline<>(SB)

TEXT libc_madvise_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_madvise(SB)
GLOBL	·libc_madvise_trampoline_addr(SB), RODATA, $8
DATA	·libc_madvise_trampoline_addr(SB)/8, $libc_madvise_trampoline<>(SB)

TEXT libc_mlock_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mlock(SB)
GLOBL	·libc_mlock_trampoline_addr(SB), RODATA, $8
DATA	·libc_mlock_trampoline_addr(SB)/8, $libc_mlock_trampoline<>(SB)

TEXT libc_mlockall_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mlockall(SB)
GLOBL	·libc_mlockall_trampoline_addr(SB), RODATA, $8
DATA	·libc_mlockall_trampoline_addr(SB)/8, $libc_mlockall_trampoline<>(SB)

TEXT libc_mprotect_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mprotect(SB)
GLOBL	·libc_mprotect_trampoline_addr(SB), RODATA, $8
DATA	·libc_mprotect_trampoline_addr(SB)/8, $libc_mprotect_trampoline<>(SB)

TEXT libc_msync_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_msync(SB)
GLOBL	·libc_msync_trampoline_addr(SB), RODATA, $8
DATA	·libc_msync_trampoline_addr(SB)/8, $libc_msync_trampoline<>(SB)

TEXT libc_munlock_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_munlock(SB)
GLOBL	·libc_munlock_trampoline_addr(SB), RODATA, $8
DATA	·libc_munlock_trampoline_addr(SB)/8, $libc_munlock_trampoline<>(SB)

TEXT libc_munlockall_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_munlockall(SB)
GLOBL	·libc_munlockall_trampoline_addr(SB), RODATA, $8
DATA	·libc_munlockall_trampoline_addr(SB)/8, $libc_munlockall_trampoline<>(SB)

TEXT libc_pipe2_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_pipe2(SB)
GLOBL	·libc_pipe2_trampoline_addr(SB), RODATA, $8
DATA	·libc_pipe2_trampoline_addr(SB)/8, $libc_pipe2_trampoline<>(SB)

TEXT libc_getdents_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getdents(SB)
GLOBL	·libc_getdents_trampoline_addr(SB), RODATA, $8
DATA	·libc_getdents_trampoline_addr(SB)/8, $libc_getdents_trampoline<>(SB)

TEXT libc_getcwd_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getcwd(SB)
GLOBL	·libc_getcwd_trampoline_addr(SB), RODATA, $8
DATA	·libc_getcwd_trampoline_addr(SB)/8, $libc_getcwd_trampoline<>(SB)

TEXT libc_getresuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getresuid(SB)
GLOBL	·libc_getresuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getresuid_trampoline_addr(SB)/8, $libc_getresuid_trampoline<>(SB)

TEXT libc_getresgid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getresgid(SB)
GLOBL	·libc_getresgid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getresgid_trampoline_addr(SB)/8, $libc_getresgid_trampoline<>(SB)

TEXT libc_ioctl_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_ioctl(SB)
GLOBL	·libc_ioctl_trampoline_addr(SB), RODATA, $8
DATA	·libc_ioctl_trampoline_addr(SB)/8, $libc_ioctl_trampoline<>(SB)

TEXT libc_sysctl_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_sysctl(SB)
GLOBL	·libc_sysctl_trampoline_addr(SB), RODATA, $8
DATA	·libc_sysctl_trampoline_addr(SB)/8, $libc_sysctl_trampoline<>(SB)

TEXT libc_fcntl_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fcntl(SB)
GLOBL	·libc_fcntl_trampoline_addr(SB), RODATA, $8
DATA	·libc_fcntl_trampoline_addr(SB)/8, $libc_fcntl_trampoline<>(SB)

TEXT libc_ppoll_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_ppoll(SB)
GLOBL	·libc_ppoll_trampoline_addr(SB), RODATA, $8
DATA	·libc_ppoll_trampoline_addr(SB)/8, $libc_ppoll_trampoline<>(SB)

TEXT libc_access_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_access(SB)
GLOBL	·libc_access_trampoline_addr(SB), RODATA, $8
DATA	·libc_access_trampoline_addr(SB)/8, $libc_access_trampoline<>(SB)

TEXT libc_adjtime_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_adjtime(SB)
GLOBL	·libc_adjtime_trampoline_addr(SB), RODATA, $8
DATA	·libc_adjtime_trampoline_addr(SB)/8, $libc_adjtime_trampoline<>(SB)

TEXT libc_chdir_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_chdir(SB)
GLOBL	·libc_chdir_trampoline_addr(SB), RODATA, $8
DATA	·libc_chdir_trampoline_addr(SB)/8, $libc_chdir_trampoline<>(SB)

TEXT libc_chflags_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_chflags(SB)
GLOBL	·libc_chflags_trampoline_addr(SB), RODATA, $8
DATA	·libc_chflags_trampoline_addr(SB)/8, $libc_chflags_trampoline<>(SB)

TEXT libc_chmod_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_chmod(SB)
GLOBL	·libc_chmod_trampoline_addr(SB), RODATA, $8
DATA	·libc_chmod_trampoline_addr(SB)/8, $libc_chmod_trampoline<>(SB)

TEXT libc_chown_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_chown(SB)
GLOBL	·libc_chown_trampoline_addr(SB), RODATA, $8
DATA	·libc_chown_trampoline_addr(SB)/8, $libc_chown_trampoline<>(SB)

TEXT libc_chroot_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_chroot(SB)
GLOBL	·libc_chroot_trampoline_addr(SB), RODATA, $8
DATA	·libc_chroot_trampoline_addr(SB)/8, $libc_chroot_trampoline<>(SB)

TEXT libc_clock_gettime_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_clock_gettime(SB)
GLOBL	·libc_clock_gettime_trampoline_addr(SB), RODATA, $8
DATA	·libc_clock_gettime_trampoline_addr(SB)/8, $libc_clock_gettime_trampoline<>(SB)

TEXT libc_close_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_close(SB)
GLOBL	·libc_close_trampoline_addr(SB), RODATA, $8
DATA	·libc_close_trampoline_addr(SB)/8, $libc_close_trampoline<>(SB)

TEXT libc_dup_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_dup(SB)
GLOBL	·libc_dup_trampoline_addr(SB), RODATA, $8
DATA	·libc_dup_trampoline_addr(SB)/8, $libc_dup_trampoline<>(SB)

TEXT libc_dup2_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_dup2(SB)
GLOBL	·libc_dup2_trampoline_addr(SB), RODATA, $8
DATA	·libc_dup2_trampoline_addr(SB)/8, $libc_dup2_trampoline<>(SB)

TEXT libc_dup3_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_dup3(SB)
GLOBL	·libc_dup3_trampoline_addr(SB), RODATA, $8
DATA	·libc_dup3_trampoline_addr(SB)/8, $libc_dup3_trampoline<>(SB)

TEXT libc_exit_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_exit(SB)
GLOBL	·libc_exit_trampoline_addr(SB), RODATA, $8
DATA	·libc_exit_trampoline_addr(SB)/8, $libc_exit_trampoline<>(SB)

TEXT libc_faccessat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_faccessat(SB)
GLOBL	·libc_faccessat_trampoline_addr(SB), RODATA, $8
DATA	·libc_faccessat_trampoline_addr(SB)/8, $libc_faccessat_trampoline<>(SB)

TEXT libc_fchdir_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fchdir(SB)
GLOBL	·libc_fchdir_trampoline_addr(SB), RODATA, $8
DATA	·libc_fchdir_trampoline_addr(SB)/8, $libc_fchdir_trampoline<>(SB)

TEXT libc_fchflags_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fchflags(SB)
GLOBL	·libc_fchflags_trampoline_addr(SB), RODATA, $8
DATA	·libc_fchflags_trampoline_addr(SB)/8, $libc_fchflags_trampoline<>(SB)

TEXT libc_fchmod_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fchmod(SB)
GLOBL	·libc_fchmod_trampoline_addr(SB), RODATA, $8
DATA	·libc_fchmod_trampoline_addr(SB)/8, $libc_fchmod_trampoline<>(SB)

TEXT libc_fchmodat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fchmodat(SB)
GLOBL	·libc_fchmodat_trampoline_addr(SB), RODATA, $8
DATA	·libc_fchmodat_trampoline_addr(SB)/8, $libc_fchmodat_trampoline<>(SB)

TEXT libc_fchown_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fchown(SB)
GLOBL	·libc_fchown_trampoline_addr(SB), RODATA, $8
DATA	·libc_fchown_trampoline_addr(SB)/8, $libc_fchown_trampoline<>(SB)

TEXT libc_fchownat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fchownat(SB)
GLOBL	·libc_fchownat_trampoline_addr(SB), RODATA, $8
DATA	·libc_fchownat_trampoline_addr(SB)/8, $libc_fchownat_trampoline<>(SB)

TEXT libc_flock_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_flock(SB)
GLOBL	·libc_flock_trampoline_addr(SB), RODATA, $8
DATA	·libc_flock_trampoline_addr(SB)/8, $libc_flock_trampoline<>(SB)

TEXT libc_fpathconf_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fpathconf(SB)
GLOBL	·libc_fpathconf_trampoline_addr(SB), RODATA, $8
DATA	·libc_fpathconf_trampoline_addr(SB)/8, $libc_fpathconf_trampoline<>(SB)

TEXT libc_fstat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fstat(SB)
GLOBL	·libc_fstat_trampoline_addr(SB), RODATA, $8
DATA	·libc_fstat_trampoline_addr(SB)/8, $libc_fstat_trampoline<>(SB)

TEXT libc_fstatat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fstatat(SB)
GLOBL	·libc_fstatat_trampoline_addr(SB), RODATA, $8
DATA	·libc_fstatat_trampoline_addr(SB)/8, $libc_fstatat_trampoline<>(SB)

TEXT libc_fstatfs_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fstatfs(SB)
GLOBL	·libc_fstatfs_trampoline_addr(SB), RODATA, $8
DATA	·libc_fstatfs_trampoline_addr(SB)/8, $libc_fstatfs_trampoline<>(SB)

TEXT libc_fsync_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_fsync(SB)
GLOBL	·libc_fsync_trampoline_addr(SB), RODATA, $8
DATA	·libc_fsync_trampoline_addr(SB)/8, $libc_fsync_trampoline<>(SB)

TEXT libc_ftruncate_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_ftruncate(SB)
GLOBL	·libc_ftruncate_trampoline_addr(SB), RODATA, $8
DATA	·libc_ftruncate_trampoline_addr(SB)/8, $libc_ftruncate_trampoline<>(SB)

TEXT libc_getegid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getegid(SB)
GLOBL	·libc_getegid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getegid_trampoline_addr(SB)/8, $libc_getegid_trampoline<>(SB)

TEXT libc_geteuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_geteuid(SB)
GLOBL	·libc_geteuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_geteuid_trampoline_addr(SB)/8, $libc_geteuid_trampoline<>(SB)

TEXT libc_getgid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getgid(SB)
GLOBL	·libc_getgid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getgid_trampoline_addr(SB)/8, $libc_getgid_trampoline<>(SB)

TEXT libc_getpgid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getpgid(SB)
GLOBL	·libc_getpgid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getpgid_trampoline_addr(SB)/8, $libc_getpgid_trampoline<>(SB)

TEXT libc_getpgrp_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getpgrp(SB)
GLOBL	·libc_getpgrp_trampoline_addr(SB), RODATA, $8
DATA	·libc_getpgrp_trampoline_addr(SB)/8, $libc_getpgrp_trampoline<>(SB)

TEXT libc_getpid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getpid(SB)
GLOBL	·libc_getpid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getpid_trampoline_addr(SB)/8, $libc_getpid_trampoline<>(SB)

TEXT libc_getppid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getppid(SB)
GLOBL	·libc_getppid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getppid_trampoline_addr(SB)/8, $libc_getppid_trampoline<>(SB)

TEXT libc_getpriority_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getpriority(SB)
GLOBL	·libc_getpriority_trampoline_addr(SB), RODATA, $8
DATA	·libc_getpriority_trampoline_addr(SB)/8, $libc_getpriority_trampoline<>(SB)

TEXT libc_getrlimit_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getrlimit(SB)
GLOBL	·libc_getrlimit_trampoline_addr(SB), RODATA, $8
DATA	·libc_getrlimit_trampoline_addr(SB)/8, $libc_getrlimit_trampoline<>(SB)

TEXT libc_getrtable_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getrtable(SB)
GLOBL	·libc_getrtable_trampoline_addr(SB), RODATA, $8
DATA	·libc_getrtable_trampoline_addr(SB)/8, $libc_getrtable_trampoline<>(SB)

TEXT libc_getrusage_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getrusage(SB)
GLOBL	·libc_getrusage_trampoline_addr(SB), RODATA, $8
DATA	·libc_getrusage_trampoline_addr(SB)/8, $libc_getrusage_trampoline<>(SB)

TEXT libc_getsid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getsid(SB)
GLOBL	·libc_getsid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getsid_trampoline_addr(SB)/8, $libc_getsid_trampoline<>(SB)

TEXT libc_gettimeofday_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_gettimeofday(SB)
GLOBL	·libc_gettimeofday_trampoline_addr(SB), RODATA, $8
DATA	·libc_gettimeofday_trampoline_addr(SB)/8, $libc_gettimeofday_trampoline<>(SB)

TEXT libc_getuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getuid(SB)
GLOBL	·libc_getuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_getuid_trampoline_addr(SB)/8, $libc_getuid_trampoline<>(SB)

TEXT libc_issetugid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_issetugid(SB)
GLOBL	·libc_issetugid_trampoline_addr(SB), RODATA, $8
DATA	·libc_issetugid_trampoline_addr(SB)/8, $libc_issetugid_trampoline<>(SB)

TEXT libc_kill_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_kill(SB)
GLOBL	·libc_kill_trampoline_addr(SB), RODATA, $8
DATA	·libc_kill_trampoline_addr(SB)/8, $libc_kill_trampoline<>(SB)

TEXT libc_kqueue_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_kqueue(SB)
GLOBL	·libc_kqueue_trampoline_addr(SB), RODATA, $8
DATA	·libc_kqueue_trampoline_addr(SB)/8, $libc_kqueue_trampoline<>(SB)

TEXT libc_lchown_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_lchown(SB)
GLOBL	·libc_lchown_trampoline_addr(SB), RODATA, $8
DATA	·libc_lchown_trampoline_addr(SB)/8, $libc_lchown_trampoline<>(SB)

TEXT libc_link_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_link(SB)
GLOBL	·libc_link_trampoline_addr(SB), RODATA, $8
DATA	·libc_link_trampoline_addr(SB)/8, $libc_link_trampoline<>(SB)

TEXT libc_linkat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_linkat(SB)
GLOBL	·libc_linkat_trampoline_addr(SB), RODATA, $8
DATA	·libc_linkat_trampoline_addr(SB)/8, $libc_linkat_trampoline<>(SB)

TEXT libc_listen_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_listen(SB)
GLOBL	·libc_listen_trampoline_addr(SB), RODATA, $8
DATA	·libc_listen_trampoline_addr(SB)/8, $libc_listen_trampoline<>(SB)

TEXT libc_lstat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_lstat(SB)
GLOBL	·libc_lstat_trampoline_addr(SB), RODATA, $8
DATA	·libc_lstat_trampoline_addr(SB)/8, $libc_lstat_trampoline<>(SB)

TEXT libc_mkdir_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mkdir(SB)
GLOBL	·libc_mkdir_trampoline_addr(SB), RODATA, $8
DATA	·libc_mkdir_trampoline_addr(SB)/8, $libc_mkdir_trampoline<>(SB)

TEXT libc_mkdirat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mkdirat(SB)
GLOBL	·libc_mkdirat_trampoline_addr(SB), RODATA, $8
DATA	·libc_mkdirat_trampoline_addr(SB)/8, $libc_mkdirat_trampoline<>(SB)

TEXT libc_mkfifo_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mkfifo(SB)
GLOBL	·libc_mkfifo_trampoline_addr(SB), RODATA, $8
DATA	·libc_mkfifo_trampoline_addr(SB)/8, $libc_mkfifo_trampoline<>(SB)

TEXT libc_mkfifoat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mkfifoat(SB)
GLOBL	·libc_mkfifoat_trampoline_addr(SB), RODATA, $8
DATA	·libc_mkfifoat_trampoline_addr(SB)/8, $libc_mkfifoat_trampoline<>(SB)

TEXT libc_mknod_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mknod(SB)
GLOBL	·libc_mknod_trampoline_addr(SB), RODATA, $8
DATA	·libc_mknod_trampoline_addr(SB)/8, $libc_mknod_trampoline<>(SB)

TEXT libc_mknodat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mknodat(SB)
GLOBL	·libc_mknodat_trampoline_addr(SB), RODATA, $8
DATA	·libc_mknodat_trampoline_addr(SB)/8, $libc_mknodat_trampoline<>(SB)

TEXT libc_mount_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mount(SB)
GLOBL	·libc_mount_trampoline_addr(SB), RODATA, $8
DATA	·libc_mount_trampoline_addr(SB)/8, $libc_mount_trampoline<>(SB)

TEXT libc_nanosleep_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_nanosleep(SB)
GLOBL	·libc_nanosleep_trampoline_addr(SB), RODATA, $8
DATA	·libc_nanosleep_trampoline_addr(SB)/8, $libc_nanosleep_trampoline<>(SB)

TEXT libc_open_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_open(SB)
GLOBL	·libc_open_trampoline_addr(SB), RODATA, $8
DATA	·libc_open_trampoline_addr(SB)/8, $libc_open_trampoline<>(SB)

TEXT libc_openat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_openat(SB)
GLOBL	·libc_openat_trampoline_addr(SB), RODATA, $8
DATA	·libc_openat_trampoline_addr(SB)/8, $libc_openat_trampoline<>(SB)

TEXT libc_pathconf_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_pathconf(SB)
GLOBL	·libc_pathconf_trampoline_addr(SB), RODATA, $8
DATA	·libc_pathconf_trampoline_addr(SB)/8, $libc_pathconf_trampoline<>(SB)

TEXT libc_pread_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_pread(SB)
GLOBL	·libc_pread_trampoline_addr(SB), RODATA, $8
DATA	·libc_pread_trampoline_addr(SB)/8, $libc_pread_trampoline<>(SB)

TEXT libc_pwrite_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_pwrite(SB)
GLOBL	·libc_pwrite_trampoline_addr(SB), RODATA, $8
DATA	·libc_pwrite_trampoline_addr(SB)/8, $libc_pwrite_trampoline<>(SB)

TEXT libc_read_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_read(SB)
GLOBL	·libc_read_trampoline_addr(SB), RODATA, $8
DATA	·libc_read_trampoline_addr(SB)/8, $libc_read_trampoline<>(SB)

TEXT libc_readlink_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_readlink(SB)
GLOBL	·libc_readlink_trampoline_addr(SB), RODATA, $8
DATA	·libc_readlink_trampoline_addr(SB)/8, $libc_readlink_trampoline<>(SB)

TEXT libc_readlinkat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_readlinkat(SB)
GLOBL	·libc_readlinkat_trampoline_addr(SB), RODATA, $8
DATA	·libc_readlinkat_trampoline_addr(SB)/8, $libc_readlinkat_trampoline<>(SB)

TEXT libc_rename_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_rename(SB)
GLOBL	·libc_rename_trampoline_addr(SB), RODATA, $8
DATA	·libc_rename_trampoline_addr(SB)/8, $libc_rename_trampoline<>(SB)

TEXT libc_renameat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_renameat(SB)
GLOBL	·libc_renameat_trampoline_addr(SB), RODATA, $8
DATA	·libc_renameat_trampoline_addr(SB)/8, $libc_renameat_trampoline<>(SB)

TEXT libc_revoke_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_revoke(SB)
GLOBL	·libc_revoke_trampoline_addr(SB), RODATA, $8
DATA	·libc_revoke_trampoline_addr(SB)/8, $libc_revoke_trampoline<>(SB)

TEXT libc_rmdir_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_rmdir(SB)
GLOBL	·libc_rmdir_trampoline_addr(SB), RODATA, $8
DATA	·libc_rmdir_trampoline_addr(SB)/8, $libc_rmdir_trampoline<>(SB)

TEXT libc_lseek_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_lseek(SB)
GLOBL	·libc_lseek_trampoline_addr(SB), RODATA, $8
DATA	·libc_lseek_trampoline_addr(SB)/8, $libc_lseek_trampoline<>(SB)

TEXT libc_select_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_select(SB)
GLOBL	·libc_select_trampoline_addr(SB), RODATA, $8
DATA	·libc_select_trampoline_addr(SB)/8, $libc_select_trampoline<>(SB)

TEXT libc_setegid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setegid(SB)
GLOBL	·libc_setegid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setegid_trampoline_addr(SB)/8, $libc_setegid_trampoline<>(SB)

TEXT libc_seteuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_seteuid(SB)
GLOBL	·libc_seteuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_seteuid_trampoline_addr(SB)/8, $libc_seteuid_trampoline<>(SB)

TEXT libc_setgid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setgid(SB)
GLOBL	·libc_setgid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setgid_trampoline_addr(SB)/8, $libc_setgid_trampoline<>(SB)

TEXT libc_setlogin_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setlogin(SB)
GLOBL	·libc_setlogin_trampoline_addr(SB), RODATA, $8
DATA	·libc_setlogin_trampoline_addr(SB)/8, $libc_setlogin_trampoline<>(SB)

TEXT libc_setpgid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setpgid(SB)
GLOBL	·libc_setpgid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setpgid_trampoline_addr(SB)/8, $libc_setpgid_trampoline<>(SB)

TEXT libc_setpriority_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setpriority(SB)
GLOBL	·libc_setpriority_trampoline_addr(SB), RODATA, $8
DATA	·libc_setpriority_trampoline_addr(SB)/8, $libc_setpriority_trampoline<>(SB)

TEXT libc_setregid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setregid(SB)
GLOBL	·libc_setregid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setregid_trampoline_addr(SB)/8, $libc_setregid_trampoline<>(SB)

TEXT libc_setreuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setreuid(SB)
GLOBL	·libc_setreuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setreuid_trampoline_addr(SB)/8, $libc_setreuid_trampoline<>(SB)

TEXT libc_setresgid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setresgid(SB)
GLOBL	·libc_setresgid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setresgid_trampoline_addr(SB)/8, $libc_setresgid_trampoline<>(SB)

TEXT libc_setresuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setresuid(SB)
GLOBL	·libc_setresuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setresuid_trampoline_addr(SB)/8, $libc_setresuid_trampoline<>(SB)

TEXT libc_setrtable_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setrtable(SB)
GLOBL	·libc_setrtable_trampoline_addr(SB), RODATA, $8
DATA	·libc_setrtable_trampoline_addr(SB)/8, $libc_setrtable_trampoline<>(SB)

TEXT libc_setsid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setsid(SB)
GLOBL	·libc_setsid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setsid_trampoline_addr(SB)/8, $libc_setsid_trampoline<>(SB)

TEXT libc_settimeofday_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_settimeofday(SB)
GLOBL	·libc_settimeofday_trampoline_addr(SB), RODATA, $8
DATA	·libc_settimeofday_trampoline_addr(SB)/8, $libc_settimeofday_trampoline<>(SB)

TEXT libc_setuid_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_setuid(SB)
GLOBL	·libc_setuid_trampoline_addr(SB), RODATA, $8
DATA	·libc_setuid_trampoline_addr(SB)/8, $libc_setuid_trampoline<>(SB)

TEXT libc_stat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_stat(SB)
GLOBL	·libc_stat_trampoline_addr(SB), RODATA, $8
DATA	·libc_stat_trampoline_addr(SB)/8, $libc_stat_trampoline<>(SB)

TEXT libc_statfs_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_statfs(SB)
GLOBL	·libc_statfs_trampoline_addr(SB), RODATA, $8
DATA	·libc_statfs_trampoline_addr(SB)/8, $libc_statfs_trampoline<>(SB)

TEXT libc_symlink_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_symlink(SB)
GLOBL	·libc_symlink_trampoline_addr(SB), RODATA, $8
DATA	·libc_symlink_trampoline_addr(SB)/8, $libc_symlink_trampoline<>(SB)

TEXT libc_symlinkat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_symlinkat(SB)
GLOBL	·libc_symlinkat_trampoline_addr(SB), RODATA, $8
DATA	·libc_symlinkat_trampoline_addr(SB)/8, $libc_symlinkat_trampoline<>(SB)

TEXT libc_sync_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_sync(SB)
GLOBL	·libc_sync_trampoline_addr(SB), RODATA, $8
DATA	·libc_sync_trampoline_addr(SB)/8, $libc_sync_trampoline<>(SB)

TEXT libc_truncate_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_truncate(SB)
GLOBL	·libc_truncate_trampoline_addr(SB), RODATA, $8
DATA	·libc_truncate_trampoline_addr(SB)/8, $libc_truncate_trampoline<>(SB)

TEXT libc_umask_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_umask(SB)
GLOBL	·libc_umask_trampoline_addr(SB), RODATA, $8
DATA	·libc_umask_trampoline_addr(SB)/8, $libc_umask_trampoline<>(SB)

TEXT libc_unlink_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_unlink(SB)
GLOBL	·libc_unlink_trampoline_addr(SB), RODATA, $8
DATA	·libc_unlink_trampoline_addr(SB)/8, $libc_unlink_trampoline<>(SB)

TEXT libc_unlinkat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_unlinkat(SB)
GLOBL	·libc_unlinkat_trampoline_addr(SB), RODATA, $8
DATA	·libc_unlinkat_trampoline_addr(SB)/8, $libc_unlinkat_trampoline<>(SB)

TEXT libc_unmount_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_unmount(SB)
GLOBL	·libc_unmount_trampoline_addr(SB), RODATA, $8
DATA	·libc_unmount_trampoline_addr(SB)/8, $libc_unmount_trampoline<>(SB)

TEXT libc_write_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_write(SB)
GLOBL	·libc_write_trampoline_addr(SB), RODATA, $8
DATA	·libc_write_trampoline_addr(SB)/8, $libc_write_trampoline<>(SB)

TEXT libc_mmap_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_mmap(SB)
GLOBL	·libc_mmap_trampoline_addr(SB), RODATA, $8
DATA	·libc_mmap_trampoline_addr(SB)/8, $libc_mmap_trampoline<>(SB)

TEXT libc_munmap_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_munmap(SB)
GLOBL	·libc_munmap_trampoline_addr(SB), RODATA, $8
DATA	·libc_munmap_trampoline_addr(SB)/8, $libc_munmap_trampoline<>(SB)

TEXT libc_getfsstat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_getfsstat(SB)
GLOBL	·libc_getfsstat_trampoline_addr(SB), RODATA, $8
DATA	·libc_getfsstat_trampoline_addr(SB)/8, $libc_getfsstat_trampoline<>(SB)

TEXT libc_utimensat_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_utimensat(SB)
GLOBL	·libc_utimensat_trampoline_addr(SB), RODATA, $8
DATA	·libc_utimensat_trampoline_addr(SB)/8, $libc_utimensat_trampoline<>(SB)

TEXT libc_pledge_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_pledge(SB)
GLOBL	·libc_pledge_trampoline_addr(SB), RODATA, $8
DATA	·libc_pledge_trampoline_addr(SB)/8, $libc_pledge_trampoline<>(SB)

TEXT libc_unveil_trampoline<>(SB),NOSPLIT,$0-0
	JMP	libc_unveil(SB)
GLOBL	·libc_unveil_trampoline_addr(SB), RODATA, $8
DATA	·libc_unveil_trampoline_addr(SB)/8, $libc_unveil_trampoline<>(SB)
