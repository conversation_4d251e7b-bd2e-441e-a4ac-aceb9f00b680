// Copyright 2016 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Service exported by server reflection.  A more complete description of how
// server reflection works can be found at
// https://github.com/grpc/grpc/blob/master/doc/server-reflection.md
//
// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/reflection/v1/reflection.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/reflection/v1/reflection.proto

package grpc_reflection_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The message sent by the client when calling ServerReflectionInfo method.
type ServerReflectionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Host  string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	// To use reflection service, the client should set one of the following
	// fields in message_request. The server distinguishes requests by their
	// defined field and then handles them using corresponding methods.
	//
	// Types that are valid to be assigned to MessageRequest:
	//
	//	*ServerReflectionRequest_FileByFilename
	//	*ServerReflectionRequest_FileContainingSymbol
	//	*ServerReflectionRequest_FileContainingExtension
	//	*ServerReflectionRequest_AllExtensionNumbersOfType
	//	*ServerReflectionRequest_ListServices
	MessageRequest isServerReflectionRequest_MessageRequest `protobuf_oneof:"message_request"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ServerReflectionRequest) Reset() {
	*x = ServerReflectionRequest{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerReflectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerReflectionRequest) ProtoMessage() {}

func (x *ServerReflectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerReflectionRequest.ProtoReflect.Descriptor instead.
func (*ServerReflectionRequest) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{0}
}

func (x *ServerReflectionRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ServerReflectionRequest) GetMessageRequest() isServerReflectionRequest_MessageRequest {
	if x != nil {
		return x.MessageRequest
	}
	return nil
}

func (x *ServerReflectionRequest) GetFileByFilename() string {
	if x != nil {
		if x, ok := x.MessageRequest.(*ServerReflectionRequest_FileByFilename); ok {
			return x.FileByFilename
		}
	}
	return ""
}

func (x *ServerReflectionRequest) GetFileContainingSymbol() string {
	if x != nil {
		if x, ok := x.MessageRequest.(*ServerReflectionRequest_FileContainingSymbol); ok {
			return x.FileContainingSymbol
		}
	}
	return ""
}

func (x *ServerReflectionRequest) GetFileContainingExtension() *ExtensionRequest {
	if x != nil {
		if x, ok := x.MessageRequest.(*ServerReflectionRequest_FileContainingExtension); ok {
			return x.FileContainingExtension
		}
	}
	return nil
}

func (x *ServerReflectionRequest) GetAllExtensionNumbersOfType() string {
	if x != nil {
		if x, ok := x.MessageRequest.(*ServerReflectionRequest_AllExtensionNumbersOfType); ok {
			return x.AllExtensionNumbersOfType
		}
	}
	return ""
}

func (x *ServerReflectionRequest) GetListServices() string {
	if x != nil {
		if x, ok := x.MessageRequest.(*ServerReflectionRequest_ListServices); ok {
			return x.ListServices
		}
	}
	return ""
}

type isServerReflectionRequest_MessageRequest interface {
	isServerReflectionRequest_MessageRequest()
}

type ServerReflectionRequest_FileByFilename struct {
	// Find a proto file by the file name.
	FileByFilename string `protobuf:"bytes,3,opt,name=file_by_filename,json=fileByFilename,proto3,oneof"`
}

type ServerReflectionRequest_FileContainingSymbol struct {
	// Find the proto file that declares the given fully-qualified symbol name.
	// This field should be a fully-qualified symbol name
	// (e.g. <package>.<service>[.<method>] or <package>.<type>).
	FileContainingSymbol string `protobuf:"bytes,4,opt,name=file_containing_symbol,json=fileContainingSymbol,proto3,oneof"`
}

type ServerReflectionRequest_FileContainingExtension struct {
	// Find the proto file which defines an extension extending the given
	// message type with the given field number.
	FileContainingExtension *ExtensionRequest `protobuf:"bytes,5,opt,name=file_containing_extension,json=fileContainingExtension,proto3,oneof"`
}

type ServerReflectionRequest_AllExtensionNumbersOfType struct {
	// Finds the tag numbers used by all known extensions of the given message
	// type, and appends them to ExtensionNumberResponse in an undefined order.
	// Its corresponding method is best-effort: it's not guaranteed that the
	// reflection service will implement this method, and it's not guaranteed
	// that this method will provide all extensions. Returns
	// StatusCode::UNIMPLEMENTED if it's not implemented.
	// This field should be a fully-qualified type name. The format is
	// <package>.<type>
	AllExtensionNumbersOfType string `protobuf:"bytes,6,opt,name=all_extension_numbers_of_type,json=allExtensionNumbersOfType,proto3,oneof"`
}

type ServerReflectionRequest_ListServices struct {
	// List the full names of registered services. The content will not be
	// checked.
	ListServices string `protobuf:"bytes,7,opt,name=list_services,json=listServices,proto3,oneof"`
}

func (*ServerReflectionRequest_FileByFilename) isServerReflectionRequest_MessageRequest() {}

func (*ServerReflectionRequest_FileContainingSymbol) isServerReflectionRequest_MessageRequest() {}

func (*ServerReflectionRequest_FileContainingExtension) isServerReflectionRequest_MessageRequest() {}

func (*ServerReflectionRequest_AllExtensionNumbersOfType) isServerReflectionRequest_MessageRequest() {
}

func (*ServerReflectionRequest_ListServices) isServerReflectionRequest_MessageRequest() {}

// The type name and extension number sent by the client when requesting
// file_containing_extension.
type ExtensionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Fully-qualified type name. The format should be <package>.<type>
	ContainingType  string `protobuf:"bytes,1,opt,name=containing_type,json=containingType,proto3" json:"containing_type,omitempty"`
	ExtensionNumber int32  `protobuf:"varint,2,opt,name=extension_number,json=extensionNumber,proto3" json:"extension_number,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ExtensionRequest) Reset() {
	*x = ExtensionRequest{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtensionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionRequest) ProtoMessage() {}

func (x *ExtensionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionRequest.ProtoReflect.Descriptor instead.
func (*ExtensionRequest) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{1}
}

func (x *ExtensionRequest) GetContainingType() string {
	if x != nil {
		return x.ContainingType
	}
	return ""
}

func (x *ExtensionRequest) GetExtensionNumber() int32 {
	if x != nil {
		return x.ExtensionNumber
	}
	return 0
}

// The message sent by the server to answer ServerReflectionInfo method.
type ServerReflectionResponse struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	ValidHost       string                   `protobuf:"bytes,1,opt,name=valid_host,json=validHost,proto3" json:"valid_host,omitempty"`
	OriginalRequest *ServerReflectionRequest `protobuf:"bytes,2,opt,name=original_request,json=originalRequest,proto3" json:"original_request,omitempty"`
	// The server sets one of the following fields according to the message_request
	// in the request.
	//
	// Types that are valid to be assigned to MessageResponse:
	//
	//	*ServerReflectionResponse_FileDescriptorResponse
	//	*ServerReflectionResponse_AllExtensionNumbersResponse
	//	*ServerReflectionResponse_ListServicesResponse
	//	*ServerReflectionResponse_ErrorResponse
	MessageResponse isServerReflectionResponse_MessageResponse `protobuf_oneof:"message_response"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServerReflectionResponse) Reset() {
	*x = ServerReflectionResponse{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerReflectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerReflectionResponse) ProtoMessage() {}

func (x *ServerReflectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerReflectionResponse.ProtoReflect.Descriptor instead.
func (*ServerReflectionResponse) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{2}
}

func (x *ServerReflectionResponse) GetValidHost() string {
	if x != nil {
		return x.ValidHost
	}
	return ""
}

func (x *ServerReflectionResponse) GetOriginalRequest() *ServerReflectionRequest {
	if x != nil {
		return x.OriginalRequest
	}
	return nil
}

func (x *ServerReflectionResponse) GetMessageResponse() isServerReflectionResponse_MessageResponse {
	if x != nil {
		return x.MessageResponse
	}
	return nil
}

func (x *ServerReflectionResponse) GetFileDescriptorResponse() *FileDescriptorResponse {
	if x != nil {
		if x, ok := x.MessageResponse.(*ServerReflectionResponse_FileDescriptorResponse); ok {
			return x.FileDescriptorResponse
		}
	}
	return nil
}

func (x *ServerReflectionResponse) GetAllExtensionNumbersResponse() *ExtensionNumberResponse {
	if x != nil {
		if x, ok := x.MessageResponse.(*ServerReflectionResponse_AllExtensionNumbersResponse); ok {
			return x.AllExtensionNumbersResponse
		}
	}
	return nil
}

func (x *ServerReflectionResponse) GetListServicesResponse() *ListServiceResponse {
	if x != nil {
		if x, ok := x.MessageResponse.(*ServerReflectionResponse_ListServicesResponse); ok {
			return x.ListServicesResponse
		}
	}
	return nil
}

func (x *ServerReflectionResponse) GetErrorResponse() *ErrorResponse {
	if x != nil {
		if x, ok := x.MessageResponse.(*ServerReflectionResponse_ErrorResponse); ok {
			return x.ErrorResponse
		}
	}
	return nil
}

type isServerReflectionResponse_MessageResponse interface {
	isServerReflectionResponse_MessageResponse()
}

type ServerReflectionResponse_FileDescriptorResponse struct {
	// This message is used to answer file_by_filename, file_containing_symbol,
	// file_containing_extension requests with transitive dependencies.
	// As the repeated label is not allowed in oneof fields, we use a
	// FileDescriptorResponse message to encapsulate the repeated fields.
	// The reflection service is allowed to avoid sending FileDescriptorProtos
	// that were previously sent in response to earlier requests in the stream.
	FileDescriptorResponse *FileDescriptorResponse `protobuf:"bytes,4,opt,name=file_descriptor_response,json=fileDescriptorResponse,proto3,oneof"`
}

type ServerReflectionResponse_AllExtensionNumbersResponse struct {
	// This message is used to answer all_extension_numbers_of_type requests.
	AllExtensionNumbersResponse *ExtensionNumberResponse `protobuf:"bytes,5,opt,name=all_extension_numbers_response,json=allExtensionNumbersResponse,proto3,oneof"`
}

type ServerReflectionResponse_ListServicesResponse struct {
	// This message is used to answer list_services requests.
	ListServicesResponse *ListServiceResponse `protobuf:"bytes,6,opt,name=list_services_response,json=listServicesResponse,proto3,oneof"`
}

type ServerReflectionResponse_ErrorResponse struct {
	// This message is used when an error occurs.
	ErrorResponse *ErrorResponse `protobuf:"bytes,7,opt,name=error_response,json=errorResponse,proto3,oneof"`
}

func (*ServerReflectionResponse_FileDescriptorResponse) isServerReflectionResponse_MessageResponse() {
}

func (*ServerReflectionResponse_AllExtensionNumbersResponse) isServerReflectionResponse_MessageResponse() {
}

func (*ServerReflectionResponse_ListServicesResponse) isServerReflectionResponse_MessageResponse() {}

func (*ServerReflectionResponse_ErrorResponse) isServerReflectionResponse_MessageResponse() {}

// Serialized FileDescriptorProto messages sent by the server answering
// a file_by_filename, file_containing_symbol, or file_containing_extension
// request.
type FileDescriptorResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Serialized FileDescriptorProto messages. We avoid taking a dependency on
	// descriptor.proto, which uses proto2 only features, by making them opaque
	// bytes instead.
	FileDescriptorProto [][]byte `protobuf:"bytes,1,rep,name=file_descriptor_proto,json=fileDescriptorProto,proto3" json:"file_descriptor_proto,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *FileDescriptorResponse) Reset() {
	*x = FileDescriptorResponse{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDescriptorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDescriptorResponse) ProtoMessage() {}

func (x *FileDescriptorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDescriptorResponse.ProtoReflect.Descriptor instead.
func (*FileDescriptorResponse) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{3}
}

func (x *FileDescriptorResponse) GetFileDescriptorProto() [][]byte {
	if x != nil {
		return x.FileDescriptorProto
	}
	return nil
}

// A list of extension numbers sent by the server answering
// all_extension_numbers_of_type request.
type ExtensionNumberResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Full name of the base type, including the package name. The format
	// is <package>.<type>
	BaseTypeName    string  `protobuf:"bytes,1,opt,name=base_type_name,json=baseTypeName,proto3" json:"base_type_name,omitempty"`
	ExtensionNumber []int32 `protobuf:"varint,2,rep,packed,name=extension_number,json=extensionNumber,proto3" json:"extension_number,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ExtensionNumberResponse) Reset() {
	*x = ExtensionNumberResponse{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtensionNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionNumberResponse) ProtoMessage() {}

func (x *ExtensionNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionNumberResponse.ProtoReflect.Descriptor instead.
func (*ExtensionNumberResponse) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{4}
}

func (x *ExtensionNumberResponse) GetBaseTypeName() string {
	if x != nil {
		return x.BaseTypeName
	}
	return ""
}

func (x *ExtensionNumberResponse) GetExtensionNumber() []int32 {
	if x != nil {
		return x.ExtensionNumber
	}
	return nil
}

// A list of ServiceResponse sent by the server answering list_services request.
type ListServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The information of each service may be expanded in the future, so we use
	// ServiceResponse message to encapsulate it.
	Service       []*ServiceResponse `protobuf:"bytes,1,rep,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceResponse) Reset() {
	*x = ListServiceResponse{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceResponse) ProtoMessage() {}

func (x *ListServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceResponse.ProtoReflect.Descriptor instead.
func (*ListServiceResponse) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{5}
}

func (x *ListServiceResponse) GetService() []*ServiceResponse {
	if x != nil {
		return x.Service
	}
	return nil
}

// The information of a single service used by ListServiceResponse to answer
// list_services request.
type ServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Full name of a registered service, including its package name. The format
	// is <package>.<service>
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceResponse) Reset() {
	*x = ServiceResponse{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceResponse) ProtoMessage() {}

func (x *ServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceResponse.ProtoReflect.Descriptor instead.
func (*ServiceResponse) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The error code and error message sent by the server when an error occurs.
type ErrorResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This field uses the error codes defined in grpc::StatusCode.
	ErrorCode     int32  `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage  string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_reflection_v1_reflection_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_grpc_reflection_v1_reflection_proto_rawDescGZIP(), []int{7}
}

func (x *ErrorResponse) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *ErrorResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_grpc_reflection_v1_reflection_proto protoreflect.FileDescriptor

const file_grpc_reflection_v1_reflection_proto_rawDesc = "" +
	"\n" +
	"#grpc/reflection/v1/reflection.proto\x12\x12grpc.reflection.v1\"\xf3\x02\n" +
	"\x17ServerReflectionRequest\x12\x12\n" +
	"\x04host\x18\x01 \x01(\tR\x04host\x12*\n" +
	"\x10file_by_filename\x18\x03 \x01(\tH\x00R\x0efileByFilename\x126\n" +
	"\x16file_containing_symbol\x18\x04 \x01(\tH\x00R\x14fileContainingSymbol\x12b\n" +
	"\x19file_containing_extension\x18\x05 \x01(\v2$.grpc.reflection.v1.ExtensionRequestH\x00R\x17fileContainingExtension\x12B\n" +
	"\x1dall_extension_numbers_of_type\x18\x06 \x01(\tH\x00R\x19allExtensionNumbersOfType\x12%\n" +
	"\rlist_services\x18\a \x01(\tH\x00R\flistServicesB\x11\n" +
	"\x0fmessage_request\"f\n" +
	"\x10ExtensionRequest\x12'\n" +
	"\x0fcontaining_type\x18\x01 \x01(\tR\x0econtainingType\x12)\n" +
	"\x10extension_number\x18\x02 \x01(\x05R\x0fextensionNumber\"\xae\x04\n" +
	"\x18ServerReflectionResponse\x12\x1d\n" +
	"\n" +
	"valid_host\x18\x01 \x01(\tR\tvalidHost\x12V\n" +
	"\x10original_request\x18\x02 \x01(\v2+.grpc.reflection.v1.ServerReflectionRequestR\x0foriginalRequest\x12f\n" +
	"\x18file_descriptor_response\x18\x04 \x01(\v2*.grpc.reflection.v1.FileDescriptorResponseH\x00R\x16fileDescriptorResponse\x12r\n" +
	"\x1eall_extension_numbers_response\x18\x05 \x01(\v2+.grpc.reflection.v1.ExtensionNumberResponseH\x00R\x1ballExtensionNumbersResponse\x12_\n" +
	"\x16list_services_response\x18\x06 \x01(\v2'.grpc.reflection.v1.ListServiceResponseH\x00R\x14listServicesResponse\x12J\n" +
	"\x0eerror_response\x18\a \x01(\v2!.grpc.reflection.v1.ErrorResponseH\x00R\rerrorResponseB\x12\n" +
	"\x10message_response\"L\n" +
	"\x16FileDescriptorResponse\x122\n" +
	"\x15file_descriptor_proto\x18\x01 \x03(\fR\x13fileDescriptorProto\"j\n" +
	"\x17ExtensionNumberResponse\x12$\n" +
	"\x0ebase_type_name\x18\x01 \x01(\tR\fbaseTypeName\x12)\n" +
	"\x10extension_number\x18\x02 \x03(\x05R\x0fextensionNumber\"T\n" +
	"\x13ListServiceResponse\x12=\n" +
	"\aservice\x18\x01 \x03(\v2#.grpc.reflection.v1.ServiceResponseR\aservice\"%\n" +
	"\x0fServiceResponse\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"S\n" +
	"\rErrorResponse\x12\x1d\n" +
	"\n" +
	"error_code\x18\x01 \x01(\x05R\terrorCode\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage2\x89\x01\n" +
	"\x10ServerReflection\x12u\n" +
	"\x14ServerReflectionInfo\x12+.grpc.reflection.v1.ServerReflectionRequest\x1a,.grpc.reflection.v1.ServerReflectionResponse(\x010\x01Bf\n" +
	"\x15io.grpc.reflection.v1B\x15ServerReflectionProtoP\x01Z4google.golang.org/grpc/reflection/grpc_reflection_v1b\x06proto3"

var (
	file_grpc_reflection_v1_reflection_proto_rawDescOnce sync.Once
	file_grpc_reflection_v1_reflection_proto_rawDescData []byte
)

func file_grpc_reflection_v1_reflection_proto_rawDescGZIP() []byte {
	file_grpc_reflection_v1_reflection_proto_rawDescOnce.Do(func() {
		file_grpc_reflection_v1_reflection_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_reflection_v1_reflection_proto_rawDesc), len(file_grpc_reflection_v1_reflection_proto_rawDesc)))
	})
	return file_grpc_reflection_v1_reflection_proto_rawDescData
}

var file_grpc_reflection_v1_reflection_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_grpc_reflection_v1_reflection_proto_goTypes = []any{
	(*ServerReflectionRequest)(nil),  // 0: grpc.reflection.v1.ServerReflectionRequest
	(*ExtensionRequest)(nil),         // 1: grpc.reflection.v1.ExtensionRequest
	(*ServerReflectionResponse)(nil), // 2: grpc.reflection.v1.ServerReflectionResponse
	(*FileDescriptorResponse)(nil),   // 3: grpc.reflection.v1.FileDescriptorResponse
	(*ExtensionNumberResponse)(nil),  // 4: grpc.reflection.v1.ExtensionNumberResponse
	(*ListServiceResponse)(nil),      // 5: grpc.reflection.v1.ListServiceResponse
	(*ServiceResponse)(nil),          // 6: grpc.reflection.v1.ServiceResponse
	(*ErrorResponse)(nil),            // 7: grpc.reflection.v1.ErrorResponse
}
var file_grpc_reflection_v1_reflection_proto_depIdxs = []int32{
	1, // 0: grpc.reflection.v1.ServerReflectionRequest.file_containing_extension:type_name -> grpc.reflection.v1.ExtensionRequest
	0, // 1: grpc.reflection.v1.ServerReflectionResponse.original_request:type_name -> grpc.reflection.v1.ServerReflectionRequest
	3, // 2: grpc.reflection.v1.ServerReflectionResponse.file_descriptor_response:type_name -> grpc.reflection.v1.FileDescriptorResponse
	4, // 3: grpc.reflection.v1.ServerReflectionResponse.all_extension_numbers_response:type_name -> grpc.reflection.v1.ExtensionNumberResponse
	5, // 4: grpc.reflection.v1.ServerReflectionResponse.list_services_response:type_name -> grpc.reflection.v1.ListServiceResponse
	7, // 5: grpc.reflection.v1.ServerReflectionResponse.error_response:type_name -> grpc.reflection.v1.ErrorResponse
	6, // 6: grpc.reflection.v1.ListServiceResponse.service:type_name -> grpc.reflection.v1.ServiceResponse
	0, // 7: grpc.reflection.v1.ServerReflection.ServerReflectionInfo:input_type -> grpc.reflection.v1.ServerReflectionRequest
	2, // 8: grpc.reflection.v1.ServerReflection.ServerReflectionInfo:output_type -> grpc.reflection.v1.ServerReflectionResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_grpc_reflection_v1_reflection_proto_init() }
func file_grpc_reflection_v1_reflection_proto_init() {
	if File_grpc_reflection_v1_reflection_proto != nil {
		return
	}
	file_grpc_reflection_v1_reflection_proto_msgTypes[0].OneofWrappers = []any{
		(*ServerReflectionRequest_FileByFilename)(nil),
		(*ServerReflectionRequest_FileContainingSymbol)(nil),
		(*ServerReflectionRequest_FileContainingExtension)(nil),
		(*ServerReflectionRequest_AllExtensionNumbersOfType)(nil),
		(*ServerReflectionRequest_ListServices)(nil),
	}
	file_grpc_reflection_v1_reflection_proto_msgTypes[2].OneofWrappers = []any{
		(*ServerReflectionResponse_FileDescriptorResponse)(nil),
		(*ServerReflectionResponse_AllExtensionNumbersResponse)(nil),
		(*ServerReflectionResponse_ListServicesResponse)(nil),
		(*ServerReflectionResponse_ErrorResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_reflection_v1_reflection_proto_rawDesc), len(file_grpc_reflection_v1_reflection_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_reflection_v1_reflection_proto_goTypes,
		DependencyIndexes: file_grpc_reflection_v1_reflection_proto_depIdxs,
		MessageInfos:      file_grpc_reflection_v1_reflection_proto_msgTypes,
	}.Build()
	File_grpc_reflection_v1_reflection_proto = out.File
	file_grpc_reflection_v1_reflection_proto_goTypes = nil
	file_grpc_reflection_v1_reflection_proto_depIdxs = nil
}
