// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

//go:build go1.21

package cases

// UnicodeVersion is the Unicode version from which the tables in this package are derived.
const UnicodeVersion = "15.0.0"

var xorData string = "" + // Size: 213 bytes
	"\x00\x06\x07\x00\x01?\x00\x0f\x03\x00\x0f\x12\x00\x0f\x1f\x00\x0f\x1d" +
	"\x00\x01\x13\x00\x0f\x16\x00\x0f\x0b\x00\x0f3\x00\x0f7\x00\x01#\x00\x0f?" +
	"\x00\x0e'\x00\x0f/\x00\x0e>\x00\x0f*\x00\x0c&\x00\x0c*\x00\x0c;\x00\x0c9" +
	"\x00\x0c%\x00\x01\x08\x00\x03\x0d\x00\x03\x09\x00\x02\x06\x00\x02\x02" +
	"\x00\x02\x0c\x00\x01\x00\x00\x01\x03\x00\x01\x01\x00\x01 \x00\x01\x0c" +
	"\x00\x01\x10\x00\x03\x10\x00\x036 \x00\x037 \x00\x0b#\x10\x00\x0b 0\x00" +
	"\x0b!\x10\x00\x0b!0\x001\x00\x00\x0b(\x04\x00\x03\x04\x1e\x00\x0b)\x08" +
	"\x00\x03\x0a\x00\x02:\x00\x02>\x00\x02,\x00\x02\x00\x00\x02\x10\x00\x01<" +
	"\x00\x01&\x00\x01*\x00\x01.\x00\x010\x003 \x00\x01\x18\x00\x01(\x00\x03'" +
	"\x00\x03)\x00\x03+\x00\x03/\x00\x03\x19\x00\x03\x1b\x00\x03\x1f\x00\x01" +
	"\x1e\x00\x01\x22"

var exceptions string = "" + // Size: 2450 bytes
	"\x00\x12\x12μΜΜ\x12\x12ssSSSs\x13\x18i̇i̇\x10\x09II\x13\x1bʼnʼNʼN\x11" +
	"\x09sSS\x12\x12ǆǆǅ\x12\x12ǆǆǄ\x10\x12Ǆǅ\x12\x12ǉǉǈ\x12\x12ǉǉǇ\x10\x12Ǉǈ" +
	"\x12\x12ǌǌǋ\x12\x12ǌǌǊ\x10\x12Ǌǋ\x13\x1bǰJ̌J̌\x12\x12ǳǳǲ\x12\x12ǳǳǱ\x10" +
	"\x12Ǳǲ\x13\x18ⱥⱥ\x13\x18ⱦⱦ\x10\x1bⱾⱾ\x10\x1bⱿⱿ\x10\x1bⱯⱯ\x10\x1bⱭⱭ\x10" +
	"\x1bⱰⱰ\x10\x1bꞫꞫ\x10\x1bꞬꞬ\x10\x1bꞍꞍ\x10\x1bꞪꞪ\x10\x1bꞮꞮ\x10\x1bⱢⱢ\x10" +
	"\x1bꞭꞭ\x10\x1bⱮⱮ\x10\x1bⱤⱤ\x10\x1bꟅꟅ\x10\x1bꞱꞱ\x10\x1bꞲꞲ\x10\x1bꞰꞰ2\x12ι" +
	"ΙΙ\x166ΐΪ́Ϊ́\x166ΰΫ́Ϋ́\x12\x12σΣΣ\x12\x12βΒΒ\x12\x12θΘΘ\x12\x12" +
	"φΦΦ\x12\x12πΠΠ\x12\x12κΚΚ\x12\x12ρΡΡ\x12\x12εΕΕ\x14$եւԵՒԵւ\x10\x1bᲐა" +
	"\x10\x1bᲑბ\x10\x1bᲒგ\x10\x1bᲓდ\x10\x1bᲔე\x10\x1bᲕვ\x10\x1bᲖზ\x10\x1bᲗთ" +
	"\x10\x1bᲘი\x10\x1bᲙკ\x10\x1bᲚლ\x10\x1bᲛმ\x10\x1bᲜნ\x10\x1bᲝო\x10\x1bᲞპ" +
	"\x10\x1bᲟჟ\x10\x1bᲠრ\x10\x1bᲡს\x10\x1bᲢტ\x10\x1bᲣუ\x10\x1bᲤფ\x10\x1bᲥქ" +
	"\x10\x1bᲦღ\x10\x1bᲧყ\x10\x1bᲨშ\x10\x1bᲩჩ\x10\x1bᲪც\x10\x1bᲫძ\x10\x1bᲬწ" +
	"\x10\x1bᲭჭ\x10\x1bᲮხ\x10\x1bᲯჯ\x10\x1bᲰჰ\x10\x1bᲱჱ\x10\x1bᲲჲ\x10\x1bᲳჳ" +
	"\x10\x1bᲴჴ\x10\x1bᲵჵ\x10\x1bᲶჶ\x10\x1bᲷჷ\x10\x1bᲸჸ\x10\x1bᲹჹ\x10\x1bᲺჺ" +
	"\x10\x1bᲽჽ\x10\x1bᲾჾ\x10\x1bᲿჿ\x12\x12вВВ\x12\x12дДД\x12\x12оОО\x12\x12с" +
	"СС\x12\x12тТТ\x12\x12тТТ\x12\x12ъЪЪ\x12\x12ѣѢѢ\x13\x1bꙋꙊꙊ\x13\x1bẖH̱H̱" +
	"\x13\x1bẗT̈T̈\x13\x1bẘW̊W̊\x13\x1bẙY̊Y̊\x13\x1baʾAʾAʾ\x13\x1bṡṠṠ\x12" +
	"\x10ssß\x14$ὐΥ̓Υ̓\x166ὒΥ̓̀Υ̓̀\x166ὔΥ̓́Υ̓́\x166ὖΥ̓͂Υ̓͂\x15+ἀιἈΙᾈ" +
	"\x15+ἁιἉΙᾉ\x15+ἂιἊΙᾊ\x15+ἃιἋΙᾋ\x15+ἄιἌΙᾌ\x15+ἅιἍΙᾍ\x15+ἆιἎΙᾎ\x15+ἇιἏΙᾏ" +
	"\x15\x1dἀιᾀἈΙ\x15\x1dἁιᾁἉΙ\x15\x1dἂιᾂἊΙ\x15\x1dἃιᾃἋΙ\x15\x1dἄιᾄἌΙ\x15" +
	"\x1dἅιᾅἍΙ\x15\x1dἆιᾆἎΙ\x15\x1dἇιᾇἏΙ\x15+ἠιἨΙᾘ\x15+ἡιἩΙᾙ\x15+ἢιἪΙᾚ\x15+ἣι" +
	"ἫΙᾛ\x15+ἤιἬΙᾜ\x15+ἥιἭΙᾝ\x15+ἦιἮΙᾞ\x15+ἧιἯΙᾟ\x15\x1dἠιᾐἨΙ\x15\x1dἡιᾑἩΙ" +
	"\x15\x1dἢιᾒἪΙ\x15\x1dἣιᾓἫΙ\x15\x1dἤιᾔἬΙ\x15\x1dἥιᾕἭΙ\x15\x1dἦιᾖἮΙ\x15" +
	"\x1dἧιᾗἯΙ\x15+ὠιὨΙᾨ\x15+ὡιὩΙᾩ\x15+ὢιὪΙᾪ\x15+ὣιὫΙᾫ\x15+ὤιὬΙᾬ\x15+ὥιὭΙᾭ" +
	"\x15+ὦιὮΙᾮ\x15+ὧιὯΙᾯ\x15\x1dὠιᾠὨΙ\x15\x1dὡιᾡὩΙ\x15\x1dὢιᾢὪΙ\x15\x1dὣιᾣὫΙ" +
	"\x15\x1dὤιᾤὬΙ\x15\x1dὥιᾥὭΙ\x15\x1dὦιᾦὮΙ\x15\x1dὧιᾧὯΙ\x15-ὰιᾺΙᾺͅ\x14#αιΑΙ" +
	"ᾼ\x14$άιΆΙΆͅ\x14$ᾶΑ͂Α͂\x166ᾶιΑ͂Ιᾼ͂\x14\x1cαιᾳΑΙ\x12\x12ιΙΙ\x15-ὴιῊΙ" +
	"Ὴͅ\x14#ηιΗΙῌ\x14$ήιΉΙΉͅ\x14$ῆΗ͂Η͂\x166ῆιΗ͂Ιῌ͂\x14\x1cηιῃΗΙ\x166ῒΙ" +
	"̈̀Ϊ̀\x166ΐΪ́Ϊ́\x14$ῖΙ͂Ι͂\x166ῗΪ͂Ϊ͂\x166ῢΫ̀Ϋ̀\x166ΰΫ́Ϋ" +
	"́\x14$ῤΡ̓Ρ̓\x14$ῦΥ͂Υ͂\x166ῧΫ͂Ϋ͂\x15-ὼιῺΙῺͅ\x14#ωιΩΙῼ\x14$ώιΏΙΏͅ" +
	"\x14$ῶΩ͂Ω͂\x166ῶιΩ͂Ιῼ͂\x14\x1cωιῳΩΙ\x12\x10ωω\x11\x08kk\x12\x10åå\x12" +
	"\x10ɫɫ\x12\x10ɽɽ\x10\x12ȺȺ\x10\x12ȾȾ\x12\x10ɑɑ\x12\x10ɱɱ\x12\x10ɐɐ\x12" +
	"\x10ɒɒ\x12\x10ȿȿ\x12\x10ɀɀ\x12\x10ɥɥ\x12\x10ɦɦ\x12\x10ɜɜ\x12\x10ɡɡ\x12" +
	"\x10ɬɬ\x12\x10ɪɪ\x12\x10ʞʞ\x12\x10ʇʇ\x12\x10ʝʝ\x12\x10ʂʂ\x12\x12ffFFFf" +
	"\x12\x12fiFIFi\x12\x12flFLFl\x13\x1bffiFFIFfi\x13\x1bfflFFLFfl\x12\x12st" +
	"STSt\x12\x12stSTSt\x14$մնՄՆՄն\x14$մեՄԵՄե\x14$միՄԻՄի\x14$վնՎՆՎն\x14$մխՄԽՄ" +
	"խ"

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *caseTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return caseValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = caseIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *caseTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return caseValues[c0]
	}
	i := caseIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *caseTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return caseValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = caseIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *caseTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return caseValues[c0]
	}
	i := caseIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// caseTrie. Total size: 13398 bytes (13.08 KiB). Checksum: 544af6e6b1b70931.
type caseTrie struct{}

func newCaseTrie(i int) *caseTrie {
	return &caseTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *caseTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 22:
		return uint16(caseValues[n<<6+uint32(b)])
	default:
		n -= 22
		return uint16(sparse.lookup(n, b))
	}
}

// caseValues: 24 blocks, 1536 entries, 3072 bytes
// The third block is the zero block.
var caseValues = [1536]uint16{
	// Block 0x0, offset 0x0
	0x27: 0x0054,
	0x2e: 0x0054,
	0x30: 0x0010, 0x31: 0x0010, 0x32: 0x0010, 0x33: 0x0010, 0x34: 0x0010, 0x35: 0x0010,
	0x36: 0x0010, 0x37: 0x0010, 0x38: 0x0010, 0x39: 0x0010, 0x3a: 0x0054,
	// Block 0x1, offset 0x40
	0x41: 0x2013, 0x42: 0x2013, 0x43: 0x2013, 0x44: 0x2013, 0x45: 0x2013,
	0x46: 0x2013, 0x47: 0x2013, 0x48: 0x2013, 0x49: 0x2013, 0x4a: 0x2013, 0x4b: 0x2013,
	0x4c: 0x2013, 0x4d: 0x2013, 0x4e: 0x2013, 0x4f: 0x2013, 0x50: 0x2013, 0x51: 0x2013,
	0x52: 0x2013, 0x53: 0x2013, 0x54: 0x2013, 0x55: 0x2013, 0x56: 0x2013, 0x57: 0x2013,
	0x58: 0x2013, 0x59: 0x2013, 0x5a: 0x2013,
	0x5e: 0x0004, 0x5f: 0x0010, 0x60: 0x0004, 0x61: 0x2012, 0x62: 0x2012, 0x63: 0x2012,
	0x64: 0x2012, 0x65: 0x2012, 0x66: 0x2012, 0x67: 0x2012, 0x68: 0x2012, 0x69: 0x2012,
	0x6a: 0x2012, 0x6b: 0x2012, 0x6c: 0x2012, 0x6d: 0x2012, 0x6e: 0x2012, 0x6f: 0x2012,
	0x70: 0x2012, 0x71: 0x2012, 0x72: 0x2012, 0x73: 0x2012, 0x74: 0x2012, 0x75: 0x2012,
	0x76: 0x2012, 0x77: 0x2012, 0x78: 0x2012, 0x79: 0x2012, 0x7a: 0x2012,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x0852, 0xc1: 0x0b53, 0xc2: 0x0113, 0xc3: 0x0112, 0xc4: 0x0113, 0xc5: 0x0112,
	0xc6: 0x0b53, 0xc7: 0x0f13, 0xc8: 0x0f12, 0xc9: 0x0e53, 0xca: 0x1153, 0xcb: 0x0713,
	0xcc: 0x0712, 0xcd: 0x0012, 0xce: 0x1453, 0xcf: 0x1753, 0xd0: 0x1a53, 0xd1: 0x0313,
	0xd2: 0x0312, 0xd3: 0x1d53, 0xd4: 0x2053, 0xd5: 0x2352, 0xd6: 0x2653, 0xd7: 0x2653,
	0xd8: 0x0113, 0xd9: 0x0112, 0xda: 0x2952, 0xdb: 0x0012, 0xdc: 0x1d53, 0xdd: 0x2c53,
	0xde: 0x2f52, 0xdf: 0x3253, 0xe0: 0x0113, 0xe1: 0x0112, 0xe2: 0x0113, 0xe3: 0x0112,
	0xe4: 0x0113, 0xe5: 0x0112, 0xe6: 0x3553, 0xe7: 0x0f13, 0xe8: 0x0f12, 0xe9: 0x3853,
	0xea: 0x0012, 0xeb: 0x0012, 0xec: 0x0113, 0xed: 0x0112, 0xee: 0x3553, 0xef: 0x1f13,
	0xf0: 0x1f12, 0xf1: 0x3b53, 0xf2: 0x3e53, 0xf3: 0x0713, 0xf4: 0x0712, 0xf5: 0x0313,
	0xf6: 0x0312, 0xf7: 0x4153, 0xf8: 0x0113, 0xf9: 0x0112, 0xfa: 0x0012, 0xfb: 0x0010,
	0xfc: 0x0113, 0xfd: 0x0112, 0xfe: 0x0012, 0xff: 0x4452,
	// Block 0x4, offset 0x100
	0x100: 0x0010, 0x101: 0x0010, 0x102: 0x0010, 0x103: 0x0010, 0x104: 0x02db, 0x105: 0x0359,
	0x106: 0x03da, 0x107: 0x043b, 0x108: 0x04b9, 0x109: 0x053a, 0x10a: 0x059b, 0x10b: 0x0619,
	0x10c: 0x069a, 0x10d: 0x0313, 0x10e: 0x0312, 0x10f: 0x1f13, 0x110: 0x1f12, 0x111: 0x0313,
	0x112: 0x0312, 0x113: 0x0713, 0x114: 0x0712, 0x115: 0x0313, 0x116: 0x0312, 0x117: 0x0f13,
	0x118: 0x0f12, 0x119: 0x0313, 0x11a: 0x0312, 0x11b: 0x0713, 0x11c: 0x0712, 0x11d: 0x1452,
	0x11e: 0x0113, 0x11f: 0x0112, 0x120: 0x0113, 0x121: 0x0112, 0x122: 0x0113, 0x123: 0x0112,
	0x124: 0x0113, 0x125: 0x0112, 0x126: 0x0113, 0x127: 0x0112, 0x128: 0x0113, 0x129: 0x0112,
	0x12a: 0x0113, 0x12b: 0x0112, 0x12c: 0x0113, 0x12d: 0x0112, 0x12e: 0x0113, 0x12f: 0x0112,
	0x130: 0x06fa, 0x131: 0x07ab, 0x132: 0x0829, 0x133: 0x08aa, 0x134: 0x0113, 0x135: 0x0112,
	0x136: 0x2353, 0x137: 0x4453, 0x138: 0x0113, 0x139: 0x0112, 0x13a: 0x0113, 0x13b: 0x0112,
	0x13c: 0x0113, 0x13d: 0x0112, 0x13e: 0x0113, 0x13f: 0x0112,
	// Block 0x5, offset 0x140
	0x140: 0x0a8a, 0x141: 0x0313, 0x142: 0x0312, 0x143: 0x0853, 0x144: 0x4753, 0x145: 0x4a53,
	0x146: 0x0113, 0x147: 0x0112, 0x148: 0x0113, 0x149: 0x0112, 0x14a: 0x0113, 0x14b: 0x0112,
	0x14c: 0x0113, 0x14d: 0x0112, 0x14e: 0x0113, 0x14f: 0x0112, 0x150: 0x0b0a, 0x151: 0x0b8a,
	0x152: 0x0c0a, 0x153: 0x0b52, 0x154: 0x0b52, 0x155: 0x0012, 0x156: 0x0e52, 0x157: 0x1152,
	0x158: 0x0012, 0x159: 0x1752, 0x15a: 0x0012, 0x15b: 0x1a52, 0x15c: 0x0c8a, 0x15d: 0x0012,
	0x15e: 0x0012, 0x15f: 0x0012, 0x160: 0x1d52, 0x161: 0x0d0a, 0x162: 0x0012, 0x163: 0x2052,
	0x164: 0x0012, 0x165: 0x0d8a, 0x166: 0x0e0a, 0x167: 0x0012, 0x168: 0x2652, 0x169: 0x2652,
	0x16a: 0x0e8a, 0x16b: 0x0f0a, 0x16c: 0x0f8a, 0x16d: 0x0012, 0x16e: 0x0012, 0x16f: 0x1d52,
	0x170: 0x0012, 0x171: 0x100a, 0x172: 0x2c52, 0x173: 0x0012, 0x174: 0x0012, 0x175: 0x3252,
	0x176: 0x0012, 0x177: 0x0012, 0x178: 0x0012, 0x179: 0x0012, 0x17a: 0x0012, 0x17b: 0x0012,
	0x17c: 0x0012, 0x17d: 0x108a, 0x17e: 0x0012, 0x17f: 0x0012,
	// Block 0x6, offset 0x180
	0x180: 0x3552, 0x181: 0x0012, 0x182: 0x110a, 0x183: 0x3852, 0x184: 0x0012, 0x185: 0x0012,
	0x186: 0x0012, 0x187: 0x118a, 0x188: 0x3552, 0x189: 0x4752, 0x18a: 0x3b52, 0x18b: 0x3e52,
	0x18c: 0x4a52, 0x18d: 0x0012, 0x18e: 0x0012, 0x18f: 0x0012, 0x190: 0x0012, 0x191: 0x0012,
	0x192: 0x4152, 0x193: 0x0012, 0x194: 0x0010, 0x195: 0x0012, 0x196: 0x0012, 0x197: 0x0012,
	0x198: 0x0012, 0x199: 0x0012, 0x19a: 0x0012, 0x19b: 0x0012, 0x19c: 0x0012, 0x19d: 0x120a,
	0x19e: 0x128a, 0x19f: 0x0012, 0x1a0: 0x0012, 0x1a1: 0x0012, 0x1a2: 0x0012, 0x1a3: 0x0012,
	0x1a4: 0x0012, 0x1a5: 0x0012, 0x1a6: 0x0012, 0x1a7: 0x0012, 0x1a8: 0x0012, 0x1a9: 0x0012,
	0x1aa: 0x0012, 0x1ab: 0x0012, 0x1ac: 0x0012, 0x1ad: 0x0012, 0x1ae: 0x0012, 0x1af: 0x0012,
	0x1b0: 0x0015, 0x1b1: 0x0015, 0x1b2: 0x0015, 0x1b3: 0x0015, 0x1b4: 0x0015, 0x1b5: 0x0015,
	0x1b6: 0x0015, 0x1b7: 0x0015, 0x1b8: 0x0015, 0x1b9: 0x0014, 0x1ba: 0x0014, 0x1bb: 0x0014,
	0x1bc: 0x0014, 0x1bd: 0x0014, 0x1be: 0x0014, 0x1bf: 0x0014,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x0024, 0x1c1: 0x0024, 0x1c2: 0x0024, 0x1c3: 0x0024, 0x1c4: 0x0024, 0x1c5: 0x130d,
	0x1c6: 0x0024, 0x1c7: 0x0034, 0x1c8: 0x0034, 0x1c9: 0x0034, 0x1ca: 0x0024, 0x1cb: 0x0024,
	0x1cc: 0x0024, 0x1cd: 0x0034, 0x1ce: 0x0034, 0x1cf: 0x0014, 0x1d0: 0x0024, 0x1d1: 0x0024,
	0x1d2: 0x0024, 0x1d3: 0x0034, 0x1d4: 0x0034, 0x1d5: 0x0034, 0x1d6: 0x0034, 0x1d7: 0x0024,
	0x1d8: 0x0034, 0x1d9: 0x0034, 0x1da: 0x0034, 0x1db: 0x0024, 0x1dc: 0x0034, 0x1dd: 0x0034,
	0x1de: 0x0034, 0x1df: 0x0034, 0x1e0: 0x0034, 0x1e1: 0x0034, 0x1e2: 0x0034, 0x1e3: 0x0024,
	0x1e4: 0x0024, 0x1e5: 0x0024, 0x1e6: 0x0024, 0x1e7: 0x0024, 0x1e8: 0x0024, 0x1e9: 0x0024,
	0x1ea: 0x0024, 0x1eb: 0x0024, 0x1ec: 0x0024, 0x1ed: 0x0024, 0x1ee: 0x0024, 0x1ef: 0x0024,
	0x1f0: 0x0113, 0x1f1: 0x0112, 0x1f2: 0x0113, 0x1f3: 0x0112, 0x1f4: 0x0014, 0x1f5: 0x0004,
	0x1f6: 0x0113, 0x1f7: 0x0112, 0x1fa: 0x0015, 0x1fb: 0x4d52,
	0x1fc: 0x5052, 0x1fd: 0x5052, 0x1ff: 0x5353,
	// Block 0x8, offset 0x200
	0x204: 0x0004, 0x205: 0x0004,
	0x206: 0x2a13, 0x207: 0x0054, 0x208: 0x2513, 0x209: 0x2713, 0x20a: 0x2513,
	0x20c: 0x5653, 0x20e: 0x5953, 0x20f: 0x5c53, 0x210: 0x138a, 0x211: 0x2013,
	0x212: 0x2013, 0x213: 0x2013, 0x214: 0x2013, 0x215: 0x2013, 0x216: 0x2013, 0x217: 0x2013,
	0x218: 0x2013, 0x219: 0x2013, 0x21a: 0x2013, 0x21b: 0x2013, 0x21c: 0x2013, 0x21d: 0x2013,
	0x21e: 0x2013, 0x21f: 0x2013, 0x220: 0x5f53, 0x221: 0x5f53, 0x223: 0x5f53,
	0x224: 0x5f53, 0x225: 0x5f53, 0x226: 0x5f53, 0x227: 0x5f53, 0x228: 0x5f53, 0x229: 0x5f53,
	0x22a: 0x5f53, 0x22b: 0x5f53, 0x22c: 0x2a12, 0x22d: 0x2512, 0x22e: 0x2712, 0x22f: 0x2512,
	0x230: 0x14ca, 0x231: 0x2012, 0x232: 0x2012, 0x233: 0x2012, 0x234: 0x2012, 0x235: 0x2012,
	0x236: 0x2012, 0x237: 0x2012, 0x238: 0x2012, 0x239: 0x2012, 0x23a: 0x2012, 0x23b: 0x2012,
	0x23c: 0x2012, 0x23d: 0x2012, 0x23e: 0x2012, 0x23f: 0x2012,
	// Block 0x9, offset 0x240
	0x240: 0x5f52, 0x241: 0x5f52, 0x242: 0x160a, 0x243: 0x5f52, 0x244: 0x5f52, 0x245: 0x5f52,
	0x246: 0x5f52, 0x247: 0x5f52, 0x248: 0x5f52, 0x249: 0x5f52, 0x24a: 0x5f52, 0x24b: 0x5f52,
	0x24c: 0x5652, 0x24d: 0x5952, 0x24e: 0x5c52, 0x24f: 0x1813, 0x250: 0x168a, 0x251: 0x170a,
	0x252: 0x0013, 0x253: 0x0013, 0x254: 0x0013, 0x255: 0x178a, 0x256: 0x180a, 0x257: 0x1812,
	0x258: 0x0113, 0x259: 0x0112, 0x25a: 0x0113, 0x25b: 0x0112, 0x25c: 0x0113, 0x25d: 0x0112,
	0x25e: 0x0113, 0x25f: 0x0112, 0x260: 0x0113, 0x261: 0x0112, 0x262: 0x0113, 0x263: 0x0112,
	0x264: 0x0113, 0x265: 0x0112, 0x266: 0x0113, 0x267: 0x0112, 0x268: 0x0113, 0x269: 0x0112,
	0x26a: 0x0113, 0x26b: 0x0112, 0x26c: 0x0113, 0x26d: 0x0112, 0x26e: 0x0113, 0x26f: 0x0112,
	0x270: 0x188a, 0x271: 0x190a, 0x272: 0x0b12, 0x273: 0x5352, 0x274: 0x6253, 0x275: 0x198a,
	0x277: 0x0f13, 0x278: 0x0f12, 0x279: 0x0b13, 0x27a: 0x0113, 0x27b: 0x0112,
	0x27c: 0x0012, 0x27d: 0x4d53, 0x27e: 0x5053, 0x27f: 0x5053,
	// Block 0xa, offset 0x280
	0x280: 0x6852, 0x281: 0x6852, 0x282: 0x6852, 0x283: 0x6852, 0x284: 0x6852, 0x285: 0x6852,
	0x286: 0x6852, 0x287: 0x1a0a, 0x288: 0x0012, 0x28a: 0x0010,
	0x291: 0x0034,
	0x292: 0x0024, 0x293: 0x0024, 0x294: 0x0024, 0x295: 0x0024, 0x296: 0x0034, 0x297: 0x0024,
	0x298: 0x0024, 0x299: 0x0024, 0x29a: 0x0034, 0x29b: 0x0034, 0x29c: 0x0024, 0x29d: 0x0024,
	0x29e: 0x0024, 0x29f: 0x0024, 0x2a0: 0x0024, 0x2a1: 0x0024, 0x2a2: 0x0034, 0x2a3: 0x0034,
	0x2a4: 0x0034, 0x2a5: 0x0034, 0x2a6: 0x0034, 0x2a7: 0x0034, 0x2a8: 0x0024, 0x2a9: 0x0024,
	0x2aa: 0x0034, 0x2ab: 0x0024, 0x2ac: 0x0024, 0x2ad: 0x0034, 0x2ae: 0x0034, 0x2af: 0x0024,
	0x2b0: 0x0034, 0x2b1: 0x0034, 0x2b2: 0x0034, 0x2b3: 0x0034, 0x2b4: 0x0034, 0x2b5: 0x0034,
	0x2b6: 0x0034, 0x2b7: 0x0034, 0x2b8: 0x0034, 0x2b9: 0x0034, 0x2ba: 0x0034, 0x2bb: 0x0034,
	0x2bc: 0x0034, 0x2bd: 0x0034, 0x2bf: 0x0034,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x0010, 0x2c1: 0x0010, 0x2c2: 0x0010, 0x2c3: 0x0010, 0x2c4: 0x0010, 0x2c5: 0x0010,
	0x2c6: 0x0010, 0x2c7: 0x0010, 0x2c8: 0x0010, 0x2c9: 0x0014, 0x2ca: 0x0024, 0x2cb: 0x0024,
	0x2cc: 0x0024, 0x2cd: 0x0024, 0x2ce: 0x0024, 0x2cf: 0x0034, 0x2d0: 0x0034, 0x2d1: 0x0034,
	0x2d2: 0x0034, 0x2d3: 0x0034, 0x2d4: 0x0024, 0x2d5: 0x0024, 0x2d6: 0x0024, 0x2d7: 0x0024,
	0x2d8: 0x0024, 0x2d9: 0x0024, 0x2da: 0x0024, 0x2db: 0x0024, 0x2dc: 0x0024, 0x2dd: 0x0024,
	0x2de: 0x0024, 0x2df: 0x0024, 0x2e0: 0x0024, 0x2e1: 0x0024, 0x2e2: 0x0014, 0x2e3: 0x0034,
	0x2e4: 0x0024, 0x2e5: 0x0024, 0x2e6: 0x0034, 0x2e7: 0x0024, 0x2e8: 0x0024, 0x2e9: 0x0034,
	0x2ea: 0x0024, 0x2eb: 0x0024, 0x2ec: 0x0024, 0x2ed: 0x0034, 0x2ee: 0x0034, 0x2ef: 0x0034,
	0x2f0: 0x0034, 0x2f1: 0x0034, 0x2f2: 0x0034, 0x2f3: 0x0024, 0x2f4: 0x0024, 0x2f5: 0x0024,
	0x2f6: 0x0034, 0x2f7: 0x0024, 0x2f8: 0x0024, 0x2f9: 0x0034, 0x2fa: 0x0034, 0x2fb: 0x0024,
	0x2fc: 0x0024, 0x2fd: 0x0024, 0x2fe: 0x0024, 0x2ff: 0x0024,
	// Block 0xc, offset 0x300
	0x300: 0x7053, 0x301: 0x7053, 0x302: 0x7053, 0x303: 0x7053, 0x304: 0x7053, 0x305: 0x7053,
	0x307: 0x7053,
	0x30d: 0x7053, 0x310: 0x1aea, 0x311: 0x1b6a,
	0x312: 0x1bea, 0x313: 0x1c6a, 0x314: 0x1cea, 0x315: 0x1d6a, 0x316: 0x1dea, 0x317: 0x1e6a,
	0x318: 0x1eea, 0x319: 0x1f6a, 0x31a: 0x1fea, 0x31b: 0x206a, 0x31c: 0x20ea, 0x31d: 0x216a,
	0x31e: 0x21ea, 0x31f: 0x226a, 0x320: 0x22ea, 0x321: 0x236a, 0x322: 0x23ea, 0x323: 0x246a,
	0x324: 0x24ea, 0x325: 0x256a, 0x326: 0x25ea, 0x327: 0x266a, 0x328: 0x26ea, 0x329: 0x276a,
	0x32a: 0x27ea, 0x32b: 0x286a, 0x32c: 0x28ea, 0x32d: 0x296a, 0x32e: 0x29ea, 0x32f: 0x2a6a,
	0x330: 0x2aea, 0x331: 0x2b6a, 0x332: 0x2bea, 0x333: 0x2c6a, 0x334: 0x2cea, 0x335: 0x2d6a,
	0x336: 0x2dea, 0x337: 0x2e6a, 0x338: 0x2eea, 0x339: 0x2f6a, 0x33a: 0x2fea,
	0x33c: 0x0015, 0x33d: 0x306a, 0x33e: 0x30ea, 0x33f: 0x316a,
	// Block 0xd, offset 0x340
	0x340: 0x0812, 0x341: 0x0812, 0x342: 0x0812, 0x343: 0x0812, 0x344: 0x0812, 0x345: 0x0812,
	0x348: 0x0813, 0x349: 0x0813, 0x34a: 0x0813, 0x34b: 0x0813,
	0x34c: 0x0813, 0x34d: 0x0813, 0x350: 0x3b1a, 0x351: 0x0812,
	0x352: 0x3bfa, 0x353: 0x0812, 0x354: 0x3d3a, 0x355: 0x0812, 0x356: 0x3e7a, 0x357: 0x0812,
	0x359: 0x0813, 0x35b: 0x0813, 0x35d: 0x0813,
	0x35f: 0x0813, 0x360: 0x0812, 0x361: 0x0812, 0x362: 0x0812, 0x363: 0x0812,
	0x364: 0x0812, 0x365: 0x0812, 0x366: 0x0812, 0x367: 0x0812, 0x368: 0x0813, 0x369: 0x0813,
	0x36a: 0x0813, 0x36b: 0x0813, 0x36c: 0x0813, 0x36d: 0x0813, 0x36e: 0x0813, 0x36f: 0x0813,
	0x370: 0x9252, 0x371: 0x9252, 0x372: 0x9552, 0x373: 0x9552, 0x374: 0x9852, 0x375: 0x9852,
	0x376: 0x9b52, 0x377: 0x9b52, 0x378: 0x9e52, 0x379: 0x9e52, 0x37a: 0xa152, 0x37b: 0xa152,
	0x37c: 0x4d52, 0x37d: 0x4d52,
	// Block 0xe, offset 0x380
	0x380: 0x3fba, 0x381: 0x40aa, 0x382: 0x419a, 0x383: 0x428a, 0x384: 0x437a, 0x385: 0x446a,
	0x386: 0x455a, 0x387: 0x464a, 0x388: 0x4739, 0x389: 0x4829, 0x38a: 0x4919, 0x38b: 0x4a09,
	0x38c: 0x4af9, 0x38d: 0x4be9, 0x38e: 0x4cd9, 0x38f: 0x4dc9, 0x390: 0x4eba, 0x391: 0x4faa,
	0x392: 0x509a, 0x393: 0x518a, 0x394: 0x527a, 0x395: 0x536a, 0x396: 0x545a, 0x397: 0x554a,
	0x398: 0x5639, 0x399: 0x5729, 0x39a: 0x5819, 0x39b: 0x5909, 0x39c: 0x59f9, 0x39d: 0x5ae9,
	0x39e: 0x5bd9, 0x39f: 0x5cc9, 0x3a0: 0x5dba, 0x3a1: 0x5eaa, 0x3a2: 0x5f9a, 0x3a3: 0x608a,
	0x3a4: 0x617a, 0x3a5: 0x626a, 0x3a6: 0x635a, 0x3a7: 0x644a, 0x3a8: 0x6539, 0x3a9: 0x6629,
	0x3aa: 0x6719, 0x3ab: 0x6809, 0x3ac: 0x68f9, 0x3ad: 0x69e9, 0x3ae: 0x6ad9, 0x3af: 0x6bc9,
	0x3b0: 0x0812, 0x3b1: 0x0812, 0x3b2: 0x6cba, 0x3b3: 0x6dca, 0x3b4: 0x6e9a,
	0x3b6: 0x6f7a, 0x3b7: 0x705a, 0x3b8: 0x0813, 0x3b9: 0x0813, 0x3ba: 0x9253, 0x3bb: 0x9253,
	0x3bc: 0x7199, 0x3bd: 0x0004, 0x3be: 0x726a, 0x3bf: 0x0004,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x0004, 0x3c1: 0x0004, 0x3c2: 0x72ea, 0x3c3: 0x73fa, 0x3c4: 0x74ca,
	0x3c6: 0x75aa, 0x3c7: 0x768a, 0x3c8: 0x9553, 0x3c9: 0x9553, 0x3ca: 0x9853, 0x3cb: 0x9853,
	0x3cc: 0x77c9, 0x3cd: 0x0004, 0x3ce: 0x0004, 0x3cf: 0x0004, 0x3d0: 0x0812, 0x3d1: 0x0812,
	0x3d2: 0x789a, 0x3d3: 0x79da, 0x3d6: 0x7b1a, 0x3d7: 0x7bfa,
	0x3d8: 0x0813, 0x3d9: 0x0813, 0x3da: 0x9b53, 0x3db: 0x9b53, 0x3dd: 0x0004,
	0x3de: 0x0004, 0x3df: 0x0004, 0x3e0: 0x0812, 0x3e1: 0x0812, 0x3e2: 0x7d3a, 0x3e3: 0x7e7a,
	0x3e4: 0x7fba, 0x3e5: 0x0912, 0x3e6: 0x809a, 0x3e7: 0x817a, 0x3e8: 0x0813, 0x3e9: 0x0813,
	0x3ea: 0xa153, 0x3eb: 0xa153, 0x3ec: 0x0913, 0x3ed: 0x0004, 0x3ee: 0x0004, 0x3ef: 0x0004,
	0x3f2: 0x82ba, 0x3f3: 0x83ca, 0x3f4: 0x849a,
	0x3f6: 0x857a, 0x3f7: 0x865a, 0x3f8: 0x9e53, 0x3f9: 0x9e53, 0x3fa: 0x4d53, 0x3fb: 0x4d53,
	0x3fc: 0x8799, 0x3fd: 0x0004, 0x3fe: 0x0004,
	// Block 0x10, offset 0x400
	0x402: 0x0013,
	0x407: 0x0013, 0x40a: 0x0012, 0x40b: 0x0013,
	0x40c: 0x0013, 0x40d: 0x0013, 0x40e: 0x0012, 0x40f: 0x0012, 0x410: 0x0013, 0x411: 0x0013,
	0x412: 0x0013, 0x413: 0x0012, 0x415: 0x0013,
	0x419: 0x0013, 0x41a: 0x0013, 0x41b: 0x0013, 0x41c: 0x0013, 0x41d: 0x0013,
	0x424: 0x0013, 0x426: 0x886b, 0x428: 0x0013,
	0x42a: 0x88cb, 0x42b: 0x890b, 0x42c: 0x0013, 0x42d: 0x0013, 0x42f: 0x0012,
	0x430: 0x0013, 0x431: 0x0013, 0x432: 0xa453, 0x433: 0x0013, 0x434: 0x0012, 0x435: 0x0010,
	0x436: 0x0010, 0x437: 0x0010, 0x438: 0x0010, 0x439: 0x0012,
	0x43c: 0x0012, 0x43d: 0x0012, 0x43e: 0x0013, 0x43f: 0x0013,
	// Block 0x11, offset 0x440
	0x440: 0x1a13, 0x441: 0x1a13, 0x442: 0x1e13, 0x443: 0x1e13, 0x444: 0x1a13, 0x445: 0x1a13,
	0x446: 0x2613, 0x447: 0x2613, 0x448: 0x2a13, 0x449: 0x2a13, 0x44a: 0x2e13, 0x44b: 0x2e13,
	0x44c: 0x2a13, 0x44d: 0x2a13, 0x44e: 0x2613, 0x44f: 0x2613, 0x450: 0xa752, 0x451: 0xa752,
	0x452: 0xaa52, 0x453: 0xaa52, 0x454: 0xad52, 0x455: 0xad52, 0x456: 0xaa52, 0x457: 0xaa52,
	0x458: 0xa752, 0x459: 0xa752, 0x45a: 0x1a12, 0x45b: 0x1a12, 0x45c: 0x1e12, 0x45d: 0x1e12,
	0x45e: 0x1a12, 0x45f: 0x1a12, 0x460: 0x2612, 0x461: 0x2612, 0x462: 0x2a12, 0x463: 0x2a12,
	0x464: 0x2e12, 0x465: 0x2e12, 0x466: 0x2a12, 0x467: 0x2a12, 0x468: 0x2612, 0x469: 0x2612,
	// Block 0x12, offset 0x480
	0x480: 0x6552, 0x481: 0x6552, 0x482: 0x6552, 0x483: 0x6552, 0x484: 0x6552, 0x485: 0x6552,
	0x486: 0x6552, 0x487: 0x6552, 0x488: 0x6552, 0x489: 0x6552, 0x48a: 0x6552, 0x48b: 0x6552,
	0x48c: 0x6552, 0x48d: 0x6552, 0x48e: 0x6552, 0x48f: 0x6552, 0x490: 0xb052, 0x491: 0xb052,
	0x492: 0xb052, 0x493: 0xb052, 0x494: 0xb052, 0x495: 0xb052, 0x496: 0xb052, 0x497: 0xb052,
	0x498: 0xb052, 0x499: 0xb052, 0x49a: 0xb052, 0x49b: 0xb052, 0x49c: 0xb052, 0x49d: 0xb052,
	0x49e: 0xb052, 0x49f: 0xb052, 0x4a0: 0x0113, 0x4a1: 0x0112, 0x4a2: 0x896b, 0x4a3: 0x8b53,
	0x4a4: 0x89cb, 0x4a5: 0x8a2a, 0x4a6: 0x8a8a, 0x4a7: 0x0f13, 0x4a8: 0x0f12, 0x4a9: 0x0313,
	0x4aa: 0x0312, 0x4ab: 0x0713, 0x4ac: 0x0712, 0x4ad: 0x8aeb, 0x4ae: 0x8b4b, 0x4af: 0x8bab,
	0x4b0: 0x8c0b, 0x4b1: 0x0012, 0x4b2: 0x0113, 0x4b3: 0x0112, 0x4b4: 0x0012, 0x4b5: 0x0313,
	0x4b6: 0x0312, 0x4b7: 0x0012, 0x4b8: 0x0012, 0x4b9: 0x0012, 0x4ba: 0x0012, 0x4bb: 0x0012,
	0x4bc: 0x0015, 0x4bd: 0x0015, 0x4be: 0x8c6b, 0x4bf: 0x8ccb,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x0113, 0x4c1: 0x0112, 0x4c2: 0x0113, 0x4c3: 0x0112, 0x4c4: 0x0113, 0x4c5: 0x0112,
	0x4c6: 0x0113, 0x4c7: 0x0112, 0x4c8: 0x0014, 0x4c9: 0x0014, 0x4ca: 0x0014, 0x4cb: 0x0713,
	0x4cc: 0x0712, 0x4cd: 0x8d2b, 0x4ce: 0x0012, 0x4cf: 0x0010, 0x4d0: 0x0113, 0x4d1: 0x0112,
	0x4d2: 0x0113, 0x4d3: 0x0112, 0x4d4: 0x6552, 0x4d5: 0x0012, 0x4d6: 0x0113, 0x4d7: 0x0112,
	0x4d8: 0x0113, 0x4d9: 0x0112, 0x4da: 0x0113, 0x4db: 0x0112, 0x4dc: 0x0113, 0x4dd: 0x0112,
	0x4de: 0x0113, 0x4df: 0x0112, 0x4e0: 0x0113, 0x4e1: 0x0112, 0x4e2: 0x0113, 0x4e3: 0x0112,
	0x4e4: 0x0113, 0x4e5: 0x0112, 0x4e6: 0x0113, 0x4e7: 0x0112, 0x4e8: 0x0113, 0x4e9: 0x0112,
	0x4ea: 0x8d8b, 0x4eb: 0x8deb, 0x4ec: 0x8e4b, 0x4ed: 0x8eab, 0x4ee: 0x8f0b, 0x4ef: 0x0012,
	0x4f0: 0x8f6b, 0x4f1: 0x8fcb, 0x4f2: 0x902b, 0x4f3: 0xb353, 0x4f4: 0x0113, 0x4f5: 0x0112,
	0x4f6: 0x0113, 0x4f7: 0x0112, 0x4f8: 0x0113, 0x4f9: 0x0112, 0x4fa: 0x0113, 0x4fb: 0x0112,
	0x4fc: 0x0113, 0x4fd: 0x0112, 0x4fe: 0x0113, 0x4ff: 0x0112,
	// Block 0x14, offset 0x500
	0x500: 0x90ea, 0x501: 0x916a, 0x502: 0x91ea, 0x503: 0x926a, 0x504: 0x931a, 0x505: 0x93ca,
	0x506: 0x944a,
	0x513: 0x94ca, 0x514: 0x95aa, 0x515: 0x968a, 0x516: 0x976a, 0x517: 0x984a,
	0x51d: 0x0010,
	0x51e: 0x0034, 0x51f: 0x0010, 0x520: 0x0010, 0x521: 0x0010, 0x522: 0x0010, 0x523: 0x0010,
	0x524: 0x0010, 0x525: 0x0010, 0x526: 0x0010, 0x527: 0x0010, 0x528: 0x0010,
	0x52a: 0x0010, 0x52b: 0x0010, 0x52c: 0x0010, 0x52d: 0x0010, 0x52e: 0x0010, 0x52f: 0x0010,
	0x530: 0x0010, 0x531: 0x0010, 0x532: 0x0010, 0x533: 0x0010, 0x534: 0x0010, 0x535: 0x0010,
	0x536: 0x0010, 0x538: 0x0010, 0x539: 0x0010, 0x53a: 0x0010, 0x53b: 0x0010,
	0x53c: 0x0010, 0x53e: 0x0010,
	// Block 0x15, offset 0x540
	0x540: 0x2713, 0x541: 0x2913, 0x542: 0x2b13, 0x543: 0x2913, 0x544: 0x2f13, 0x545: 0x2913,
	0x546: 0x2b13, 0x547: 0x2913, 0x548: 0x2713, 0x549: 0x3913, 0x54a: 0x3b13,
	0x54c: 0x3f13, 0x54d: 0x3913, 0x54e: 0x3b13, 0x54f: 0x3913, 0x550: 0x2713, 0x551: 0x2913,
	0x552: 0x2b13, 0x554: 0x2f13, 0x555: 0x2913, 0x557: 0xbc52,
	0x558: 0xbf52, 0x559: 0xc252, 0x55a: 0xbf52, 0x55b: 0xc552, 0x55c: 0xbf52, 0x55d: 0xc252,
	0x55e: 0xbf52, 0x55f: 0xbc52, 0x560: 0xc852, 0x561: 0xcb52, 0x563: 0xce52,
	0x564: 0xc852, 0x565: 0xcb52, 0x566: 0xc852, 0x567: 0x2712, 0x568: 0x2912, 0x569: 0x2b12,
	0x56a: 0x2912, 0x56b: 0x2f12, 0x56c: 0x2912, 0x56d: 0x2b12, 0x56e: 0x2912, 0x56f: 0x2712,
	0x570: 0x3912, 0x571: 0x3b12, 0x573: 0x3f12, 0x574: 0x3912, 0x575: 0x3b12,
	0x576: 0x3912, 0x577: 0x2712, 0x578: 0x2912, 0x579: 0x2b12, 0x57b: 0x2f12,
	0x57c: 0x2912,
	// Block 0x16, offset 0x580
	0x580: 0x2213, 0x581: 0x2213, 0x582: 0x2613, 0x583: 0x2613, 0x584: 0x2213, 0x585: 0x2213,
	0x586: 0x2e13, 0x587: 0x2e13, 0x588: 0x2213, 0x589: 0x2213, 0x58a: 0x2613, 0x58b: 0x2613,
	0x58c: 0x2213, 0x58d: 0x2213, 0x58e: 0x3e13, 0x58f: 0x3e13, 0x590: 0x2213, 0x591: 0x2213,
	0x592: 0x2613, 0x593: 0x2613, 0x594: 0x2213, 0x595: 0x2213, 0x596: 0x2e13, 0x597: 0x2e13,
	0x598: 0x2213, 0x599: 0x2213, 0x59a: 0x2613, 0x59b: 0x2613, 0x59c: 0x2213, 0x59d: 0x2213,
	0x59e: 0xd153, 0x59f: 0xd153, 0x5a0: 0xd453, 0x5a1: 0xd453, 0x5a2: 0x2212, 0x5a3: 0x2212,
	0x5a4: 0x2612, 0x5a5: 0x2612, 0x5a6: 0x2212, 0x5a7: 0x2212, 0x5a8: 0x2e12, 0x5a9: 0x2e12,
	0x5aa: 0x2212, 0x5ab: 0x2212, 0x5ac: 0x2612, 0x5ad: 0x2612, 0x5ae: 0x2212, 0x5af: 0x2212,
	0x5b0: 0x3e12, 0x5b1: 0x3e12, 0x5b2: 0x2212, 0x5b3: 0x2212, 0x5b4: 0x2612, 0x5b5: 0x2612,
	0x5b6: 0x2212, 0x5b7: 0x2212, 0x5b8: 0x2e12, 0x5b9: 0x2e12, 0x5ba: 0x2212, 0x5bb: 0x2212,
	0x5bc: 0x2612, 0x5bd: 0x2612, 0x5be: 0x2212, 0x5bf: 0x2212,
	// Block 0x17, offset 0x5c0
	0x5c2: 0x0010,
	0x5c7: 0x0010, 0x5c9: 0x0010, 0x5cb: 0x0010,
	0x5cd: 0x0010, 0x5ce: 0x0010, 0x5cf: 0x0010, 0x5d1: 0x0010,
	0x5d2: 0x0010, 0x5d4: 0x0010, 0x5d7: 0x0010,
	0x5d9: 0x0010, 0x5db: 0x0010, 0x5dd: 0x0010,
	0x5df: 0x0010, 0x5e1: 0x0010, 0x5e2: 0x0010,
	0x5e4: 0x0010, 0x5e7: 0x0010, 0x5e8: 0x0010, 0x5e9: 0x0010,
	0x5ea: 0x0010, 0x5ec: 0x0010, 0x5ed: 0x0010, 0x5ee: 0x0010, 0x5ef: 0x0010,
	0x5f0: 0x0010, 0x5f1: 0x0010, 0x5f2: 0x0010, 0x5f4: 0x0010, 0x5f5: 0x0010,
	0x5f6: 0x0010, 0x5f7: 0x0010, 0x5f9: 0x0010, 0x5fa: 0x0010, 0x5fb: 0x0010,
	0x5fc: 0x0010, 0x5fe: 0x0010,
}

// caseIndex: 27 blocks, 1728 entries, 3456 bytes
// Block 0 is the zero block.
var caseIndex = [1728]uint16{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x16, 0xc3: 0x17, 0xc4: 0x18, 0xc5: 0x19, 0xc6: 0x01, 0xc7: 0x02,
	0xc8: 0x1a, 0xc9: 0x03, 0xca: 0x04, 0xcb: 0x1b, 0xcc: 0x1c, 0xcd: 0x05, 0xce: 0x06, 0xcf: 0x07,
	0xd0: 0x1d, 0xd1: 0x1e, 0xd2: 0x1f, 0xd3: 0x20, 0xd4: 0x21, 0xd5: 0x22, 0xd6: 0x08, 0xd7: 0x23,
	0xd8: 0x24, 0xd9: 0x25, 0xda: 0x26, 0xdb: 0x27, 0xdc: 0x28, 0xdd: 0x29, 0xde: 0x2a, 0xdf: 0x2b,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05,
	0xea: 0x06, 0xeb: 0x07, 0xec: 0x07, 0xed: 0x08, 0xef: 0x09,
	0xf0: 0x16, 0xf3: 0x18,
	// Block 0x4, offset 0x100
	0x120: 0x2c, 0x121: 0x2d, 0x122: 0x2e, 0x123: 0x09, 0x124: 0x2f, 0x125: 0x30, 0x126: 0x31, 0x127: 0x32,
	0x128: 0x33, 0x129: 0x34, 0x12a: 0x35, 0x12b: 0x36, 0x12c: 0x37, 0x12d: 0x38, 0x12e: 0x39, 0x12f: 0x3a,
	0x130: 0x3b, 0x131: 0x3c, 0x132: 0x3d, 0x133: 0x3e, 0x134: 0x3f, 0x135: 0x40, 0x136: 0x41, 0x137: 0x42,
	0x138: 0x43, 0x139: 0x44, 0x13a: 0x45, 0x13b: 0x46, 0x13c: 0x47, 0x13d: 0x48, 0x13e: 0x49, 0x13f: 0x4a,
	// Block 0x5, offset 0x140
	0x140: 0x4b, 0x141: 0x4c, 0x142: 0x4d, 0x143: 0x0a, 0x144: 0x26, 0x145: 0x26, 0x146: 0x26, 0x147: 0x26,
	0x148: 0x26, 0x149: 0x4e, 0x14a: 0x4f, 0x14b: 0x50, 0x14c: 0x51, 0x14d: 0x52, 0x14e: 0x53, 0x14f: 0x54,
	0x150: 0x55, 0x151: 0x26, 0x152: 0x26, 0x153: 0x26, 0x154: 0x26, 0x155: 0x26, 0x156: 0x26, 0x157: 0x26,
	0x158: 0x26, 0x159: 0x56, 0x15a: 0x57, 0x15b: 0x58, 0x15c: 0x59, 0x15d: 0x5a, 0x15e: 0x5b, 0x15f: 0x5c,
	0x160: 0x5d, 0x161: 0x5e, 0x162: 0x5f, 0x163: 0x60, 0x164: 0x61, 0x165: 0x62, 0x167: 0x63,
	0x168: 0x64, 0x169: 0x65, 0x16a: 0x66, 0x16b: 0x67, 0x16c: 0x68, 0x16d: 0x69, 0x16e: 0x6a, 0x16f: 0x6b,
	0x170: 0x6c, 0x171: 0x6d, 0x172: 0x6e, 0x173: 0x6f, 0x174: 0x70, 0x175: 0x71, 0x176: 0x72, 0x177: 0x73,
	0x178: 0x74, 0x179: 0x74, 0x17a: 0x75, 0x17b: 0x74, 0x17c: 0x76, 0x17d: 0x0b, 0x17e: 0x0c, 0x17f: 0x0d,
	// Block 0x6, offset 0x180
	0x180: 0x77, 0x181: 0x78, 0x182: 0x79, 0x183: 0x7a, 0x184: 0x0e, 0x185: 0x7b, 0x186: 0x7c,
	0x192: 0x7d, 0x193: 0x0f,
	0x1b0: 0x7e, 0x1b1: 0x10, 0x1b2: 0x74, 0x1b3: 0x7f, 0x1b4: 0x80, 0x1b5: 0x81, 0x1b6: 0x82, 0x1b7: 0x83,
	0x1b8: 0x84,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x85, 0x1c2: 0x86, 0x1c3: 0x87, 0x1c4: 0x88, 0x1c5: 0x26, 0x1c6: 0x89,
	// Block 0x8, offset 0x200
	0x200: 0x8a, 0x201: 0x26, 0x202: 0x26, 0x203: 0x26, 0x204: 0x26, 0x205: 0x26, 0x206: 0x26, 0x207: 0x26,
	0x208: 0x26, 0x209: 0x26, 0x20a: 0x26, 0x20b: 0x26, 0x20c: 0x26, 0x20d: 0x26, 0x20e: 0x26, 0x20f: 0x26,
	0x210: 0x26, 0x211: 0x26, 0x212: 0x8b, 0x213: 0x8c, 0x214: 0x26, 0x215: 0x26, 0x216: 0x26, 0x217: 0x26,
	0x218: 0x8d, 0x219: 0x8e, 0x21a: 0x8f, 0x21b: 0x90, 0x21c: 0x91, 0x21d: 0x92, 0x21e: 0x11, 0x21f: 0x93,
	0x220: 0x94, 0x221: 0x95, 0x222: 0x26, 0x223: 0x96, 0x224: 0x97, 0x225: 0x98, 0x226: 0x99, 0x227: 0x9a,
	0x228: 0x9b, 0x229: 0x9c, 0x22a: 0x9d, 0x22b: 0x9e, 0x22c: 0x9f, 0x22d: 0xa0, 0x22e: 0xa1, 0x22f: 0xa2,
	0x230: 0x26, 0x231: 0x26, 0x232: 0x26, 0x233: 0x26, 0x234: 0x26, 0x235: 0x26, 0x236: 0x26, 0x237: 0x26,
	0x238: 0x26, 0x239: 0x26, 0x23a: 0x26, 0x23b: 0x26, 0x23c: 0x26, 0x23d: 0x26, 0x23e: 0x26, 0x23f: 0x26,
	// Block 0x9, offset 0x240
	0x240: 0x26, 0x241: 0x26, 0x242: 0x26, 0x243: 0x26, 0x244: 0x26, 0x245: 0x26, 0x246: 0x26, 0x247: 0x26,
	0x248: 0x26, 0x249: 0x26, 0x24a: 0x26, 0x24b: 0x26, 0x24c: 0x26, 0x24d: 0x26, 0x24e: 0x26, 0x24f: 0x26,
	0x250: 0x26, 0x251: 0x26, 0x252: 0x26, 0x253: 0x26, 0x254: 0x26, 0x255: 0x26, 0x256: 0x26, 0x257: 0x26,
	0x258: 0x26, 0x259: 0x26, 0x25a: 0x26, 0x25b: 0x26, 0x25c: 0x26, 0x25d: 0x26, 0x25e: 0x26, 0x25f: 0x26,
	0x260: 0x26, 0x261: 0x26, 0x262: 0x26, 0x263: 0x26, 0x264: 0x26, 0x265: 0x26, 0x266: 0x26, 0x267: 0x26,
	0x268: 0x26, 0x269: 0x26, 0x26a: 0x26, 0x26b: 0x26, 0x26c: 0x26, 0x26d: 0x26, 0x26e: 0x26, 0x26f: 0x26,
	0x270: 0x26, 0x271: 0x26, 0x272: 0x26, 0x273: 0x26, 0x274: 0x26, 0x275: 0x26, 0x276: 0x26, 0x277: 0x26,
	0x278: 0x26, 0x279: 0x26, 0x27a: 0x26, 0x27b: 0x26, 0x27c: 0x26, 0x27d: 0x26, 0x27e: 0x26, 0x27f: 0x26,
	// Block 0xa, offset 0x280
	0x280: 0x26, 0x281: 0x26, 0x282: 0x26, 0x283: 0x26, 0x284: 0x26, 0x285: 0x26, 0x286: 0x26, 0x287: 0x26,
	0x288: 0x26, 0x289: 0x26, 0x28a: 0x26, 0x28b: 0x26, 0x28c: 0x26, 0x28d: 0x26, 0x28e: 0x26, 0x28f: 0x26,
	0x290: 0x26, 0x291: 0x26, 0x292: 0x26, 0x293: 0x26, 0x294: 0x26, 0x295: 0x26, 0x296: 0x26, 0x297: 0x26,
	0x298: 0x26, 0x299: 0x26, 0x29a: 0x26, 0x29b: 0x26, 0x29c: 0x26, 0x29d: 0x26, 0x29e: 0xa3, 0x29f: 0xa4,
	// Block 0xb, offset 0x2c0
	0x2ec: 0x12, 0x2ed: 0xa5, 0x2ee: 0xa6, 0x2ef: 0xa7,
	0x2f0: 0x26, 0x2f1: 0x26, 0x2f2: 0x26, 0x2f3: 0x26, 0x2f4: 0xa8, 0x2f5: 0xa9, 0x2f6: 0xaa, 0x2f7: 0xab,
	0x2f8: 0xac, 0x2f9: 0xad, 0x2fa: 0x26, 0x2fb: 0xae, 0x2fc: 0xaf, 0x2fd: 0xb0, 0x2fe: 0xb1, 0x2ff: 0xb2,
	// Block 0xc, offset 0x300
	0x300: 0xb3, 0x301: 0xb4, 0x302: 0x26, 0x303: 0xb5, 0x305: 0xb6, 0x307: 0xb7,
	0x30a: 0xb8, 0x30b: 0xb9, 0x30c: 0xba, 0x30d: 0xbb, 0x30e: 0xbc, 0x30f: 0xbd,
	0x310: 0xbe, 0x311: 0xbf, 0x312: 0xc0, 0x313: 0xc1, 0x314: 0xc2, 0x315: 0xc3, 0x316: 0x13,
	0x318: 0x26, 0x319: 0x26, 0x31a: 0x26, 0x31b: 0x26, 0x31c: 0xc4, 0x31d: 0xc5, 0x31e: 0xc6,
	0x320: 0xc7, 0x321: 0xc8, 0x322: 0xc9, 0x323: 0xca, 0x324: 0xcb, 0x326: 0xcc,
	0x328: 0xcd, 0x329: 0xce, 0x32a: 0xcf, 0x32b: 0xd0, 0x32c: 0x60, 0x32d: 0xd1, 0x32e: 0xd2,
	0x330: 0x26, 0x331: 0xd3, 0x332: 0xd4, 0x333: 0xd5, 0x334: 0xd6,
	0x33a: 0xd7, 0x33b: 0xd8, 0x33c: 0xd9, 0x33d: 0xda, 0x33e: 0xdb, 0x33f: 0xdc,
	// Block 0xd, offset 0x340
	0x340: 0xdd, 0x341: 0xde, 0x342: 0xdf, 0x343: 0xe0, 0x344: 0xe1, 0x345: 0xe2, 0x346: 0xe3, 0x347: 0xe4,
	0x348: 0xe5, 0x349: 0xe6, 0x34a: 0xe7, 0x34b: 0xe8, 0x34c: 0xe9, 0x34d: 0xea,
	0x350: 0xeb, 0x351: 0xec, 0x352: 0xed, 0x353: 0xee, 0x356: 0xef, 0x357: 0xf0,
	0x358: 0xf1, 0x359: 0xf2, 0x35a: 0xf3, 0x35b: 0xf4, 0x35c: 0xf5,
	0x360: 0xf6, 0x362: 0xf7, 0x363: 0xf8, 0x364: 0xf9, 0x365: 0xfa, 0x366: 0xfb, 0x367: 0xfc,
	0x368: 0xfd, 0x369: 0xfe, 0x36a: 0xff, 0x36b: 0x100,
	0x370: 0x101, 0x371: 0x102, 0x372: 0x103, 0x374: 0x104, 0x375: 0x105, 0x376: 0x106,
	0x37b: 0x107, 0x37c: 0x108, 0x37d: 0x109, 0x37e: 0x10a,
	// Block 0xe, offset 0x380
	0x380: 0x26, 0x381: 0x26, 0x382: 0x26, 0x383: 0x26, 0x384: 0x26, 0x385: 0x26, 0x386: 0x26, 0x387: 0x26,
	0x388: 0x26, 0x389: 0x26, 0x38a: 0x26, 0x38b: 0x26, 0x38c: 0x26, 0x38d: 0x26, 0x38e: 0x10b,
	0x390: 0x26, 0x391: 0x10c, 0x392: 0x26, 0x393: 0x26, 0x394: 0x26, 0x395: 0x10d,
	0x3be: 0xa9, 0x3bf: 0x10e,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x26, 0x3c1: 0x26, 0x3c2: 0x26, 0x3c3: 0x26, 0x3c4: 0x26, 0x3c5: 0x26, 0x3c6: 0x26, 0x3c7: 0x26,
	0x3c8: 0x26, 0x3c9: 0x26, 0x3ca: 0x26, 0x3cb: 0x26, 0x3cc: 0x26, 0x3cd: 0x26, 0x3ce: 0x26, 0x3cf: 0x26,
	0x3d0: 0x10f, 0x3d1: 0x110,
	// Block 0x10, offset 0x400
	0x410: 0x26, 0x411: 0x26, 0x412: 0x26, 0x413: 0x26, 0x414: 0x26, 0x415: 0x26, 0x416: 0x26, 0x417: 0x26,
	0x418: 0x26, 0x419: 0x111,
	// Block 0x11, offset 0x440
	0x460: 0x26, 0x461: 0x26, 0x462: 0x26, 0x463: 0x26, 0x464: 0x26, 0x465: 0x26, 0x466: 0x26, 0x467: 0x26,
	0x468: 0x100, 0x469: 0x112, 0x46a: 0x113, 0x46b: 0x114, 0x46c: 0x115, 0x46d: 0x116, 0x46e: 0x117,
	0x479: 0x118, 0x47c: 0x26, 0x47d: 0x119, 0x47e: 0x11a, 0x47f: 0x11b,
	// Block 0x12, offset 0x480
	0x4bf: 0x11c,
	// Block 0x13, offset 0x4c0
	0x4f0: 0x26, 0x4f1: 0x11d, 0x4f2: 0x11e,
	// Block 0x14, offset 0x500
	0x53c: 0x11f, 0x53d: 0x120,
	// Block 0x15, offset 0x540
	0x545: 0x121, 0x546: 0x122,
	0x549: 0x123,
	0x550: 0x124, 0x551: 0x125, 0x552: 0x126, 0x553: 0x127, 0x554: 0x128, 0x555: 0x129, 0x556: 0x12a, 0x557: 0x12b,
	0x558: 0x12c, 0x559: 0x12d, 0x55a: 0x12e, 0x55b: 0x12f, 0x55c: 0x130, 0x55d: 0x131, 0x55e: 0x132, 0x55f: 0x133,
	0x568: 0x134, 0x569: 0x135, 0x56a: 0x136,
	0x57c: 0x137,
	// Block 0x16, offset 0x580
	0x580: 0x138, 0x581: 0x139, 0x582: 0x13a, 0x584: 0x13b, 0x585: 0x13c,
	0x58a: 0x13d, 0x58b: 0x13e,
	0x593: 0x13f,
	0x59f: 0x140,
	0x5a0: 0x26, 0x5a1: 0x26, 0x5a2: 0x26, 0x5a3: 0x141, 0x5a4: 0x14, 0x5a5: 0x142,
	0x5b8: 0x143, 0x5b9: 0x15, 0x5ba: 0x144,
	// Block 0x17, offset 0x5c0
	0x5c4: 0x145, 0x5c5: 0x146, 0x5c6: 0x147,
	0x5cf: 0x148,
	0x5ef: 0x149,
	// Block 0x18, offset 0x600
	0x610: 0x0a, 0x611: 0x0b, 0x612: 0x0c, 0x613: 0x0d, 0x614: 0x0e, 0x616: 0x0f,
	0x61a: 0x10, 0x61b: 0x11, 0x61c: 0x12, 0x61d: 0x13, 0x61e: 0x14, 0x61f: 0x15,
	// Block 0x19, offset 0x640
	0x640: 0x14a, 0x641: 0x14b, 0x644: 0x14b, 0x645: 0x14b, 0x646: 0x14b, 0x647: 0x14c,
	// Block 0x1a, offset 0x680
	0x6a0: 0x17,
}

// sparseOffsets: 312 entries, 624 bytes
var sparseOffsets = []uint16{0x0, 0x9, 0xf, 0x18, 0x24, 0x2e, 0x34, 0x37, 0x3b, 0x3e, 0x42, 0x4c, 0x4e, 0x57, 0x5e, 0x63, 0x71, 0x72, 0x80, 0x8f, 0x99, 0x9c, 0xa3, 0xab, 0xaf, 0xb7, 0xbd, 0xcb, 0xd6, 0xe3, 0xee, 0xfa, 0x104, 0x110, 0x11b, 0x127, 0x133, 0x13b, 0x145, 0x150, 0x15b, 0x167, 0x16d, 0x178, 0x17e, 0x186, 0x189, 0x18e, 0x192, 0x196, 0x19d, 0x1a6, 0x1ae, 0x1af, 0x1b8, 0x1bf, 0x1c7, 0x1cd, 0x1d2, 0x1d6, 0x1d9, 0x1db, 0x1de, 0x1e3, 0x1e4, 0x1e6, 0x1e8, 0x1ea, 0x1f1, 0x1f6, 0x1fa, 0x203, 0x206, 0x209, 0x20f, 0x210, 0x21b, 0x21c, 0x21d, 0x222, 0x22f, 0x238, 0x23e, 0x246, 0x24f, 0x258, 0x261, 0x266, 0x269, 0x274, 0x282, 0x284, 0x28b, 0x28f, 0x29b, 0x29c, 0x2a7, 0x2af, 0x2b7, 0x2bd, 0x2be, 0x2cc, 0x2d1, 0x2d4, 0x2d9, 0x2dd, 0x2e3, 0x2e8, 0x2eb, 0x2f0, 0x2f5, 0x2f6, 0x2fc, 0x2fe, 0x2ff, 0x301, 0x303, 0x306, 0x307, 0x309, 0x30c, 0x312, 0x316, 0x318, 0x31d, 0x324, 0x334, 0x33e, 0x33f, 0x348, 0x34c, 0x351, 0x359, 0x35f, 0x365, 0x36f, 0x374, 0x37d, 0x383, 0x38c, 0x390, 0x398, 0x39a, 0x39c, 0x39f, 0x3a1, 0x3a3, 0x3a4, 0x3a5, 0x3a7, 0x3a9, 0x3af, 0x3b4, 0x3b6, 0x3bd, 0x3c0, 0x3c2, 0x3c8, 0x3cd, 0x3cf, 0x3d0, 0x3d1, 0x3d2, 0x3d4, 0x3d6, 0x3d8, 0x3db, 0x3dd, 0x3e0, 0x3e8, 0x3eb, 0x3ef, 0x3f7, 0x3f9, 0x409, 0x40a, 0x40c, 0x411, 0x417, 0x419, 0x41a, 0x41c, 0x41e, 0x420, 0x42d, 0x42e, 0x42f, 0x433, 0x435, 0x436, 0x437, 0x438, 0x439, 0x43c, 0x43f, 0x440, 0x443, 0x44a, 0x450, 0x452, 0x456, 0x45e, 0x464, 0x468, 0x46f, 0x473, 0x477, 0x480, 0x48a, 0x48c, 0x492, 0x498, 0x4a2, 0x4ac, 0x4ae, 0x4b7, 0x4bd, 0x4c3, 0x4c9, 0x4cc, 0x4d2, 0x4d5, 0x4de, 0x4df, 0x4e6, 0x4ea, 0x4eb, 0x4ee, 0x4f8, 0x4fb, 0x4fd, 0x504, 0x50c, 0x512, 0x519, 0x51a, 0x520, 0x523, 0x52b, 0x532, 0x53c, 0x544, 0x547, 0x54c, 0x550, 0x551, 0x552, 0x553, 0x554, 0x555, 0x557, 0x55a, 0x55b, 0x55e, 0x55f, 0x562, 0x564, 0x568, 0x569, 0x56b, 0x56e, 0x570, 0x573, 0x576, 0x578, 0x57d, 0x57f, 0x580, 0x585, 0x589, 0x58a, 0x58d, 0x591, 0x59c, 0x5a0, 0x5a8, 0x5ad, 0x5b1, 0x5b4, 0x5b8, 0x5bb, 0x5be, 0x5c3, 0x5c7, 0x5cb, 0x5cf, 0x5d3, 0x5d5, 0x5d7, 0x5da, 0x5de, 0x5e4, 0x5e5, 0x5e6, 0x5e9, 0x5eb, 0x5ed, 0x5f0, 0x5f5, 0x5f9, 0x5fb, 0x601, 0x60a, 0x60f, 0x610, 0x613, 0x614, 0x615, 0x616, 0x618, 0x619, 0x61a}

// sparseValues: 1562 entries, 6248 bytes
var sparseValues = [1562]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0004, lo: 0xa8, hi: 0xa8},
	{value: 0x0012, lo: 0xaa, hi: 0xaa},
	{value: 0x0014, lo: 0xad, hi: 0xad},
	{value: 0x0004, lo: 0xaf, hi: 0xaf},
	{value: 0x0004, lo: 0xb4, hi: 0xb4},
	{value: 0x001a, lo: 0xb5, hi: 0xb5},
	{value: 0x0054, lo: 0xb7, hi: 0xb7},
	{value: 0x0004, lo: 0xb8, hi: 0xb8},
	{value: 0x0012, lo: 0xba, hi: 0xba},
	// Block 0x1, offset 0x9
	{value: 0x2013, lo: 0x80, hi: 0x96},
	{value: 0x2013, lo: 0x98, hi: 0x9e},
	{value: 0x009a, lo: 0x9f, hi: 0x9f},
	{value: 0x2012, lo: 0xa0, hi: 0xb6},
	{value: 0x2012, lo: 0xb8, hi: 0xbe},
	{value: 0x0252, lo: 0xbf, hi: 0xbf},
	// Block 0x2, offset 0xf
	{value: 0x0117, lo: 0x80, hi: 0xaf},
	{value: 0x011b, lo: 0xb0, hi: 0xb0},
	{value: 0x019a, lo: 0xb1, hi: 0xb1},
	{value: 0x0117, lo: 0xb2, hi: 0xb7},
	{value: 0x0012, lo: 0xb8, hi: 0xb8},
	{value: 0x0316, lo: 0xb9, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x0316, lo: 0xbd, hi: 0xbe},
	{value: 0x0553, lo: 0xbf, hi: 0xbf},
	// Block 0x3, offset 0x18
	{value: 0x0552, lo: 0x80, hi: 0x80},
	{value: 0x0316, lo: 0x81, hi: 0x82},
	{value: 0x0716, lo: 0x83, hi: 0x84},
	{value: 0x0316, lo: 0x85, hi: 0x86},
	{value: 0x0f16, lo: 0x87, hi: 0x88},
	{value: 0x01da, lo: 0x89, hi: 0x89},
	{value: 0x0117, lo: 0x8a, hi: 0xb7},
	{value: 0x0253, lo: 0xb8, hi: 0xb8},
	{value: 0x0316, lo: 0xb9, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x0316, lo: 0xbd, hi: 0xbe},
	{value: 0x028a, lo: 0xbf, hi: 0xbf},
	// Block 0x4, offset 0x24
	{value: 0x0117, lo: 0x80, hi: 0x9f},
	{value: 0x2f53, lo: 0xa0, hi: 0xa0},
	{value: 0x0012, lo: 0xa1, hi: 0xa1},
	{value: 0x0117, lo: 0xa2, hi: 0xb3},
	{value: 0x0012, lo: 0xb4, hi: 0xb9},
	{value: 0x090b, lo: 0xba, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x2953, lo: 0xbd, hi: 0xbd},
	{value: 0x098b, lo: 0xbe, hi: 0xbe},
	{value: 0x0a0a, lo: 0xbf, hi: 0xbf},
	// Block 0x5, offset 0x2e
	{value: 0x0015, lo: 0x80, hi: 0x81},
	{value: 0x0014, lo: 0x82, hi: 0x97},
	{value: 0x0004, lo: 0x98, hi: 0x9d},
	{value: 0x0014, lo: 0x9e, hi: 0x9f},
	{value: 0x0015, lo: 0xa0, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xbf},
	// Block 0x6, offset 0x34
	{value: 0x0024, lo: 0x80, hi: 0x94},
	{value: 0x0034, lo: 0x95, hi: 0xbc},
	{value: 0x0024, lo: 0xbd, hi: 0xbf},
	// Block 0x7, offset 0x37
	{value: 0x6553, lo: 0x80, hi: 0x8f},
	{value: 0x2013, lo: 0x90, hi: 0x9f},
	{value: 0x5f53, lo: 0xa0, hi: 0xaf},
	{value: 0x2012, lo: 0xb0, hi: 0xbf},
	// Block 0x8, offset 0x3b
	{value: 0x5f52, lo: 0x80, hi: 0x8f},
	{value: 0x6552, lo: 0x90, hi: 0x9f},
	{value: 0x0117, lo: 0xa0, hi: 0xbf},
	// Block 0x9, offset 0x3e
	{value: 0x0117, lo: 0x80, hi: 0x81},
	{value: 0x0024, lo: 0x83, hi: 0x87},
	{value: 0x0014, lo: 0x88, hi: 0x89},
	{value: 0x0117, lo: 0x8a, hi: 0xbf},
	// Block 0xa, offset 0x42
	{value: 0x0f13, lo: 0x80, hi: 0x80},
	{value: 0x0316, lo: 0x81, hi: 0x82},
	{value: 0x0716, lo: 0x83, hi: 0x84},
	{value: 0x0316, lo: 0x85, hi: 0x86},
	{value: 0x0f16, lo: 0x87, hi: 0x88},
	{value: 0x0316, lo: 0x89, hi: 0x8a},
	{value: 0x0716, lo: 0x8b, hi: 0x8c},
	{value: 0x0316, lo: 0x8d, hi: 0x8e},
	{value: 0x0f12, lo: 0x8f, hi: 0x8f},
	{value: 0x0117, lo: 0x90, hi: 0xbf},
	// Block 0xb, offset 0x4c
	{value: 0x0117, lo: 0x80, hi: 0xaf},
	{value: 0x6553, lo: 0xb1, hi: 0xbf},
	// Block 0xc, offset 0x4e
	{value: 0x3013, lo: 0x80, hi: 0x8f},
	{value: 0x6853, lo: 0x90, hi: 0x96},
	{value: 0x0014, lo: 0x99, hi: 0x99},
	{value: 0x0010, lo: 0x9a, hi: 0x9c},
	{value: 0x0010, lo: 0x9e, hi: 0x9e},
	{value: 0x0054, lo: 0x9f, hi: 0x9f},
	{value: 0x0012, lo: 0xa0, hi: 0xa0},
	{value: 0x6552, lo: 0xa1, hi: 0xaf},
	{value: 0x3012, lo: 0xb0, hi: 0xbf},
	// Block 0xd, offset 0x57
	{value: 0x0034, lo: 0x81, hi: 0x82},
	{value: 0x0024, lo: 0x84, hi: 0x84},
	{value: 0x0034, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0xaa},
	{value: 0x0010, lo: 0xaf, hi: 0xb3},
	{value: 0x0054, lo: 0xb4, hi: 0xb4},
	// Block 0xe, offset 0x5e
	{value: 0x0014, lo: 0x80, hi: 0x85},
	{value: 0x0024, lo: 0x90, hi: 0x97},
	{value: 0x0034, lo: 0x98, hi: 0x9a},
	{value: 0x0014, lo: 0x9c, hi: 0x9c},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0xf, offset 0x63
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x8a},
	{value: 0x0034, lo: 0x8b, hi: 0x92},
	{value: 0x0024, lo: 0x93, hi: 0x94},
	{value: 0x0034, lo: 0x95, hi: 0x96},
	{value: 0x0024, lo: 0x97, hi: 0x9b},
	{value: 0x0034, lo: 0x9c, hi: 0x9c},
	{value: 0x0024, lo: 0x9d, hi: 0x9e},
	{value: 0x0034, lo: 0x9f, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0010, lo: 0xab, hi: 0xab},
	{value: 0x0010, lo: 0xae, hi: 0xaf},
	{value: 0x0034, lo: 0xb0, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xbf},
	// Block 0x10, offset 0x71
	{value: 0x0010, lo: 0x80, hi: 0xbf},
	// Block 0x11, offset 0x72
	{value: 0x0010, lo: 0x80, hi: 0x93},
	{value: 0x0010, lo: 0x95, hi: 0x95},
	{value: 0x0024, lo: 0x96, hi: 0x9c},
	{value: 0x0014, lo: 0x9d, hi: 0x9d},
	{value: 0x0024, lo: 0x9f, hi: 0xa2},
	{value: 0x0034, lo: 0xa3, hi: 0xa3},
	{value: 0x0024, lo: 0xa4, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xa6},
	{value: 0x0024, lo: 0xa7, hi: 0xa8},
	{value: 0x0034, lo: 0xaa, hi: 0xaa},
	{value: 0x0024, lo: 0xab, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xbc},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x12, offset 0x80
	{value: 0x0014, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0034, lo: 0x91, hi: 0x91},
	{value: 0x0010, lo: 0x92, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb0},
	{value: 0x0034, lo: 0xb1, hi: 0xb1},
	{value: 0x0024, lo: 0xb2, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0024, lo: 0xb5, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb9},
	{value: 0x0024, lo: 0xba, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbc},
	{value: 0x0024, lo: 0xbd, hi: 0xbd},
	{value: 0x0034, lo: 0xbe, hi: 0xbe},
	{value: 0x0024, lo: 0xbf, hi: 0xbf},
	// Block 0x13, offset 0x8f
	{value: 0x0024, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0024, lo: 0x83, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x84},
	{value: 0x0024, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0024, lo: 0x87, hi: 0x87},
	{value: 0x0034, lo: 0x88, hi: 0x88},
	{value: 0x0024, lo: 0x89, hi: 0x8a},
	{value: 0x0010, lo: 0x8d, hi: 0xbf},
	// Block 0x14, offset 0x99
	{value: 0x0010, lo: 0x80, hi: 0xa5},
	{value: 0x0014, lo: 0xa6, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	// Block 0x15, offset 0x9c
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0024, lo: 0xab, hi: 0xb1},
	{value: 0x0034, lo: 0xb2, hi: 0xb2},
	{value: 0x0024, lo: 0xb3, hi: 0xb3},
	{value: 0x0014, lo: 0xb4, hi: 0xb5},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0034, lo: 0xbd, hi: 0xbd},
	// Block 0x16, offset 0xa3
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0024, lo: 0x96, hi: 0x99},
	{value: 0x0014, lo: 0x9a, hi: 0x9a},
	{value: 0x0024, lo: 0x9b, hi: 0xa3},
	{value: 0x0014, lo: 0xa4, hi: 0xa4},
	{value: 0x0024, lo: 0xa5, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa8},
	{value: 0x0024, lo: 0xa9, hi: 0xad},
	// Block 0x17, offset 0xab
	{value: 0x0010, lo: 0x80, hi: 0x98},
	{value: 0x0034, lo: 0x99, hi: 0x9b},
	{value: 0x0010, lo: 0xa0, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x18, offset 0xaf
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0004, lo: 0x88, hi: 0x88},
	{value: 0x0010, lo: 0x89, hi: 0x8e},
	{value: 0x0014, lo: 0x90, hi: 0x91},
	{value: 0x0024, lo: 0x98, hi: 0x98},
	{value: 0x0034, lo: 0x99, hi: 0x9b},
	{value: 0x0024, lo: 0x9c, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x19, offset 0xb7
	{value: 0x0014, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0xb9},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x1a, offset 0xbd
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x88},
	{value: 0x0010, lo: 0x89, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0024, lo: 0x91, hi: 0x91},
	{value: 0x0034, lo: 0x92, hi: 0x92},
	{value: 0x0024, lo: 0x93, hi: 0x94},
	{value: 0x0014, lo: 0x95, hi: 0x97},
	{value: 0x0010, lo: 0x98, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0014, lo: 0xb1, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xbf},
	// Block 0x1b, offset 0xcb
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb2},
	{value: 0x0010, lo: 0xb6, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x1c, offset 0xd6
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8e, hi: 0x8e},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x9c, hi: 0x9d},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xb1},
	{value: 0x0010, lo: 0xbc, hi: 0xbc},
	{value: 0x0024, lo: 0xbe, hi: 0xbe},
	// Block 0x1d, offset 0xe3
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8a},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb6},
	{value: 0x0010, lo: 0xb8, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x1e, offset 0xee
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0014, lo: 0x87, hi: 0x88},
	{value: 0x0014, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0014, lo: 0x91, hi: 0x91},
	{value: 0x0010, lo: 0x99, hi: 0x9c},
	{value: 0x0010, lo: 0x9e, hi: 0x9e},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb5},
	// Block 0x1f, offset 0xfa
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8d},
	{value: 0x0010, lo: 0x8f, hi: 0x91},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x20, offset 0x104
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x85},
	{value: 0x0014, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x89, hi: 0x89},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb9, hi: 0xb9},
	{value: 0x0014, lo: 0xba, hi: 0xbf},
	// Block 0x21, offset 0x110
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x22, offset 0x11b
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0014, lo: 0x95, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x9c, hi: 0x9d},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	// Block 0x23, offset 0x127
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8a},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0x95},
	{value: 0x0010, lo: 0x99, hi: 0x9a},
	{value: 0x0010, lo: 0x9c, hi: 0x9c},
	{value: 0x0010, lo: 0x9e, hi: 0x9f},
	{value: 0x0010, lo: 0xa3, hi: 0xa4},
	{value: 0x0010, lo: 0xa8, hi: 0xaa},
	{value: 0x0010, lo: 0xae, hi: 0xb9},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x24, offset 0x133
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x82},
	{value: 0x0010, lo: 0x86, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	// Block 0x25, offset 0x13b
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x83},
	{value: 0x0014, lo: 0x84, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	{value: 0x0014, lo: 0xbe, hi: 0xbf},
	// Block 0x26, offset 0x145
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x84},
	{value: 0x0014, lo: 0x86, hi: 0x88},
	{value: 0x0014, lo: 0x8a, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0034, lo: 0x95, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x9a},
	{value: 0x0010, lo: 0x9d, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	// Block 0x27, offset 0x150
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x28, offset 0x15b
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0014, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x95, hi: 0x96},
	{value: 0x0010, lo: 0x9d, hi: 0x9e},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb1, hi: 0xb3},
	// Block 0x29, offset 0x167
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x2a, offset 0x16d
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x86, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8e, hi: 0x8e},
	{value: 0x0010, lo: 0x94, hi: 0x97},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xba, hi: 0xbf},
	// Block 0x2b, offset 0x178
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x96},
	{value: 0x0010, lo: 0x9a, hi: 0xb1},
	{value: 0x0010, lo: 0xb3, hi: 0xbb},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	// Block 0x2c, offset 0x17e
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0010, lo: 0x8f, hi: 0x91},
	{value: 0x0014, lo: 0x92, hi: 0x94},
	{value: 0x0014, lo: 0x96, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x9f},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	// Block 0x2d, offset 0x186
	{value: 0x0014, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb4, hi: 0xb7},
	{value: 0x0034, lo: 0xb8, hi: 0xba},
	// Block 0x2e, offset 0x189
	{value: 0x0004, lo: 0x86, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x87},
	{value: 0x0034, lo: 0x88, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x2f, offset 0x18e
	{value: 0x0014, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb4, hi: 0xb7},
	{value: 0x0034, lo: 0xb8, hi: 0xba},
	{value: 0x0014, lo: 0xbb, hi: 0xbc},
	// Block 0x30, offset 0x192
	{value: 0x0004, lo: 0x86, hi: 0x86},
	{value: 0x0034, lo: 0x88, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x31, offset 0x196
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0034, lo: 0x98, hi: 0x99},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0034, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	{value: 0x0034, lo: 0xb9, hi: 0xb9},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x32, offset 0x19d
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0010, lo: 0x89, hi: 0xac},
	{value: 0x0034, lo: 0xb1, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xba, hi: 0xbd},
	{value: 0x0014, lo: 0xbe, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x33, offset 0x1a6
	{value: 0x0034, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0024, lo: 0x82, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x84},
	{value: 0x0024, lo: 0x86, hi: 0x87},
	{value: 0x0010, lo: 0x88, hi: 0x8c},
	{value: 0x0014, lo: 0x8d, hi: 0x97},
	{value: 0x0014, lo: 0x99, hi: 0xbc},
	// Block 0x34, offset 0x1ae
	{value: 0x0034, lo: 0x86, hi: 0x86},
	// Block 0x35, offset 0x1af
	{value: 0x0010, lo: 0xab, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	{value: 0x0010, lo: 0xb8, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbc},
	{value: 0x0014, lo: 0xbd, hi: 0xbe},
	// Block 0x36, offset 0x1b8
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x96, hi: 0x97},
	{value: 0x0014, lo: 0x98, hi: 0x99},
	{value: 0x0014, lo: 0x9e, hi: 0xa0},
	{value: 0x0010, lo: 0xa2, hi: 0xa4},
	{value: 0x0010, lo: 0xa7, hi: 0xad},
	{value: 0x0014, lo: 0xb1, hi: 0xb4},
	// Block 0x37, offset 0x1bf
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x84},
	{value: 0x0014, lo: 0x85, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8f, hi: 0x9c},
	{value: 0x0014, lo: 0x9d, hi: 0x9d},
	{value: 0x6c53, lo: 0xa0, hi: 0xbf},
	// Block 0x38, offset 0x1c7
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x98},
	{value: 0x0010, lo: 0x9a, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x39, offset 0x1cd
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb5},
	{value: 0x0010, lo: 0xb8, hi: 0xbe},
	// Block 0x3a, offset 0x1d2
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x82, hi: 0x85},
	{value: 0x0010, lo: 0x88, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0xbf},
	// Block 0x3b, offset 0x1d6
	{value: 0x0010, lo: 0x80, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0x95},
	{value: 0x0010, lo: 0x98, hi: 0xbf},
	// Block 0x3c, offset 0x1d9
	{value: 0x0010, lo: 0x80, hi: 0x9a},
	{value: 0x0024, lo: 0x9d, hi: 0x9f},
	// Block 0x3d, offset 0x1db
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	{value: 0x7453, lo: 0xa0, hi: 0xaf},
	{value: 0x7853, lo: 0xb0, hi: 0xbf},
	// Block 0x3e, offset 0x1de
	{value: 0x7c53, lo: 0x80, hi: 0x8f},
	{value: 0x8053, lo: 0x90, hi: 0x9f},
	{value: 0x7c53, lo: 0xa0, hi: 0xaf},
	{value: 0x0813, lo: 0xb0, hi: 0xb5},
	{value: 0x0892, lo: 0xb8, hi: 0xbd},
	// Block 0x3f, offset 0x1e3
	{value: 0x0010, lo: 0x81, hi: 0xbf},
	// Block 0x40, offset 0x1e4
	{value: 0x0010, lo: 0x80, hi: 0xac},
	{value: 0x0010, lo: 0xaf, hi: 0xbf},
	// Block 0x41, offset 0x1e6
	{value: 0x0010, lo: 0x81, hi: 0x9a},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x42, offset 0x1e8
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0010, lo: 0xae, hi: 0xb8},
	// Block 0x43, offset 0x1ea
	{value: 0x0010, lo: 0x80, hi: 0x91},
	{value: 0x0014, lo: 0x92, hi: 0x93},
	{value: 0x0034, lo: 0x94, hi: 0x94},
	{value: 0x0030, lo: 0x95, hi: 0x95},
	{value: 0x0010, lo: 0x9f, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb3},
	{value: 0x0030, lo: 0xb4, hi: 0xb4},
	// Block 0x44, offset 0x1f1
	{value: 0x0010, lo: 0x80, hi: 0x91},
	{value: 0x0014, lo: 0x92, hi: 0x93},
	{value: 0x0010, lo: 0xa0, hi: 0xac},
	{value: 0x0010, lo: 0xae, hi: 0xb0},
	{value: 0x0014, lo: 0xb2, hi: 0xb3},
	// Block 0x45, offset 0x1f6
	{value: 0x0014, lo: 0xb4, hi: 0xb5},
	{value: 0x0010, lo: 0xb6, hi: 0xb6},
	{value: 0x0014, lo: 0xb7, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x46, offset 0x1fa
	{value: 0x0010, lo: 0x80, hi: 0x85},
	{value: 0x0014, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0014, lo: 0x89, hi: 0x91},
	{value: 0x0034, lo: 0x92, hi: 0x92},
	{value: 0x0014, lo: 0x93, hi: 0x93},
	{value: 0x0004, lo: 0x97, hi: 0x97},
	{value: 0x0024, lo: 0x9d, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	// Block 0x47, offset 0x203
	{value: 0x0014, lo: 0x8b, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x48, offset 0x206
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0xb8},
	// Block 0x49, offset 0x209
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0014, lo: 0x85, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0xa8},
	{value: 0x0034, lo: 0xa9, hi: 0xa9},
	{value: 0x0010, lo: 0xaa, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x4a, offset 0x20f
	{value: 0x0010, lo: 0x80, hi: 0xb5},
	// Block 0x4b, offset 0x210
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	{value: 0x0014, lo: 0xa0, hi: 0xa2},
	{value: 0x0010, lo: 0xa3, hi: 0xa6},
	{value: 0x0014, lo: 0xa7, hi: 0xa8},
	{value: 0x0010, lo: 0xa9, hi: 0xab},
	{value: 0x0010, lo: 0xb0, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb2},
	{value: 0x0010, lo: 0xb3, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xb9},
	{value: 0x0024, lo: 0xba, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbb},
	// Block 0x4c, offset 0x21b
	{value: 0x0010, lo: 0x86, hi: 0x8f},
	// Block 0x4d, offset 0x21c
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x4e, offset 0x21d
	{value: 0x0010, lo: 0x80, hi: 0x96},
	{value: 0x0024, lo: 0x97, hi: 0x97},
	{value: 0x0034, lo: 0x98, hi: 0x98},
	{value: 0x0010, lo: 0x99, hi: 0x9a},
	{value: 0x0014, lo: 0x9b, hi: 0x9b},
	// Block 0x4f, offset 0x222
	{value: 0x0010, lo: 0x95, hi: 0x95},
	{value: 0x0014, lo: 0x96, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0014, lo: 0x98, hi: 0x9e},
	{value: 0x0034, lo: 0xa0, hi: 0xa0},
	{value: 0x0010, lo: 0xa1, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa2},
	{value: 0x0010, lo: 0xa3, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xac},
	{value: 0x0010, lo: 0xad, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb4},
	{value: 0x0024, lo: 0xb5, hi: 0xbc},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x50, offset 0x22f
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0004, lo: 0xa7, hi: 0xa7},
	{value: 0x0024, lo: 0xb0, hi: 0xb4},
	{value: 0x0034, lo: 0xb5, hi: 0xba},
	{value: 0x0024, lo: 0xbb, hi: 0xbc},
	{value: 0x0034, lo: 0xbd, hi: 0xbd},
	{value: 0x0014, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x51, offset 0x238
	{value: 0x0034, lo: 0x80, hi: 0x80},
	{value: 0x0024, lo: 0x81, hi: 0x82},
	{value: 0x0034, lo: 0x83, hi: 0x84},
	{value: 0x0024, lo: 0x85, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0024, lo: 0x8b, hi: 0x8e},
	// Block 0x52, offset 0x23e
	{value: 0x0014, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x53, offset 0x246
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0030, lo: 0x84, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0024, lo: 0xab, hi: 0xab},
	{value: 0x0034, lo: 0xac, hi: 0xac},
	{value: 0x0024, lo: 0xad, hi: 0xb3},
	// Block 0x54, offset 0x24f
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa5},
	{value: 0x0010, lo: 0xa6, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa9},
	{value: 0x0030, lo: 0xaa, hi: 0xaa},
	{value: 0x0034, lo: 0xab, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xbf},
	// Block 0x55, offset 0x258
	{value: 0x0010, lo: 0x80, hi: 0xa5},
	{value: 0x0034, lo: 0xa6, hi: 0xa6},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa9},
	{value: 0x0010, lo: 0xaa, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xae},
	{value: 0x0014, lo: 0xaf, hi: 0xb1},
	{value: 0x0030, lo: 0xb2, hi: 0xb3},
	// Block 0x56, offset 0x261
	{value: 0x0010, lo: 0x80, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xb3},
	{value: 0x0010, lo: 0xb4, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	// Block 0x57, offset 0x266
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x8d, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbd},
	// Block 0x58, offset 0x269
	{value: 0x31ea, lo: 0x80, hi: 0x80},
	{value: 0x326a, lo: 0x81, hi: 0x81},
	{value: 0x32ea, lo: 0x82, hi: 0x82},
	{value: 0x336a, lo: 0x83, hi: 0x83},
	{value: 0x33ea, lo: 0x84, hi: 0x84},
	{value: 0x346a, lo: 0x85, hi: 0x85},
	{value: 0x34ea, lo: 0x86, hi: 0x86},
	{value: 0x356a, lo: 0x87, hi: 0x87},
	{value: 0x35ea, lo: 0x88, hi: 0x88},
	{value: 0x8353, lo: 0x90, hi: 0xba},
	{value: 0x8353, lo: 0xbd, hi: 0xbf},
	// Block 0x59, offset 0x274
	{value: 0x0024, lo: 0x90, hi: 0x92},
	{value: 0x0034, lo: 0x94, hi: 0x99},
	{value: 0x0024, lo: 0x9a, hi: 0x9b},
	{value: 0x0034, lo: 0x9c, hi: 0x9f},
	{value: 0x0024, lo: 0xa0, hi: 0xa0},
	{value: 0x0010, lo: 0xa1, hi: 0xa1},
	{value: 0x0034, lo: 0xa2, hi: 0xa8},
	{value: 0x0010, lo: 0xa9, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xb3},
	{value: 0x0024, lo: 0xb4, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb7},
	{value: 0x0024, lo: 0xb8, hi: 0xb9},
	{value: 0x0010, lo: 0xba, hi: 0xba},
	// Block 0x5a, offset 0x282
	{value: 0x0012, lo: 0x80, hi: 0xab},
	{value: 0x0015, lo: 0xac, hi: 0xbf},
	// Block 0x5b, offset 0x284
	{value: 0x0015, lo: 0x80, hi: 0xaa},
	{value: 0x0012, lo: 0xab, hi: 0xb7},
	{value: 0x0015, lo: 0xb8, hi: 0xb8},
	{value: 0x8752, lo: 0xb9, hi: 0xb9},
	{value: 0x0012, lo: 0xba, hi: 0xbc},
	{value: 0x8b52, lo: 0xbd, hi: 0xbd},
	{value: 0x0012, lo: 0xbe, hi: 0xbf},
	// Block 0x5c, offset 0x28b
	{value: 0x0012, lo: 0x80, hi: 0x8d},
	{value: 0x8f52, lo: 0x8e, hi: 0x8e},
	{value: 0x0012, lo: 0x8f, hi: 0x9a},
	{value: 0x0015, lo: 0x9b, hi: 0xbf},
	// Block 0x5d, offset 0x28f
	{value: 0x0024, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0024, lo: 0x83, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0024, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x90},
	{value: 0x0024, lo: 0x91, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xba},
	{value: 0x0024, lo: 0xbb, hi: 0xbb},
	{value: 0x0034, lo: 0xbc, hi: 0xbd},
	{value: 0x0024, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x5e, offset 0x29b
	{value: 0x0117, lo: 0x80, hi: 0xbf},
	// Block 0x5f, offset 0x29c
	{value: 0x0117, lo: 0x80, hi: 0x95},
	{value: 0x369a, lo: 0x96, hi: 0x96},
	{value: 0x374a, lo: 0x97, hi: 0x97},
	{value: 0x37fa, lo: 0x98, hi: 0x98},
	{value: 0x38aa, lo: 0x99, hi: 0x99},
	{value: 0x395a, lo: 0x9a, hi: 0x9a},
	{value: 0x3a0a, lo: 0x9b, hi: 0x9b},
	{value: 0x0012, lo: 0x9c, hi: 0x9d},
	{value: 0x3abb, lo: 0x9e, hi: 0x9e},
	{value: 0x0012, lo: 0x9f, hi: 0x9f},
	{value: 0x0117, lo: 0xa0, hi: 0xbf},
	// Block 0x60, offset 0x2a7
	{value: 0x0812, lo: 0x80, hi: 0x87},
	{value: 0x0813, lo: 0x88, hi: 0x8f},
	{value: 0x0812, lo: 0x90, hi: 0x95},
	{value: 0x0813, lo: 0x98, hi: 0x9d},
	{value: 0x0812, lo: 0xa0, hi: 0xa7},
	{value: 0x0813, lo: 0xa8, hi: 0xaf},
	{value: 0x0812, lo: 0xb0, hi: 0xb7},
	{value: 0x0813, lo: 0xb8, hi: 0xbf},
	// Block 0x61, offset 0x2af
	{value: 0x0004, lo: 0x8b, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8f},
	{value: 0x0054, lo: 0x98, hi: 0x99},
	{value: 0x0054, lo: 0xa4, hi: 0xa4},
	{value: 0x0054, lo: 0xa7, hi: 0xa7},
	{value: 0x0014, lo: 0xaa, hi: 0xae},
	{value: 0x0010, lo: 0xaf, hi: 0xaf},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x62, offset 0x2b7
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x94, hi: 0x94},
	{value: 0x0014, lo: 0xa0, hi: 0xa4},
	{value: 0x0014, lo: 0xa6, hi: 0xaf},
	{value: 0x0015, lo: 0xb1, hi: 0xb1},
	{value: 0x0015, lo: 0xbf, hi: 0xbf},
	// Block 0x63, offset 0x2bd
	{value: 0x0015, lo: 0x90, hi: 0x9c},
	// Block 0x64, offset 0x2be
	{value: 0x0024, lo: 0x90, hi: 0x91},
	{value: 0x0034, lo: 0x92, hi: 0x93},
	{value: 0x0024, lo: 0x94, hi: 0x97},
	{value: 0x0034, lo: 0x98, hi: 0x9a},
	{value: 0x0024, lo: 0x9b, hi: 0x9c},
	{value: 0x0014, lo: 0x9d, hi: 0xa0},
	{value: 0x0024, lo: 0xa1, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa4},
	{value: 0x0034, lo: 0xa5, hi: 0xa6},
	{value: 0x0024, lo: 0xa7, hi: 0xa7},
	{value: 0x0034, lo: 0xa8, hi: 0xa8},
	{value: 0x0024, lo: 0xa9, hi: 0xa9},
	{value: 0x0034, lo: 0xaa, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb0},
	// Block 0x65, offset 0x2cc
	{value: 0x0016, lo: 0x85, hi: 0x86},
	{value: 0x0012, lo: 0x87, hi: 0x89},
	{value: 0xa452, lo: 0x8e, hi: 0x8e},
	{value: 0x1013, lo: 0xa0, hi: 0xaf},
	{value: 0x1012, lo: 0xb0, hi: 0xbf},
	// Block 0x66, offset 0x2d1
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0716, lo: 0x83, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x88},
	// Block 0x67, offset 0x2d4
	{value: 0xa753, lo: 0xb6, hi: 0xb7},
	{value: 0xaa53, lo: 0xb8, hi: 0xb9},
	{value: 0xad53, lo: 0xba, hi: 0xbb},
	{value: 0xaa53, lo: 0xbc, hi: 0xbd},
	{value: 0xa753, lo: 0xbe, hi: 0xbf},
	// Block 0x68, offset 0x2d9
	{value: 0x3013, lo: 0x80, hi: 0x8f},
	{value: 0x6553, lo: 0x90, hi: 0x9f},
	{value: 0xb053, lo: 0xa0, hi: 0xaf},
	{value: 0x3012, lo: 0xb0, hi: 0xbf},
	// Block 0x69, offset 0x2dd
	{value: 0x0117, lo: 0x80, hi: 0xa3},
	{value: 0x0012, lo: 0xa4, hi: 0xa4},
	{value: 0x0716, lo: 0xab, hi: 0xac},
	{value: 0x0316, lo: 0xad, hi: 0xae},
	{value: 0x0024, lo: 0xaf, hi: 0xb1},
	{value: 0x0117, lo: 0xb2, hi: 0xb3},
	// Block 0x6a, offset 0x2e3
	{value: 0x6c52, lo: 0x80, hi: 0x9f},
	{value: 0x7052, lo: 0xa0, hi: 0xa5},
	{value: 0x7052, lo: 0xa7, hi: 0xa7},
	{value: 0x7052, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x6b, offset 0x2e8
	{value: 0x0010, lo: 0x80, hi: 0xa7},
	{value: 0x0014, lo: 0xaf, hi: 0xaf},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x6c, offset 0x2eb
	{value: 0x0010, lo: 0x80, hi: 0x96},
	{value: 0x0010, lo: 0xa0, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xae},
	{value: 0x0010, lo: 0xb0, hi: 0xb6},
	{value: 0x0010, lo: 0xb8, hi: 0xbe},
	// Block 0x6d, offset 0x2f0
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x88, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x9e},
	{value: 0x0024, lo: 0xa0, hi: 0xbf},
	// Block 0x6e, offset 0x2f5
	{value: 0x0014, lo: 0xaf, hi: 0xaf},
	// Block 0x6f, offset 0x2f6
	{value: 0x0014, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0xaa, hi: 0xad},
	{value: 0x0030, lo: 0xae, hi: 0xaf},
	{value: 0x0004, lo: 0xb1, hi: 0xb5},
	{value: 0x0014, lo: 0xbb, hi: 0xbb},
	{value: 0x0010, lo: 0xbc, hi: 0xbc},
	// Block 0x70, offset 0x2fc
	{value: 0x0034, lo: 0x99, hi: 0x9a},
	{value: 0x0004, lo: 0x9b, hi: 0x9e},
	// Block 0x71, offset 0x2fe
	{value: 0x0004, lo: 0xbc, hi: 0xbe},
	// Block 0x72, offset 0x2ff
	{value: 0x0010, lo: 0x85, hi: 0xaf},
	{value: 0x0010, lo: 0xb1, hi: 0xbf},
	// Block 0x73, offset 0x301
	{value: 0x0010, lo: 0x80, hi: 0x8e},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x74, offset 0x303
	{value: 0x0010, lo: 0x80, hi: 0x94},
	{value: 0x0014, lo: 0x95, hi: 0x95},
	{value: 0x0010, lo: 0x96, hi: 0xbf},
	// Block 0x75, offset 0x306
	{value: 0x0010, lo: 0x80, hi: 0x8c},
	// Block 0x76, offset 0x307
	{value: 0x0010, lo: 0x90, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbd},
	// Block 0x77, offset 0x309
	{value: 0x0010, lo: 0x80, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0010, lo: 0x90, hi: 0xab},
	// Block 0x78, offset 0x30c
	{value: 0x0117, lo: 0x80, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xae},
	{value: 0x0024, lo: 0xaf, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb2},
	{value: 0x0024, lo: 0xb4, hi: 0xbd},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x79, offset 0x312
	{value: 0x0117, lo: 0x80, hi: 0x9b},
	{value: 0x0015, lo: 0x9c, hi: 0x9d},
	{value: 0x0024, lo: 0x9e, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x7a, offset 0x316
	{value: 0x0010, lo: 0x80, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb1},
	// Block 0x7b, offset 0x318
	{value: 0x0004, lo: 0x80, hi: 0x87},
	{value: 0x0014, lo: 0x88, hi: 0xa1},
	{value: 0x0117, lo: 0xa2, hi: 0xaf},
	{value: 0x0012, lo: 0xb0, hi: 0xb1},
	{value: 0x0117, lo: 0xb2, hi: 0xbf},
	// Block 0x7c, offset 0x31d
	{value: 0x0117, lo: 0x80, hi: 0xaf},
	{value: 0x0015, lo: 0xb0, hi: 0xb0},
	{value: 0x0012, lo: 0xb1, hi: 0xb8},
	{value: 0x0316, lo: 0xb9, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x8753, lo: 0xbd, hi: 0xbd},
	{value: 0x0117, lo: 0xbe, hi: 0xbf},
	// Block 0x7d, offset 0x324
	{value: 0x0117, lo: 0x80, hi: 0x83},
	{value: 0x6553, lo: 0x84, hi: 0x84},
	{value: 0x908b, lo: 0x85, hi: 0x85},
	{value: 0x8f53, lo: 0x86, hi: 0x86},
	{value: 0x0f16, lo: 0x87, hi: 0x88},
	{value: 0x0316, lo: 0x89, hi: 0x8a},
	{value: 0x0117, lo: 0x90, hi: 0x91},
	{value: 0x0012, lo: 0x93, hi: 0x93},
	{value: 0x0012, lo: 0x95, hi: 0x95},
	{value: 0x0117, lo: 0x96, hi: 0x99},
	{value: 0x0015, lo: 0xb2, hi: 0xb4},
	{value: 0x0316, lo: 0xb5, hi: 0xb6},
	{value: 0x0010, lo: 0xb7, hi: 0xb7},
	{value: 0x0015, lo: 0xb8, hi: 0xb9},
	{value: 0x0012, lo: 0xba, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbf},
	// Block 0x7e, offset 0x334
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x8a},
	{value: 0x0014, lo: 0x8b, hi: 0x8b},
	{value: 0x0010, lo: 0x8c, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xa6},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	{value: 0x0034, lo: 0xac, hi: 0xac},
	// Block 0x7f, offset 0x33e
	{value: 0x0010, lo: 0x80, hi: 0xb3},
	// Block 0x80, offset 0x33f
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x84},
	{value: 0x0014, lo: 0x85, hi: 0x85},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0024, lo: 0xa0, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xb7},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0010, lo: 0xbd, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x81, offset 0x348
	{value: 0x0010, lo: 0x80, hi: 0xa5},
	{value: 0x0014, lo: 0xa6, hi: 0xaa},
	{value: 0x0034, lo: 0xab, hi: 0xad},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x82, offset 0x34c
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x91},
	{value: 0x0010, lo: 0x92, hi: 0x92},
	{value: 0x0030, lo: 0x93, hi: 0x93},
	{value: 0x0010, lo: 0xa0, hi: 0xbc},
	// Block 0x83, offset 0x351
	{value: 0x0014, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0xb2},
	{value: 0x0034, lo: 0xb3, hi: 0xb3},
	{value: 0x0010, lo: 0xb4, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xb9},
	{value: 0x0010, lo: 0xba, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x84, offset 0x359
	{value: 0x0030, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0014, lo: 0xa5, hi: 0xa5},
	{value: 0x0004, lo: 0xa6, hi: 0xa6},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x85, offset 0x35f
	{value: 0x0010, lo: 0x80, hi: 0xa8},
	{value: 0x0014, lo: 0xa9, hi: 0xae},
	{value: 0x0010, lo: 0xaf, hi: 0xb0},
	{value: 0x0014, lo: 0xb1, hi: 0xb2},
	{value: 0x0010, lo: 0xb3, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb6},
	// Block 0x86, offset 0x365
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0010, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0004, lo: 0xb0, hi: 0xb0},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	// Block 0x87, offset 0x36f
	{value: 0x0024, lo: 0xb0, hi: 0xb0},
	{value: 0x0024, lo: 0xb2, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0024, lo: 0xb7, hi: 0xb8},
	{value: 0x0024, lo: 0xbe, hi: 0xbf},
	// Block 0x88, offset 0x374
	{value: 0x0024, lo: 0x81, hi: 0x81},
	{value: 0x0004, lo: 0x9d, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xaf},
	{value: 0x0010, lo: 0xb2, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xb6},
	// Block 0x89, offset 0x37d
	{value: 0x0010, lo: 0x81, hi: 0x86},
	{value: 0x0010, lo: 0x89, hi: 0x8e},
	{value: 0x0010, lo: 0x91, hi: 0x96},
	{value: 0x0010, lo: 0xa0, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xae},
	{value: 0x0012, lo: 0xb0, hi: 0xbf},
	// Block 0x8a, offset 0x383
	{value: 0x0012, lo: 0x80, hi: 0x92},
	{value: 0xb352, lo: 0x93, hi: 0x93},
	{value: 0x0012, lo: 0x94, hi: 0x9a},
	{value: 0x0014, lo: 0x9b, hi: 0x9b},
	{value: 0x0015, lo: 0x9c, hi: 0x9f},
	{value: 0x0012, lo: 0xa0, hi: 0xa8},
	{value: 0x0015, lo: 0xa9, hi: 0xa9},
	{value: 0x0004, lo: 0xaa, hi: 0xab},
	{value: 0x74d2, lo: 0xb0, hi: 0xbf},
	// Block 0x8b, offset 0x38c
	{value: 0x78d2, lo: 0x80, hi: 0x8f},
	{value: 0x7cd2, lo: 0x90, hi: 0x9f},
	{value: 0x80d2, lo: 0xa0, hi: 0xaf},
	{value: 0x7cd2, lo: 0xb0, hi: 0xbf},
	// Block 0x8c, offset 0x390
	{value: 0x0010, lo: 0x80, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xa5},
	{value: 0x0010, lo: 0xa6, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa8},
	{value: 0x0010, lo: 0xa9, hi: 0xaa},
	{value: 0x0010, lo: 0xac, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x8d, offset 0x398
	{value: 0x0010, lo: 0x80, hi: 0xa3},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x8e, offset 0x39a
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x8b, hi: 0xbb},
	// Block 0x8f, offset 0x39c
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x83, hi: 0x84},
	{value: 0x0010, lo: 0x86, hi: 0xbf},
	// Block 0x90, offset 0x39f
	{value: 0x0010, lo: 0x80, hi: 0xb1},
	{value: 0x0004, lo: 0xb2, hi: 0xbf},
	// Block 0x91, offset 0x3a1
	{value: 0x0004, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x93, hi: 0xbf},
	// Block 0x92, offset 0x3a3
	{value: 0x0010, lo: 0x80, hi: 0xbd},
	// Block 0x93, offset 0x3a4
	{value: 0x0010, lo: 0x90, hi: 0xbf},
	// Block 0x94, offset 0x3a5
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	{value: 0x0010, lo: 0x92, hi: 0xbf},
	// Block 0x95, offset 0x3a7
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0010, lo: 0xb0, hi: 0xbb},
	// Block 0x96, offset 0x3a9
	{value: 0x0014, lo: 0x80, hi: 0x8f},
	{value: 0x0054, lo: 0x93, hi: 0x93},
	{value: 0x0024, lo: 0xa0, hi: 0xa6},
	{value: 0x0034, lo: 0xa7, hi: 0xad},
	{value: 0x0024, lo: 0xae, hi: 0xaf},
	{value: 0x0010, lo: 0xb3, hi: 0xb4},
	// Block 0x97, offset 0x3af
	{value: 0x0010, lo: 0x8d, hi: 0x8f},
	{value: 0x0054, lo: 0x92, hi: 0x92},
	{value: 0x0054, lo: 0x95, hi: 0x95},
	{value: 0x0010, lo: 0xb0, hi: 0xb4},
	{value: 0x0010, lo: 0xb6, hi: 0xbf},
	// Block 0x98, offset 0x3b4
	{value: 0x0010, lo: 0x80, hi: 0xbc},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x99, offset 0x3b6
	{value: 0x0054, lo: 0x87, hi: 0x87},
	{value: 0x0054, lo: 0x8e, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0054, lo: 0x9a, hi: 0x9a},
	{value: 0x5f53, lo: 0xa1, hi: 0xba},
	{value: 0x0004, lo: 0xbe, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x9a, offset 0x3bd
	{value: 0x0004, lo: 0x80, hi: 0x80},
	{value: 0x5f52, lo: 0x81, hi: 0x9a},
	{value: 0x0004, lo: 0xb0, hi: 0xb0},
	// Block 0x9b, offset 0x3c0
	{value: 0x0014, lo: 0x9e, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xbe},
	// Block 0x9c, offset 0x3c2
	{value: 0x0010, lo: 0x82, hi: 0x87},
	{value: 0x0010, lo: 0x8a, hi: 0x8f},
	{value: 0x0010, lo: 0x92, hi: 0x97},
	{value: 0x0010, lo: 0x9a, hi: 0x9c},
	{value: 0x0004, lo: 0xa3, hi: 0xa3},
	{value: 0x0014, lo: 0xb9, hi: 0xbb},
	// Block 0x9d, offset 0x3c8
	{value: 0x0010, lo: 0x80, hi: 0x8b},
	{value: 0x0010, lo: 0x8d, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xba},
	{value: 0x0010, lo: 0xbc, hi: 0xbd},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x9e, offset 0x3cd
	{value: 0x0010, lo: 0x80, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x9d},
	// Block 0x9f, offset 0x3cf
	{value: 0x0010, lo: 0x80, hi: 0xba},
	// Block 0xa0, offset 0x3d0
	{value: 0x0010, lo: 0x80, hi: 0xb4},
	// Block 0xa1, offset 0x3d1
	{value: 0x0034, lo: 0xbd, hi: 0xbd},
	// Block 0xa2, offset 0x3d2
	{value: 0x0010, lo: 0x80, hi: 0x9c},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0xa3, offset 0x3d4
	{value: 0x0010, lo: 0x80, hi: 0x90},
	{value: 0x0034, lo: 0xa0, hi: 0xa0},
	// Block 0xa4, offset 0x3d6
	{value: 0x0010, lo: 0x80, hi: 0x9f},
	{value: 0x0010, lo: 0xad, hi: 0xbf},
	// Block 0xa5, offset 0x3d8
	{value: 0x0010, lo: 0x80, hi: 0x8a},
	{value: 0x0010, lo: 0x90, hi: 0xb5},
	{value: 0x0024, lo: 0xb6, hi: 0xba},
	// Block 0xa6, offset 0x3db
	{value: 0x0010, lo: 0x80, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0xa7, offset 0x3dd
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x88, hi: 0x8f},
	{value: 0x0010, lo: 0x91, hi: 0x95},
	// Block 0xa8, offset 0x3e0
	{value: 0x2813, lo: 0x80, hi: 0x87},
	{value: 0x3813, lo: 0x88, hi: 0x8f},
	{value: 0x2813, lo: 0x90, hi: 0x97},
	{value: 0xb653, lo: 0x98, hi: 0x9f},
	{value: 0xb953, lo: 0xa0, hi: 0xa7},
	{value: 0x2812, lo: 0xa8, hi: 0xaf},
	{value: 0x3812, lo: 0xb0, hi: 0xb7},
	{value: 0x2812, lo: 0xb8, hi: 0xbf},
	// Block 0xa9, offset 0x3e8
	{value: 0xb652, lo: 0x80, hi: 0x87},
	{value: 0xb952, lo: 0x88, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0xbf},
	// Block 0xaa, offset 0x3eb
	{value: 0x0010, lo: 0x80, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0xb953, lo: 0xb0, hi: 0xb7},
	{value: 0xb653, lo: 0xb8, hi: 0xbf},
	// Block 0xab, offset 0x3ef
	{value: 0x2813, lo: 0x80, hi: 0x87},
	{value: 0x3813, lo: 0x88, hi: 0x8f},
	{value: 0x2813, lo: 0x90, hi: 0x93},
	{value: 0xb952, lo: 0x98, hi: 0x9f},
	{value: 0xb652, lo: 0xa0, hi: 0xa7},
	{value: 0x2812, lo: 0xa8, hi: 0xaf},
	{value: 0x3812, lo: 0xb0, hi: 0xb7},
	{value: 0x2812, lo: 0xb8, hi: 0xbb},
	// Block 0xac, offset 0x3f7
	{value: 0x0010, lo: 0x80, hi: 0xa7},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xad, offset 0x3f9
	{value: 0x0010, lo: 0x80, hi: 0xa3},
	{value: 0xbc53, lo: 0xb0, hi: 0xb0},
	{value: 0xbf53, lo: 0xb1, hi: 0xb1},
	{value: 0xc253, lo: 0xb2, hi: 0xb2},
	{value: 0xbf53, lo: 0xb3, hi: 0xb3},
	{value: 0xc553, lo: 0xb4, hi: 0xb4},
	{value: 0xbf53, lo: 0xb5, hi: 0xb5},
	{value: 0xc253, lo: 0xb6, hi: 0xb6},
	{value: 0xbf53, lo: 0xb7, hi: 0xb7},
	{value: 0xbc53, lo: 0xb8, hi: 0xb8},
	{value: 0xc853, lo: 0xb9, hi: 0xb9},
	{value: 0xcb53, lo: 0xba, hi: 0xba},
	{value: 0xce53, lo: 0xbc, hi: 0xbc},
	{value: 0xc853, lo: 0xbd, hi: 0xbd},
	{value: 0xcb53, lo: 0xbe, hi: 0xbe},
	{value: 0xc853, lo: 0xbf, hi: 0xbf},
	// Block 0xae, offset 0x409
	{value: 0x0010, lo: 0x80, hi: 0xb6},
	// Block 0xaf, offset 0x40a
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xa7},
	// Block 0xb0, offset 0x40c
	{value: 0x0015, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0015, lo: 0x83, hi: 0x85},
	{value: 0x0015, lo: 0x87, hi: 0xb0},
	{value: 0x0015, lo: 0xb2, hi: 0xba},
	// Block 0xb1, offset 0x411
	{value: 0x0010, lo: 0x80, hi: 0x85},
	{value: 0x0010, lo: 0x88, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0xb5},
	{value: 0x0010, lo: 0xb7, hi: 0xb8},
	{value: 0x0010, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xb2, offset 0x417
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xb6},
	// Block 0xb3, offset 0x419
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	// Block 0xb4, offset 0x41a
	{value: 0x0010, lo: 0xa0, hi: 0xb2},
	{value: 0x0010, lo: 0xb4, hi: 0xb5},
	// Block 0xb5, offset 0x41c
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xb9},
	// Block 0xb6, offset 0x41e
	{value: 0x0010, lo: 0x80, hi: 0xb7},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0xb7, offset 0x420
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x83},
	{value: 0x0014, lo: 0x85, hi: 0x86},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0014, lo: 0x8e, hi: 0x8e},
	{value: 0x0024, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x93},
	{value: 0x0010, lo: 0x95, hi: 0x97},
	{value: 0x0010, lo: 0x99, hi: 0xb5},
	{value: 0x0024, lo: 0xb8, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xb8, offset 0x42d
	{value: 0x0010, lo: 0xa0, hi: 0xbc},
	// Block 0xb9, offset 0x42e
	{value: 0x0010, lo: 0x80, hi: 0x9c},
	// Block 0xba, offset 0x42f
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0010, lo: 0x89, hi: 0xa4},
	{value: 0x0024, lo: 0xa5, hi: 0xa5},
	{value: 0x0034, lo: 0xa6, hi: 0xa6},
	// Block 0xbb, offset 0x433
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xb2},
	// Block 0xbc, offset 0x435
	{value: 0x0010, lo: 0x80, hi: 0x91},
	// Block 0xbd, offset 0x436
	{value: 0x0010, lo: 0x80, hi: 0x88},
	// Block 0xbe, offset 0x437
	{value: 0x5653, lo: 0x80, hi: 0xb2},
	// Block 0xbf, offset 0x438
	{value: 0x5652, lo: 0x80, hi: 0xb2},
	// Block 0xc0, offset 0x439
	{value: 0x0010, lo: 0x80, hi: 0xa3},
	{value: 0x0024, lo: 0xa4, hi: 0xa7},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xc1, offset 0x43c
	{value: 0x0010, lo: 0x80, hi: 0xa9},
	{value: 0x0024, lo: 0xab, hi: 0xac},
	{value: 0x0010, lo: 0xb0, hi: 0xb1},
	// Block 0xc2, offset 0x43f
	{value: 0x0034, lo: 0xbd, hi: 0xbf},
	// Block 0xc3, offset 0x440
	{value: 0x0010, lo: 0x80, hi: 0x9c},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xc4, offset 0x443
	{value: 0x0010, lo: 0x80, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x87},
	{value: 0x0024, lo: 0x88, hi: 0x8a},
	{value: 0x0034, lo: 0x8b, hi: 0x8b},
	{value: 0x0024, lo: 0x8c, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x90},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xc5, offset 0x44a
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0024, lo: 0x82, hi: 0x82},
	{value: 0x0034, lo: 0x83, hi: 0x83},
	{value: 0x0024, lo: 0x84, hi: 0x84},
	{value: 0x0034, lo: 0x85, hi: 0x85},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xc6, offset 0x450
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0010, lo: 0xa0, hi: 0xb6},
	// Block 0xc7, offset 0x452
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbf},
	// Block 0xc8, offset 0x456
	{value: 0x0014, lo: 0x80, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0034, lo: 0xb0, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xc9, offset 0x45e
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb6},
	{value: 0x0010, lo: 0xb7, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0014, lo: 0xbd, hi: 0xbd},
	// Block 0xca, offset 0x464
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0014, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0xa8},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xcb, offset 0x468
	{value: 0x0024, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0xa6},
	{value: 0x0014, lo: 0xa7, hi: 0xab},
	{value: 0x0010, lo: 0xac, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xb2},
	{value: 0x0034, lo: 0xb3, hi: 0xb4},
	{value: 0x0010, lo: 0xb6, hi: 0xbf},
	// Block 0xcc, offset 0x46f
	{value: 0x0010, lo: 0x84, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0xb2},
	{value: 0x0034, lo: 0xb3, hi: 0xb3},
	{value: 0x0010, lo: 0xb6, hi: 0xb6},
	// Block 0xcd, offset 0x473
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xce, offset 0x477
	{value: 0x0030, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x84},
	{value: 0x0014, lo: 0x89, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0014, lo: 0x8b, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x8e},
	{value: 0x0014, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x9a},
	{value: 0x0010, lo: 0x9c, hi: 0x9c},
	// Block 0xcf, offset 0x480
	{value: 0x0010, lo: 0x80, hi: 0x91},
	{value: 0x0010, lo: 0x93, hi: 0xae},
	{value: 0x0014, lo: 0xaf, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0014, lo: 0xb4, hi: 0xb4},
	{value: 0x0030, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xb6},
	{value: 0x0014, lo: 0xb7, hi: 0xb7},
	{value: 0x0014, lo: 0xbe, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xd0, offset 0x48a
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	// Block 0xd1, offset 0x48c
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x88, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8d},
	{value: 0x0010, lo: 0x8f, hi: 0x9d},
	{value: 0x0010, lo: 0x9f, hi: 0xa8},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xd2, offset 0x492
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	{value: 0x0014, lo: 0x9f, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa2},
	{value: 0x0014, lo: 0xa3, hi: 0xa8},
	{value: 0x0034, lo: 0xa9, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xd3, offset 0x498
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbb, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0xd4, offset 0x4a2
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0030, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x9d, hi: 0xa3},
	{value: 0x0024, lo: 0xa6, hi: 0xac},
	{value: 0x0024, lo: 0xb0, hi: 0xb4},
	// Block 0xd5, offset 0x4ac
	{value: 0x0010, lo: 0x80, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbf},
	// Block 0xd6, offset 0x4ae
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x8a},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0024, lo: 0x9e, hi: 0x9e},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	// Block 0xd7, offset 0x4b7
	{value: 0x0010, lo: 0x80, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb8},
	{value: 0x0010, lo: 0xb9, hi: 0xb9},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0xd8, offset 0x4bd
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0x85},
	{value: 0x0010, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xd9, offset 0x4c3
	{value: 0x0010, lo: 0x80, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb5},
	{value: 0x0010, lo: 0xb8, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xda, offset 0x4c9
	{value: 0x0034, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x98, hi: 0x9b},
	{value: 0x0014, lo: 0x9c, hi: 0x9d},
	// Block 0xdb, offset 0x4cc
	{value: 0x0010, lo: 0x80, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbc},
	{value: 0x0014, lo: 0xbd, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xdc, offset 0x4d2
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x84, hi: 0x84},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xdd, offset 0x4d5
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0014, lo: 0xab, hi: 0xab},
	{value: 0x0010, lo: 0xac, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb5},
	{value: 0x0030, lo: 0xb6, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	{value: 0x0010, lo: 0xb8, hi: 0xb8},
	// Block 0xde, offset 0x4de
	{value: 0x0010, lo: 0x80, hi: 0x89},
	// Block 0xdf, offset 0x4df
	{value: 0x0014, lo: 0x9d, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa5},
	{value: 0x0010, lo: 0xa6, hi: 0xa6},
	{value: 0x0014, lo: 0xa7, hi: 0xaa},
	{value: 0x0034, lo: 0xab, hi: 0xab},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xe0, offset 0x4e6
	{value: 0x0010, lo: 0x80, hi: 0xae},
	{value: 0x0014, lo: 0xaf, hi: 0xb7},
	{value: 0x0010, lo: 0xb8, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	// Block 0xe1, offset 0x4ea
	{value: 0x5f53, lo: 0xa0, hi: 0xbf},
	// Block 0xe2, offset 0x4eb
	{value: 0x5f52, lo: 0x80, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xe3, offset 0x4ee
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x89, hi: 0x89},
	{value: 0x0010, lo: 0x8c, hi: 0x93},
	{value: 0x0010, lo: 0x95, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0xb5},
	{value: 0x0010, lo: 0xb7, hi: 0xb8},
	{value: 0x0014, lo: 0xbb, hi: 0xbc},
	{value: 0x0030, lo: 0xbd, hi: 0xbd},
	{value: 0x0034, lo: 0xbe, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xe4, offset 0x4f8
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0034, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xe5, offset 0x4fb
	{value: 0x0010, lo: 0xa0, hi: 0xa7},
	{value: 0x0010, lo: 0xaa, hi: 0xbf},
	// Block 0xe6, offset 0x4fd
	{value: 0x0010, lo: 0x80, hi: 0x93},
	{value: 0x0014, lo: 0x94, hi: 0x97},
	{value: 0x0014, lo: 0x9a, hi: 0x9b},
	{value: 0x0010, lo: 0x9c, hi: 0x9f},
	{value: 0x0034, lo: 0xa0, hi: 0xa0},
	{value: 0x0010, lo: 0xa1, hi: 0xa1},
	{value: 0x0010, lo: 0xa3, hi: 0xa4},
	// Block 0xe7, offset 0x504
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x8a},
	{value: 0x0010, lo: 0x8b, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb8},
	{value: 0x0010, lo: 0xb9, hi: 0xba},
	{value: 0x0014, lo: 0xbb, hi: 0xbe},
	// Block 0xe8, offset 0x50c
	{value: 0x0034, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0014, lo: 0x91, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x98},
	{value: 0x0014, lo: 0x99, hi: 0x9b},
	{value: 0x0010, lo: 0x9c, hi: 0xbf},
	// Block 0xe9, offset 0x512
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0014, lo: 0x8a, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0014, lo: 0x98, hi: 0x98},
	{value: 0x0034, lo: 0x99, hi: 0x99},
	{value: 0x0010, lo: 0x9d, hi: 0x9d},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xea, offset 0x519
	{value: 0x0010, lo: 0x80, hi: 0xb8},
	// Block 0xeb, offset 0x51a
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb6},
	{value: 0x0014, lo: 0xb8, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xec, offset 0x520
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xb2, hi: 0xbf},
	// Block 0xed, offset 0x523
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	{value: 0x0014, lo: 0x92, hi: 0xa7},
	{value: 0x0010, lo: 0xa9, hi: 0xa9},
	{value: 0x0014, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb4, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb6},
	// Block 0xee, offset 0x52b
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x88, hi: 0x89},
	{value: 0x0010, lo: 0x8b, hi: 0xb0},
	{value: 0x0014, lo: 0xb1, hi: 0xb6},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0014, lo: 0xbc, hi: 0xbd},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0xef, offset 0x532
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x85},
	{value: 0x0010, lo: 0x86, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xa0, hi: 0xa5},
	{value: 0x0010, lo: 0xa7, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xbf},
	// Block 0xf0, offset 0x53c
	{value: 0x0010, lo: 0x80, hi: 0x8e},
	{value: 0x0014, lo: 0x90, hi: 0x91},
	{value: 0x0010, lo: 0x93, hi: 0x94},
	{value: 0x0014, lo: 0x95, hi: 0x95},
	{value: 0x0010, lo: 0x96, hi: 0x96},
	{value: 0x0034, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x98, hi: 0x98},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	// Block 0xf1, offset 0x544
	{value: 0x0010, lo: 0xa0, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb6},
	// Block 0xf2, offset 0x547
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xba},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0xf3, offset 0x54c
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0030, lo: 0x81, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xf4, offset 0x550
	{value: 0x0010, lo: 0xb0, hi: 0xb0},
	// Block 0xf5, offset 0x551
	{value: 0x0010, lo: 0x80, hi: 0x99},
	// Block 0xf6, offset 0x552
	{value: 0x0010, lo: 0x80, hi: 0xae},
	// Block 0xf7, offset 0x553
	{value: 0x0010, lo: 0x80, hi: 0x83},
	// Block 0xf8, offset 0x554
	{value: 0x0010, lo: 0x80, hi: 0xb0},
	// Block 0xf9, offset 0x555
	{value: 0x0010, lo: 0x80, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xbf},
	// Block 0xfa, offset 0x557
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x95},
	// Block 0xfb, offset 0x55a
	{value: 0x0010, lo: 0x80, hi: 0x86},
	// Block 0xfc, offset 0x55b
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xfd, offset 0x55e
	{value: 0x0010, lo: 0x80, hi: 0xbe},
	// Block 0xfe, offset 0x55f
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x90, hi: 0xad},
	{value: 0x0034, lo: 0xb0, hi: 0xb4},
	// Block 0xff, offset 0x562
	{value: 0x0010, lo: 0x80, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb6},
	// Block 0x100, offset 0x564
	{value: 0x0014, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xa3, hi: 0xb7},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x101, offset 0x568
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	// Block 0x102, offset 0x569
	{value: 0x2013, lo: 0x80, hi: 0x9f},
	{value: 0x2012, lo: 0xa0, hi: 0xbf},
	// Block 0x103, offset 0x56b
	{value: 0x0010, lo: 0x80, hi: 0x8a},
	{value: 0x0014, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0xbf},
	// Block 0x104, offset 0x56e
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0014, lo: 0x8f, hi: 0x9f},
	// Block 0x105, offset 0x570
	{value: 0x0014, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa3, hi: 0xa4},
	{value: 0x0030, lo: 0xb0, hi: 0xb1},
	// Block 0x106, offset 0x573
	{value: 0x0004, lo: 0xb0, hi: 0xb3},
	{value: 0x0004, lo: 0xb5, hi: 0xbb},
	{value: 0x0004, lo: 0xbd, hi: 0xbe},
	// Block 0x107, offset 0x576
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xbc},
	// Block 0x108, offset 0x578
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0014, lo: 0x9d, hi: 0x9d},
	{value: 0x0034, lo: 0x9e, hi: 0x9e},
	{value: 0x0014, lo: 0xa0, hi: 0xa3},
	// Block 0x109, offset 0x57d
	{value: 0x0014, lo: 0x80, hi: 0xad},
	{value: 0x0014, lo: 0xb0, hi: 0xbf},
	// Block 0x10a, offset 0x57f
	{value: 0x0014, lo: 0x80, hi: 0x86},
	// Block 0x10b, offset 0x580
	{value: 0x0030, lo: 0xa5, hi: 0xa6},
	{value: 0x0034, lo: 0xa7, hi: 0xa9},
	{value: 0x0030, lo: 0xad, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbf},
	// Block 0x10c, offset 0x585
	{value: 0x0034, lo: 0x80, hi: 0x82},
	{value: 0x0024, lo: 0x85, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8b},
	{value: 0x0024, lo: 0xaa, hi: 0xad},
	// Block 0x10d, offset 0x589
	{value: 0x0024, lo: 0x82, hi: 0x84},
	// Block 0x10e, offset 0x58a
	{value: 0x0013, lo: 0x80, hi: 0x99},
	{value: 0x0012, lo: 0x9a, hi: 0xb3},
	{value: 0x0013, lo: 0xb4, hi: 0xbf},
	// Block 0x10f, offset 0x58d
	{value: 0x0013, lo: 0x80, hi: 0x8d},
	{value: 0x0012, lo: 0x8e, hi: 0x94},
	{value: 0x0012, lo: 0x96, hi: 0xa7},
	{value: 0x0013, lo: 0xa8, hi: 0xbf},
	// Block 0x110, offset 0x591
	{value: 0x0013, lo: 0x80, hi: 0x81},
	{value: 0x0012, lo: 0x82, hi: 0x9b},
	{value: 0x0013, lo: 0x9c, hi: 0x9c},
	{value: 0x0013, lo: 0x9e, hi: 0x9f},
	{value: 0x0013, lo: 0xa2, hi: 0xa2},
	{value: 0x0013, lo: 0xa5, hi: 0xa6},
	{value: 0x0013, lo: 0xa9, hi: 0xac},
	{value: 0x0013, lo: 0xae, hi: 0xb5},
	{value: 0x0012, lo: 0xb6, hi: 0xb9},
	{value: 0x0012, lo: 0xbb, hi: 0xbb},
	{value: 0x0012, lo: 0xbd, hi: 0xbf},
	// Block 0x111, offset 0x59c
	{value: 0x0012, lo: 0x80, hi: 0x83},
	{value: 0x0012, lo: 0x85, hi: 0x8f},
	{value: 0x0013, lo: 0x90, hi: 0xa9},
	{value: 0x0012, lo: 0xaa, hi: 0xbf},
	// Block 0x112, offset 0x5a0
	{value: 0x0012, lo: 0x80, hi: 0x83},
	{value: 0x0013, lo: 0x84, hi: 0x85},
	{value: 0x0013, lo: 0x87, hi: 0x8a},
	{value: 0x0013, lo: 0x8d, hi: 0x94},
	{value: 0x0013, lo: 0x96, hi: 0x9c},
	{value: 0x0012, lo: 0x9e, hi: 0xb7},
	{value: 0x0013, lo: 0xb8, hi: 0xb9},
	{value: 0x0013, lo: 0xbb, hi: 0xbe},
	// Block 0x113, offset 0x5a8
	{value: 0x0013, lo: 0x80, hi: 0x84},
	{value: 0x0013, lo: 0x86, hi: 0x86},
	{value: 0x0013, lo: 0x8a, hi: 0x90},
	{value: 0x0012, lo: 0x92, hi: 0xab},
	{value: 0x0013, lo: 0xac, hi: 0xbf},
	// Block 0x114, offset 0x5ad
	{value: 0x0013, lo: 0x80, hi: 0x85},
	{value: 0x0012, lo: 0x86, hi: 0x9f},
	{value: 0x0013, lo: 0xa0, hi: 0xb9},
	{value: 0x0012, lo: 0xba, hi: 0xbf},
	// Block 0x115, offset 0x5b1
	{value: 0x0012, lo: 0x80, hi: 0x93},
	{value: 0x0013, lo: 0x94, hi: 0xad},
	{value: 0x0012, lo: 0xae, hi: 0xbf},
	// Block 0x116, offset 0x5b4
	{value: 0x0012, lo: 0x80, hi: 0x87},
	{value: 0x0013, lo: 0x88, hi: 0xa1},
	{value: 0x0012, lo: 0xa2, hi: 0xbb},
	{value: 0x0013, lo: 0xbc, hi: 0xbf},
	// Block 0x117, offset 0x5b8
	{value: 0x0013, lo: 0x80, hi: 0x95},
	{value: 0x0012, lo: 0x96, hi: 0xaf},
	{value: 0x0013, lo: 0xb0, hi: 0xbf},
	// Block 0x118, offset 0x5bb
	{value: 0x0013, lo: 0x80, hi: 0x89},
	{value: 0x0012, lo: 0x8a, hi: 0xa5},
	{value: 0x0013, lo: 0xa8, hi: 0xbf},
	// Block 0x119, offset 0x5be
	{value: 0x0013, lo: 0x80, hi: 0x80},
	{value: 0x0012, lo: 0x82, hi: 0x9a},
	{value: 0x0012, lo: 0x9c, hi: 0xa1},
	{value: 0x0013, lo: 0xa2, hi: 0xba},
	{value: 0x0012, lo: 0xbc, hi: 0xbf},
	// Block 0x11a, offset 0x5c3
	{value: 0x0012, lo: 0x80, hi: 0x94},
	{value: 0x0012, lo: 0x96, hi: 0x9b},
	{value: 0x0013, lo: 0x9c, hi: 0xb4},
	{value: 0x0012, lo: 0xb6, hi: 0xbf},
	// Block 0x11b, offset 0x5c7
	{value: 0x0012, lo: 0x80, hi: 0x8e},
	{value: 0x0012, lo: 0x90, hi: 0x95},
	{value: 0x0013, lo: 0x96, hi: 0xae},
	{value: 0x0012, lo: 0xb0, hi: 0xbf},
	// Block 0x11c, offset 0x5cb
	{value: 0x0012, lo: 0x80, hi: 0x88},
	{value: 0x0012, lo: 0x8a, hi: 0x8f},
	{value: 0x0013, lo: 0x90, hi: 0xa8},
	{value: 0x0012, lo: 0xaa, hi: 0xbf},
	// Block 0x11d, offset 0x5cf
	{value: 0x0012, lo: 0x80, hi: 0x82},
	{value: 0x0012, lo: 0x84, hi: 0x89},
	{value: 0x0017, lo: 0x8a, hi: 0x8b},
	{value: 0x0010, lo: 0x8e, hi: 0xbf},
	// Block 0x11e, offset 0x5d3
	{value: 0x0014, lo: 0x80, hi: 0xb6},
	{value: 0x0014, lo: 0xbb, hi: 0xbf},
	// Block 0x11f, offset 0x5d5
	{value: 0x0014, lo: 0x80, hi: 0xac},
	{value: 0x0014, lo: 0xb5, hi: 0xb5},
	// Block 0x120, offset 0x5d7
	{value: 0x0014, lo: 0x84, hi: 0x84},
	{value: 0x0014, lo: 0x9b, hi: 0x9f},
	{value: 0x0014, lo: 0xa1, hi: 0xaf},
	// Block 0x121, offset 0x5da
	{value: 0x0012, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x8a, hi: 0x8a},
	{value: 0x0012, lo: 0x8b, hi: 0x9e},
	{value: 0x0012, lo: 0xa5, hi: 0xaa},
	// Block 0x122, offset 0x5de
	{value: 0x0024, lo: 0x80, hi: 0x86},
	{value: 0x0024, lo: 0x88, hi: 0x98},
	{value: 0x0024, lo: 0x9b, hi: 0xa1},
	{value: 0x0024, lo: 0xa3, hi: 0xa4},
	{value: 0x0024, lo: 0xa6, hi: 0xaa},
	{value: 0x0015, lo: 0xb0, hi: 0xbf},
	// Block 0x123, offset 0x5e4
	{value: 0x0015, lo: 0x80, hi: 0xad},
	// Block 0x124, offset 0x5e5
	{value: 0x0024, lo: 0x8f, hi: 0x8f},
	// Block 0x125, offset 0x5e6
	{value: 0x0010, lo: 0x80, hi: 0xac},
	{value: 0x0024, lo: 0xb0, hi: 0xb6},
	{value: 0x0014, lo: 0xb7, hi: 0xbd},
	// Block 0x126, offset 0x5e9
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x8e, hi: 0x8e},
	// Block 0x127, offset 0x5eb
	{value: 0x0010, lo: 0x90, hi: 0xad},
	{value: 0x0024, lo: 0xae, hi: 0xae},
	// Block 0x128, offset 0x5ed
	{value: 0x0010, lo: 0x80, hi: 0xab},
	{value: 0x0024, lo: 0xac, hi: 0xaf},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x129, offset 0x5f0
	{value: 0x0010, lo: 0x90, hi: 0xaa},
	{value: 0x0014, lo: 0xab, hi: 0xab},
	{value: 0x0034, lo: 0xac, hi: 0xae},
	{value: 0x0024, lo: 0xaf, hi: 0xaf},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x12a, offset 0x5f5
	{value: 0x0010, lo: 0xa0, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xab},
	{value: 0x0010, lo: 0xad, hi: 0xae},
	{value: 0x0010, lo: 0xb0, hi: 0xbe},
	// Block 0x12b, offset 0x5f9
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0034, lo: 0x90, hi: 0x96},
	// Block 0x12c, offset 0x5fb
	{value: 0xd152, lo: 0x80, hi: 0x81},
	{value: 0xd452, lo: 0x82, hi: 0x83},
	{value: 0x0024, lo: 0x84, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0014, lo: 0x8b, hi: 0x8b},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x12d, offset 0x601
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x9f},
	{value: 0x0010, lo: 0xa1, hi: 0xa2},
	{value: 0x0010, lo: 0xa4, hi: 0xa4},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	{value: 0x0010, lo: 0xa9, hi: 0xb2},
	{value: 0x0010, lo: 0xb4, hi: 0xb7},
	{value: 0x0010, lo: 0xb9, hi: 0xb9},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	// Block 0x12e, offset 0x60a
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x8b, hi: 0x9b},
	{value: 0x0010, lo: 0xa1, hi: 0xa3},
	{value: 0x0010, lo: 0xa5, hi: 0xa9},
	{value: 0x0010, lo: 0xab, hi: 0xbb},
	// Block 0x12f, offset 0x60f
	{value: 0x0013, lo: 0xb0, hi: 0xbf},
	// Block 0x130, offset 0x610
	{value: 0x0013, lo: 0x80, hi: 0x89},
	{value: 0x0013, lo: 0x90, hi: 0xa9},
	{value: 0x0013, lo: 0xb0, hi: 0xbf},
	// Block 0x131, offset 0x613
	{value: 0x0013, lo: 0x80, hi: 0x89},
	// Block 0x132, offset 0x614
	{value: 0x0014, lo: 0xbb, hi: 0xbf},
	// Block 0x133, offset 0x615
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x134, offset 0x616
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0014, lo: 0xa0, hi: 0xbf},
	// Block 0x135, offset 0x618
	{value: 0x0014, lo: 0x80, hi: 0xbf},
	// Block 0x136, offset 0x619
	{value: 0x0014, lo: 0x80, hi: 0xaf},
}

// Total table size 16093 bytes (15KiB); checksum: EE91C452
