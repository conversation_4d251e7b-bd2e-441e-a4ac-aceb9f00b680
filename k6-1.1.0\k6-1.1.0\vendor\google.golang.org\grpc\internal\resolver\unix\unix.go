/*
 *
 * Copyright 2020 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Package unix implements a resolver for unix targets.
package unix

import (
	"fmt"

	"google.golang.org/grpc/internal/transport/networktype"
	"google.golang.org/grpc/resolver"
)

const unixScheme = "unix"
const unixAbstractScheme = "unix-abstract"

type builder struct {
	scheme string
}

func (b *builder) Build(target resolver.Target, cc resolver.ClientConn, _ resolver.BuildOptions) (resolver.Resolver, error) {
	if target.URL.Host != "" {
		return nil, fmt.Errorf("invalid (non-empty) authority: %v", target.URL.Host)
	}

	// gRPC was parsing the dial target manually before PR #4817, and we
	// switched to using url.Parse() in that PR. To avoid breaking existing
	// resolver implementations we ended up stripping the leading "/" from the
	// endpoint. This obviously does not work for the "unix" scheme. Hence we
	// end up using the parsed URL instead.
	endpoint := target.URL.Path
	if endpoint == "" {
		endpoint = target.URL.Opaque
	}
	addr := resolver.Address{Addr: endpoint}
	if b.scheme == unixAbstractScheme {
		// We can not prepend \0 as c++ gRPC does, as in Golang '@' is used to signify we do
		// not want trailing \0 in address.
		addr.Addr = "@" + addr.Addr
	}
	cc.UpdateState(resolver.State{Addresses: []resolver.Address{networktype.Set(addr, "unix")}})
	return &nopResolver{}, nil
}

func (b *builder) Scheme() string {
	return b.scheme
}

func (b *builder) OverrideAuthority(resolver.Target) string {
	return "localhost"
}

type nopResolver struct {
}

func (*nopResolver) ResolveNow(resolver.ResolveNowOptions) {}

func (*nopResolver) Close() {}

func init() {
	resolver.Register(&builder{scheme: unixScheme})
	resolver.Register(&builder{scheme: unixAbstractScheme})
}
