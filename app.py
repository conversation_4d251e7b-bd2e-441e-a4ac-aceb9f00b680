import streamlit as st
import json
import subprocess
import os
import time
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, List, Any
import tempfile
import shutil

# Page configuration
st.set_page_config(
    page_title="K6 Load Testing Dashboard",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-metric {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
    }
    .error-metric {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
    }
    .warning-metric {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'test_results' not in st.session_state:
    st.session_state.test_results = None
if 'test_running' not in st.session_state:
    st.session_state.test_running = False
if 'scenarios' not in st.session_state:
    st.session_state.scenarios = []

def main():
    st.markdown('<h1 class="main-header">⚡ K6 Load Testing Dashboard</h1>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["🏠 Home", "📝 Create Scenario", "⚙️ Configure Test", "🚀 Run Test", "📊 Results & Reports"]
    )
    
    if page == "🏠 Home":
        show_home_page()
    elif page == "📝 Create Scenario":
        show_scenario_page()
    elif page == "⚙️ Configure Test":
        show_config_page()
    elif page == "🚀 Run Test":
        show_run_test_page()
    elif page == "📊 Results & Reports":
        show_results_page()

def show_home_page():
    st.header("Welcome to K6 Load Testing Dashboard")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 Features")
        st.markdown("""
        - **Create Test Scenarios**: Define multiple endpoints with different HTTP methods
        - **Configure Load Parameters**: Set users, duration, ramp-up time, and more
        - **Real-time Execution**: Monitor test progress in real-time
        - **Detailed Reports**: Get comprehensive analysis with charts and graphs
        - **Error Analysis**: Identify failed endpoints and error patterns
        """)
    
    with col2:
        st.subheader("🚀 Quick Start")
        st.markdown("""
        1. **Create Scenario**: Define your test endpoints and parameters
        2. **Configure Test**: Set load testing parameters (users, duration, etc.)
        3. **Run Test**: Execute the load test and monitor progress
        4. **View Results**: Analyze detailed reports with visualizations
        """)
    
    # Check if k6 is installed
    st.subheader("🔧 System Status")
    check_k6_installation()

def check_k6_installation():
    try:
        result = subprocess.run(['k6', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            st.success(f"✅ K6 is installed: {result.stdout.strip()}")
        else:
            st.error("❌ K6 is not properly installed")
            show_k6_installation_guide()
    except (subprocess.TimeoutExpired, FileNotFoundError):
        st.error("❌ K6 is not installed or not in PATH")
        show_k6_installation_guide()

def show_k6_installation_guide():
    st.warning("Please install K6 to use this application:")
    st.code("""
# Windows (using Chocolatey)
choco install k6

# Windows (using Scoop)
scoop install k6

# macOS (using Homebrew)
brew install k6

# Linux (Debian/Ubuntu)
sudo gpg -k
sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6
    """)

def show_scenario_page():
    st.header("📝 Create Test Scenario")

    st.subheader("Add New Endpoint")

    with st.form("endpoint_form"):
        col1, col2 = st.columns(2)

        with col1:
            endpoint_name = st.text_input("Endpoint Name", placeholder="e.g., Login API")
            url = st.text_input("URL", placeholder="https://api.example.com/login")
            method = st.selectbox("HTTP Method", ["GET", "POST", "PUT", "DELETE", "PATCH"])

        with col2:
            weight = st.slider("Weight (%)", 1, 100, 10, help="Percentage of requests for this endpoint")
            timeout = st.number_input("Timeout (seconds)", 1, 300, 30)

        # Headers section
        st.subheader("Headers")
        headers_text = st.text_area(
            "Headers (JSON format)",
            value='{\n  "Content-Type": "application/json",\n  "Authorization": "Bearer token"\n}',
            height=100
        )

        # Request body section
        st.subheader("Request Body")
        body_text = st.text_area(
            "Request Body (for POST/PUT/PATCH)",
            value='{\n  "username": "test",\n  "password": "password"\n}',
            height=100
        )

        # Validation section
        st.subheader("Response Validation")
        expected_status = st.multiselect(
            "Expected Status Codes",
            [200, 201, 202, 204, 400, 401, 403, 404, 500],
            default=[200]
        )

        response_checks = st.text_area(
            "Response Checks (JavaScript)",
            value='// Example: check(response, {\n//   "status is 200": (r) => r.status === 200,\n//   "response time < 500ms": (r) => r.timings.duration < 500\n// });',
            height=80
        )

        submitted = st.form_submit_button("Add Endpoint")

        if submitted:
            try:
                headers_dict = json.loads(headers_text) if headers_text.strip() else {}
                body_dict = json.loads(body_text) if body_text.strip() and method in ["POST", "PUT", "PATCH"] else None

                endpoint = {
                    "name": endpoint_name,
                    "url": url,
                    "method": method,
                    "weight": weight,
                    "timeout": timeout,
                    "headers": headers_dict,
                    "body": body_dict,
                    "expected_status": expected_status,
                    "response_checks": response_checks
                }

                st.session_state.scenarios.append(endpoint)
                st.success(f"✅ Added endpoint: {endpoint_name}")
                st.rerun()

            except json.JSONDecodeError as e:
                st.error(f"❌ Invalid JSON format: {e}")

    # Display current scenarios
    if st.session_state.scenarios:
        st.subheader("Current Scenarios")

        for i, scenario in enumerate(st.session_state.scenarios):
            with st.expander(f"{scenario['name']} - {scenario['method']} ({scenario['weight']}%)"):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.write(f"**URL:** {scenario['url']}")
                    st.write(f"**Method:** {scenario['method']}")
                    st.write(f"**Weight:** {scenario['weight']}%")
                    st.write(f"**Timeout:** {scenario['timeout']}s")

                    if scenario['headers']:
                        st.write("**Headers:**")
                        st.json(scenario['headers'])

                    if scenario['body']:
                        st.write("**Body:**")
                        st.json(scenario['body'])

                    st.write(f"**Expected Status:** {scenario['expected_status']}")

                with col2:
                    if st.button(f"🗑️ Delete", key=f"delete_{i}"):
                        st.session_state.scenarios.pop(i)
                        st.rerun()

        # Clear all scenarios
        if st.button("🗑️ Clear All Scenarios"):
            st.session_state.scenarios = []
            st.rerun()
    else:
        st.info("No scenarios created yet. Add your first endpoint above!")

def show_config_page():
    st.header("⚙️ Configure Load Test")

    if not st.session_state.scenarios:
        st.warning("⚠️ Please create at least one scenario before configuring the test.")
        st.info("Go to the 'Create Scenario' page to add endpoints.")
        return

    st.subheader("Load Test Configuration")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("👥 User Configuration")

        load_pattern = st.selectbox(
            "Load Pattern",
            ["Constant Load", "Ramping Load", "Spike Test", "Stress Test"],
            help="Choose the load testing pattern"
        )

        if load_pattern == "Constant Load":
            vus = st.number_input("Virtual Users", 1, 10000, 10, help="Number of concurrent users")
            duration = st.text_input("Duration", "30s", help="Test duration (e.g., 30s, 5m, 1h)")

        elif load_pattern == "Ramping Load":
            st.write("**Ramp-up Configuration:**")
            ramp_up_to = st.number_input("Ramp up to users", 1, 10000, 50)
            ramp_up_duration = st.text_input("Ramp-up duration", "2m")

            st.write("**Steady State:**")
            steady_duration = st.text_input("Steady state duration", "5m")

            st.write("**Ramp-down:**")
            ramp_down_duration = st.text_input("Ramp-down duration", "1m")

        elif load_pattern == "Spike Test":
            normal_load = st.number_input("Normal load (users)", 1, 1000, 10)
            spike_load = st.number_input("Spike load (users)", 1, 10000, 100)
            spike_duration = st.text_input("Spike duration", "1m")

        elif load_pattern == "Stress Test":
            start_users = st.number_input("Start users", 1, 100, 1)
            max_users = st.number_input("Max users", 1, 10000, 1000)
            step_duration = st.text_input("Step duration", "2m")
            step_size = st.number_input("Step size (users)", 1, 100, 10)

    with col2:
        st.subheader("🔧 Advanced Options")

        iterations = st.number_input(
            "Iterations per VU",
            0, 10000, 0,
            help="Number of iterations per virtual user (0 = unlimited)"
        )

        max_redirects = st.number_input("Max Redirects", 0, 50, 10)

        user_agent = st.text_input(
            "User Agent",
            "k6-load-test/1.0",
            help="Custom User-Agent header"
        )

        think_time = st.number_input(
            "Think Time (seconds)",
            0.0, 60.0, 1.0,
            step=0.1,
            help="Pause between requests"
        )

        batch_requests = st.checkbox(
            "Batch Requests",
            help="Send requests in batches for better performance"
        )

        no_connection_reuse = st.checkbox(
            "Disable Connection Reuse",
            help="Create new connection for each request"
        )

    st.subheader("📊 Output Configuration")

    col3, col4 = st.columns(2)

    with col3:
        output_format = st.selectbox(
            "Output Format",
            ["JSON", "InfluxDB", "CSV", "Cloud"],
            help="Choose output format for results"
        )

        summary_trend_stats = st.multiselect(
            "Summary Trend Stats",
            ["avg", "min", "med", "max", "p(90)", "p(95)", "p(99)"],
            default=["avg", "min", "med", "max", "p(95)"]
        )

    with col4:
        quiet_mode = st.checkbox("Quiet Mode", help="Reduce console output")
        verbose_mode = st.checkbox("Verbose Mode", help="Increase console output")

        tags = st.text_input(
            "Tags",
            placeholder="env=prod,team=backend",
            help="Add tags to the test run (comma-separated key=value pairs)"
        )

    # Store configuration in session state
    if st.button("💾 Save Configuration"):
        config = {
            "load_pattern": load_pattern,
            "output_format": output_format,
            "summary_trend_stats": summary_trend_stats,
            "quiet_mode": quiet_mode,
            "verbose_mode": verbose_mode,
            "tags": tags,
            "iterations": iterations,
            "max_redirects": max_redirects,
            "user_agent": user_agent,
            "think_time": think_time,
            "batch_requests": batch_requests,
            "no_connection_reuse": no_connection_reuse
        }

        if load_pattern == "Constant Load":
            config.update({"vus": vus, "duration": duration})
        elif load_pattern == "Ramping Load":
            config.update({
                "ramp_up_to": ramp_up_to,
                "ramp_up_duration": ramp_up_duration,
                "steady_duration": steady_duration,
                "ramp_down_duration": ramp_down_duration
            })
        elif load_pattern == "Spike Test":
            config.update({
                "normal_load": normal_load,
                "spike_load": spike_load,
                "spike_duration": spike_duration
            })
        elif load_pattern == "Stress Test":
            config.update({
                "start_users": start_users,
                "max_users": max_users,
                "step_duration": step_duration,
                "step_size": step_size
            })

        st.session_state.test_config = config
        st.success("✅ Configuration saved successfully!")

    # Display current configuration
    if hasattr(st.session_state, 'test_config'):
        st.subheader("Current Configuration")
        st.json(st.session_state.test_config)

def generate_k6_script(scenarios: List[Dict], config: Dict) -> str:
    """Generate k6 JavaScript test script based on scenarios and configuration."""

    script_parts = []

    # Imports and setup
    script_parts.append("""import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
""")

    # Options configuration - Fix the VUs vs iterations issue
    vus = config.get("virtual_users", 1)
    duration = config.get("duration", "30s")
    
    # For constant load pattern, use duration-based execution instead of iterations
    if config.get("load_pattern") == "constant":
        options = {
            "summaryTrendStats": config.get("summary_trend_stats", ["avg", "min", "med", "max", "p(95)"]),
            "scenarios": {
                "default": {
                    "executor": "constant-vus",
                    "vus": vus,
                    "duration": duration
                }
            }
        }
    else:
        # For other patterns, ensure iterations >= VUs
        iterations = config.get("iterations", vus * 10)  # Default to 10 iterations per VU
        if iterations < vus:
            iterations = vus  # Ensure iterations >= VUs
            
        options = {
            "summaryTrendStats": config.get("summary_trend_stats", ["avg", "min", "med", "max", "p(95)"]),
            "scenarios": {
                "default": {
                    "executor": "shared-iterations",
                    "vus": vus,
                    "iterations": iterations,
                    "maxDuration": duration
                }
            }
        }

    script_parts.append(f"export const options = {json.dumps(options, indent=2)};")

    # Add scenarios data
    script_parts.append(f"""
const scenarios = {json.dumps(scenarios, indent=2)};

function getRandomScenario() {{
    const totalWeight = scenarios.reduce((sum, s) => sum + s.weight, 0);
    let random = Math.random() * totalWeight;

    for (const scenario of scenarios) {{
        random -= scenario.weight;
        if (random <= 0) {{
            return scenario;
        }}
    }}
    return scenarios[0];
}}
""")

    # Main test function
    script_parts.append(f"""
export default function() {{
    const scenario = getRandomScenario();
    
    // Prepare request
    const params = {{
        headers: scenario.headers || {{}},
        timeout: '{scenario.get("timeout", 30)}s'
    }};

    // Make HTTP request
    let response;
    if (scenario.method === 'GET') {{
        response = http.get(scenario.url, params);
    }} else if (scenario.method === 'POST') {{
        response = http.post(scenario.url, scenario.body, params);
    }} else if (scenario.method === 'PUT') {{
        response = http.put(scenario.url, scenario.body, params);
    }} else if (scenario.method === 'DELETE') {{
        response = http.del(scenario.url, params);
    }} else {{
        response = http.request(scenario.method, scenario.url, scenario.body, params);
    }}

    // Basic checks
    const isSuccess = scenario.expected_status.includes(response.status);
    check(response, {{
        [`${{scenario.name}} - Status is expected`]: (r) => scenario.expected_status.includes(r.status),
        [`${{scenario.name}} - Response time < ${{scenario.timeout}}s`]: (r) => r.timings.duration < (scenario.timeout * 1000)
    }});

    // Custom response checks
    if (scenario.response_checks && scenario.response_checks.trim()) {{
        try {{
            eval(scenario.response_checks);
        }} catch (e) {{
            console.error(`Error in custom checks for ${{scenario.name}}: ${{e.message}}`);
        }}
    }}

    // Record errors
    errorRate.add(!isSuccess);

    // Think time
    if ({config.get("think_time", 1)} > 0) {{
        sleep({config.get("think_time", 1)});
    }}
}}
""")

    return "\n".join(script_parts)

def show_run_test_page():
    st.header("🚀 Run Load Test")

    # Check prerequisites
    if not st.session_state.scenarios:
        st.error("❌ No scenarios found. Please create scenarios first.")
        return

    if not hasattr(st.session_state, 'test_config'):
        st.error("❌ No test configuration found. Please configure the test first.")
        return

    # Display test summary
    st.subheader("📋 Test Summary")

    col1, col2 = st.columns(2)

    with col1:
        st.metric("Scenarios", len(st.session_state.scenarios))
        st.metric("Load Pattern", st.session_state.test_config["load_pattern"])

        if st.session_state.test_config["load_pattern"] == "Constant Load":
            st.metric("Virtual Users", st.session_state.test_config["vus"])
            st.metric("Duration", st.session_state.test_config["duration"])

    with col2:
        st.write("**Endpoints:**")
        for scenario in st.session_state.scenarios:
            st.write(f"• {scenario['name']} ({scenario['method']}) - {scenario['weight']}%")

    # Generate and display k6 script
    st.subheader("📄 Generated K6 Script")

    try:
        k6_script = generate_k6_script(st.session_state.scenarios, st.session_state.test_config)

        with st.expander("View Generated Script", expanded=False):
            st.code(k6_script, language="javascript")

        # Test execution
        st.subheader("🎯 Execute Test")

        col3, col4 = st.columns([2, 1])

        with col3:
            if not st.session_state.test_running:
                if st.button("🚀 Start Load Test", type="primary"):
                    run_k6_test(k6_script)
            else:
                st.warning("⏳ Test is currently running...")
                if st.button("🛑 Stop Test"):
                    st.session_state.test_running = False
                    st.rerun()

        with col4:
            if st.button("💾 Save Script"):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                script_filename = f"k6_test_{timestamp}.js"

                with open(script_filename, 'w') as f:
                    f.write(k6_script)

                st.success(f"✅ Script saved as {script_filename}")

        # Real-time test monitoring
        if st.session_state.test_running:
            show_test_monitoring()

    except Exception as e:
        st.error(f"❌ Error generating script: {e}")

def run_k6_test(script_content: str):
    """Execute k6 test with the generated script."""

    st.session_state.test_running = True
    st.session_state.test_start_time = time.time()
    
    # Find K6 executable
    k6_paths_to_try = [
        'k6.exe',
        r'.\k6.exe',
        r'k6-1.1.0\k6-1.1.0\k6.exe',
        'k6',
        r'C:\ProgramData\chocolatey\bin\k6.exe',
        r'C:\Users\<USER>\scoop\apps\k6\current\k6.exe'.format(os.environ.get('USERNAME', '')),
        r'C:\Users\<USER>\scoop\shims\k6.exe'.format(os.environ.get('USERNAME', '')),
        r'C:\Program Files\k6\k6.exe',
    ]
    
    k6_path = None
    for path in k6_paths_to_try:
        try:
            if os.path.exists(path):
                result = subprocess.run([path, 'version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    k6_path = path
                    st.success(f"✅ Found K6 at: {path}")
                    break
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            continue
    
    if not k6_path:
        st.error("❌ K6 executable not found.")
        st.session_state.test_running = False
        return

    try:
        # Create temporary script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
            f.write(script_content)
            script_path = f.name

        # Create output file for JSON results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"k6_results_{timestamp}.json"

        # Build k6 command
        cmd = [
            k6_path, 'run',
            '--out', f'json={output_file}',
            script_path
        ]

        # Add configuration options
        config = st.session_state.test_config
        if config.get("quiet_mode"):
            cmd.append("--quiet")
        if config.get("verbose_mode"):
            cmd.append("--verbose")

        st.session_state.current_test_process = None
        st.session_state.test_output_file = output_file
        st.session_state.test_script_path = script_path

        # Create persistent placeholders
        status_container = st.container()
        output_container = st.container()
        
        with status_container:
            st.info(f"🚀 Starting k6 test using: {k6_path}")
            st.info(f"📝 Script: {script_path}")
            st.info(f"📊 Output: {output_file}")

        # Execute k6 test
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        st.session_state.current_test_process = process

        # Monitor test execution
        output_lines = []
        error_lines = []

        with output_container:
            st.subheader("📊 Test Output")
            output_display = st.empty()
            
            # Read output in real-time
            while process.poll() is None:
                # Read stdout
                stdout_line = process.stdout.readline()
                if stdout_line:
                    output_lines.append(f"OUT: {stdout_line.strip()}")
                
                # Read stderr
                stderr_line = process.stderr.readline()
                if stderr_line:
                    error_lines.append(f"ERR: {stderr_line.strip()}")
                
                # Update display
                all_lines = output_lines + error_lines
                if all_lines:
                    with output_display.container():
                        for line in all_lines[-15:]:  # Show last 15 lines
                            st.text(line)
                
                time.sleep(0.1)
                
                # Check if user stopped the test
                if not st.session_state.test_running:
                    process.terminate()
                    break

        # Get final result
        return_code = process.wait()
        
        # Get any remaining output
        remaining_stdout, remaining_stderr = process.communicate()
        if remaining_stdout:
            output_lines.extend([f"OUT: {line}" for line in remaining_stdout.split('\n') if line.strip()])
        if remaining_stderr:
            error_lines.extend([f"ERR: {line}" for line in remaining_stderr.split('\n') if line.strip()])

        # Display final output
        all_final_lines = output_lines + error_lines
        if all_final_lines:
            with output_display.container():
                st.text("=== FINAL OUTPUT ===")
                for line in all_final_lines[-20:]:
                    st.text(line)

        if return_code == 0:
            st.success("✅ Test completed successfully!")
            
            # Parse and store results
            if os.path.exists(output_file):
                parse_and_store_results(output_file)
            else:
                st.warning(f"⚠️ Output file not found: {output_file}")
        else:
            st.error(f"❌ Test failed with return code: {return_code}")
            if error_lines:
                st.error("Error details:")
                for error in error_lines[-5:]:
                    st.text(error)

        # Cleanup
        try:
            if os.path.exists(script_path):
                os.unlink(script_path)
        except:
            pass

        st.session_state.test_running = False

    except Exception as e:
        st.error(f"❌ Error running test: {e}")
        st.exception(e)  # Show full traceback
        st.session_state.test_running = False

        # Cleanup on error
        try:
            if 'script_path' in locals() and os.path.exists(script_path):
                os.unlink(script_path)
        except:
            pass

def show_test_monitoring():
    """Display real-time test monitoring information."""

    st.subheader("📊 Test Monitoring")

    # Create placeholders for real-time updates
    metrics_placeholder = st.empty()

    # Monitor test progress
    if hasattr(st.session_state, 'current_test_process') and st.session_state.current_test_process:
        with metrics_placeholder.container():
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Status", "Running" if st.session_state.test_running else "Stopped")

            with col2:
                st.metric("Process ID", st.session_state.current_test_process.pid if st.session_state.current_test_process else "N/A")

            with col3:
                st.metric("Duration", f"{time.time() - st.session_state.get('test_start_time', time.time()):.0f}s")

def parse_and_store_results(output_file: str):
    """Parse k6 JSON output and store results in session state."""

    try:
        results = {
            "summary": {},
            "metrics": [],
            "failed_requests": [],
            "timeline_data": [],
            "error_analysis": {}
        }

        with open(output_file, 'r') as f:
            for line in f:
                try:
                    data = json.loads(line.strip())

                    if data.get("type") == "Point":
                        metric_data = {
                            "timestamp": data.get("timestamp"),
                            "metric": data.get("metric"),
                            "data": data.get("data", {})
                        }
                        results["metrics"].append(metric_data)

                        # Collect timeline data for charts
                        if data.get("metric") in ["http_req_duration", "http_reqs", "vus"]:
                            results["timeline_data"].append({
                                "timestamp": data.get("timestamp"),
                                "metric": data.get("metric"),
                                "value": data.get("data", {}).get("value", 0),
                                "tags": data.get("data", {}).get("tags", {})
                            })

                        # Collect failed requests
                        if data.get("metric") == "http_req_failed" and data.get("data", {}).get("value", 0) > 0:
                            failed_request = {
                                "timestamp": data.get("timestamp"),
                                "url": data.get("data", {}).get("tags", {}).get("url", "Unknown"),
                                "method": data.get("data", {}).get("tags", {}).get("method", "Unknown"),
                                "status": data.get("data", {}).get("tags", {}).get("status", "Unknown"),
                                "error": data.get("data", {}).get("tags", {}).get("error", "Unknown")
                            }
                            results["failed_requests"].append(failed_request)

                    elif data.get("type") == "Metric":
                        # Summary metrics
                        metric_name = data.get("metric")
                        metric_values = data.get("data", {})

                        results["summary"][metric_name] = {
                            "type": metric_values.get("type"),
                            "contains": metric_values.get("contains"),
                            "values": metric_values.get("values", {}),
                            "thresholds": metric_values.get("thresholds", {})
                        }

                except json.JSONDecodeError:
                    continue

        # Analyze errors
        results["error_analysis"] = analyze_errors(results["failed_requests"])

        # Store results in session state
        st.session_state.test_results = results

        # Clean up output file
        if os.path.exists(output_file):
            os.unlink(output_file)

    except Exception as e:
        st.error(f"❌ Error parsing results: {e}")

def analyze_errors(failed_requests: List[Dict]) -> Dict:
    """Analyze failed requests to provide insights."""

    if not failed_requests:
        return {"total_errors": 0, "error_types": {}, "failed_endpoints": {}}

    error_analysis = {
        "total_errors": len(failed_requests),
        "error_types": {},
        "failed_endpoints": {},
        "status_codes": {},
        "timeline": []
    }

    for request in failed_requests:
        # Count error types
        error_type = request.get("error", "Unknown")
        error_analysis["error_types"][error_type] = error_analysis["error_types"].get(error_type, 0) + 1

        # Count failed endpoints
        endpoint = f"{request.get('method', 'Unknown')} {request.get('url', 'Unknown')}"
        error_analysis["failed_endpoints"][endpoint] = error_analysis["failed_endpoints"].get(endpoint, 0) + 1

        # Count status codes
        status = request.get("status", "Unknown")
        error_analysis["status_codes"][status] = error_analysis["status_codes"].get(status, 0) + 1

        # Timeline data
        error_analysis["timeline"].append({
            "timestamp": request.get("timestamp"),
            "endpoint": endpoint,
            "error": error_type,
            "status": status
        })

    return error_analysis

def show_results_page():
    st.header("📊 Results & Reports")

    if not st.session_state.test_results:
        st.info("🔍 No test results available. Run a load test first.")
        return

    results = st.session_state.test_results

    # Summary metrics
    st.subheader("📈 Test Summary")

    if results["summary"]:
        col1, col2, col3, col4 = st.columns(4)

        # Extract key metrics
        http_reqs = results["summary"].get("http_reqs", {}).get("values", {})
        http_req_duration = results["summary"].get("http_req_duration", {}).get("values", {})
        http_req_failed = results["summary"].get("http_req_failed", {}).get("values", {})
        vus_max = results["summary"].get("vus_max", {}).get("values", {})

        with col1:
            total_requests = http_reqs.get("count", 0)
            requests_per_sec = http_reqs.get("rate", 0)
            st.metric("Total Requests", f"{total_requests:,}", f"{requests_per_sec:.2f}/sec")

        with col2:
            avg_duration = http_req_duration.get("avg", 0)
            p95_duration = http_req_duration.get("p(95)", 0)
            st.metric("Avg Response Time", f"{avg_duration:.2f}ms", f"P95: {p95_duration:.2f}ms")

        with col3:
            failed_rate = http_req_failed.get("rate", 0) * 100
            failed_count = int(total_requests * http_req_failed.get("rate", 0))
            st.metric("Error Rate", f"{failed_rate:.2f}%", f"{failed_count:,} failed")

        with col4:
            max_vus = vus_max.get("value", 0)
            st.metric("Max Virtual Users", f"{max_vus:,}")

    # Performance charts
    st.subheader("📊 Performance Charts")

    if results["timeline_data"]:
        create_performance_charts(results["timeline_data"])

    # Error analysis
    if results["error_analysis"]["total_errors"] > 0:
        st.subheader("🚨 Error Analysis")
        show_error_analysis(results["error_analysis"])
    else:
        st.success("✅ No errors detected during the test!")

    # Detailed metrics table
    st.subheader("📋 Detailed Metrics")
    show_detailed_metrics(results["summary"])

    # Export options
    st.subheader("💾 Export Results")
    show_export_options(results)

def create_performance_charts(timeline_data: List[Dict]):
    """Create performance visualization charts."""

    if not timeline_data:
        st.warning("No timeline data available for charts.")
        return

    # Convert to DataFrame
    df = pd.DataFrame(timeline_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])

    # Response time chart
    duration_data = df[df['metric'] == 'http_req_duration'].copy()
    if not duration_data.empty:
        fig_duration = px.line(
            duration_data,
            x='timestamp',
            y='value',
            title='Response Time Over Time',
            labels={'value': 'Response Time (ms)', 'timestamp': 'Time'}
        )
        fig_duration.update_layout(height=400)
        st.plotly_chart(fig_duration, use_container_width=True)

    # Request rate chart
    reqs_data = df[df['metric'] == 'http_reqs'].copy()
    if not reqs_data.empty:
        # Group by time intervals for better visualization
        reqs_data['time_bucket'] = reqs_data['timestamp'].dt.floor('10S')
        reqs_grouped = reqs_data.groupby('time_bucket')['value'].sum().reset_index()

        fig_reqs = px.bar(
            reqs_grouped,
            x='time_bucket',
            y='value',
            title='Request Rate Over Time',
            labels={'value': 'Requests per 10s', 'time_bucket': 'Time'}
        )
        fig_reqs.update_layout(height=400)
        st.plotly_chart(fig_reqs, use_container_width=True)

    # Virtual users chart
    vus_data = df[df['metric'] == 'vus'].copy()
    if not vus_data.empty:
        fig_vus = px.line(
            vus_data,
            x='timestamp',
            y='value',
            title='Virtual Users Over Time',
            labels={'value': 'Virtual Users', 'timestamp': 'Time'}
        )
        fig_vus.update_layout(height=400)
        st.plotly_chart(fig_vus, use_container_width=True)

def show_error_analysis(error_analysis: Dict):
    """Display error analysis with charts and tables."""

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Error Types")
        if error_analysis["error_types"]:
            error_df = pd.DataFrame(
                list(error_analysis["error_types"].items()),
                columns=['Error Type', 'Count']
            )
            fig_errors = px.pie(
                error_df,
                values='Count',
                names='Error Type',
                title='Distribution of Error Types'
            )
            st.plotly_chart(fig_errors, use_container_width=True)

        st.subheader("Status Codes")
        if error_analysis["status_codes"]:
            status_df = pd.DataFrame(
                list(error_analysis["status_codes"].items()),
                columns=['Status Code', 'Count']
            )
            fig_status = px.bar(
                status_df,
                x='Status Code',
                y='Count',
                title='HTTP Status Code Distribution'
            )
            st.plotly_chart(fig_status, use_container_width=True)

    with col2:
        st.subheader("Failed Endpoints")
        if error_analysis["failed_endpoints"]:
            endpoint_df = pd.DataFrame(
                list(error_analysis["failed_endpoints"].items()),
                columns=['Endpoint', 'Failures']
            ).sort_values('Failures', ascending=False)

            st.dataframe(endpoint_df, use_container_width=True)

        st.subheader("Error Timeline")
        if error_analysis["timeline"]:
            timeline_df = pd.DataFrame(error_analysis["timeline"])
            timeline_df['timestamp'] = pd.to_datetime(timeline_df['timestamp'])

            # Group errors by time buckets
            timeline_df['time_bucket'] = timeline_df['timestamp'].dt.floor('1min')
            error_timeline = timeline_df.groupby('time_bucket').size().reset_index(name='error_count')

            fig_timeline = px.line(
                error_timeline,
                x='time_bucket',
                y='error_count',
                title='Errors Over Time',
                labels={'error_count': 'Error Count', 'time_bucket': 'Time'}
            )
            st.plotly_chart(fig_timeline, use_container_width=True)

def show_detailed_metrics(summary: Dict):
    """Display detailed metrics in a table format."""

    if not summary:
        st.warning("No detailed metrics available.")
        return

    metrics_data = []

    for metric_name, metric_data in summary.items():
        if isinstance(metric_data, dict) and "values" in metric_data:
            values = metric_data["values"]

            row = {
                "Metric": metric_name,
                "Type": metric_data.get("type", "N/A"),
                "Count": values.get("count", "N/A"),
                "Rate": f"{values.get('rate', 0):.2f}" if values.get('rate') else "N/A",
                "Average": f"{values.get('avg', 0):.2f}" if values.get('avg') else "N/A",
                "Min": f"{values.get('min', 0):.2f}" if values.get('min') else "N/A",
                "Max": f"{values.get('max', 0):.2f}" if values.get('max') else "N/A",
                "P90": f"{values.get('p(90)', 0):.2f}" if values.get('p(90)') else "N/A",
                "P95": f"{values.get('p(95)', 0):.2f}" if values.get('p(95)') else "N/A",
                "P99": f"{values.get('p(99)', 0):.2f}" if values.get('p(99)') else "N/A"
            }
            metrics_data.append(row)

    if metrics_data:
        df = pd.DataFrame(metrics_data)
        st.dataframe(df, use_container_width=True)
    else:
        st.warning("No metrics data to display.")

def show_export_options(results: Dict):
    """Provide options to export test results."""

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📄 Export Summary Report"):
            report = generate_summary_report(results)
            st.download_button(
                label="Download Summary Report",
                data=report,
                file_name=f"k6_summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )

    with col2:
        if st.button("📊 Export Metrics CSV"):
            csv_data = generate_metrics_csv(results)
            st.download_button(
                label="Download Metrics CSV",
                data=csv_data,
                file_name=f"k6_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

    with col3:
        if st.button("🔍 Export Error Report"):
            error_report = generate_error_report(results["error_analysis"])
            st.download_button(
                label="Download Error Report",
                data=error_report,
                file_name=f"k6_error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )

def generate_summary_report(results: Dict) -> str:
    """Generate a comprehensive summary report."""

    report_lines = []
    report_lines.append("=" * 60)
    report_lines.append("K6 LOAD TEST SUMMARY REPORT")
    report_lines.append("=" * 60)
    report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")

    # Test summary
    if results["summary"]:
        report_lines.append("TEST SUMMARY")
        report_lines.append("-" * 20)

        for metric_name, metric_data in results["summary"].items():
            if isinstance(metric_data, dict) and "values" in metric_data:
                values = metric_data["values"]
                report_lines.append(f"{metric_name}:")

                for key, value in values.items():
                    if isinstance(value, (int, float)):
                        report_lines.append(f"  {key}: {value:.2f}")
                    else:
                        report_lines.append(f"  {key}: {value}")
                report_lines.append("")

    # Error analysis
    if results["error_analysis"]["total_errors"] > 0:
        report_lines.append("ERROR ANALYSIS")
        report_lines.append("-" * 20)
        report_lines.append(f"Total Errors: {results['error_analysis']['total_errors']}")
        report_lines.append("")

        report_lines.append("Error Types:")
        for error_type, count in results["error_analysis"]["error_types"].items():
            report_lines.append(f"  {error_type}: {count}")
        report_lines.append("")

        report_lines.append("Failed Endpoints:")
        for endpoint, count in results["error_analysis"]["failed_endpoints"].items():
            report_lines.append(f"  {endpoint}: {count} failures")
        report_lines.append("")

    # Recommendations
    report_lines.append("RECOMMENDATIONS")
    report_lines.append("-" * 20)

    if results["summary"].get("http_req_duration", {}).get("values", {}).get("avg", 0) > 1000:
        report_lines.append("• High average response time detected. Consider optimizing backend performance.")

    if results["error_analysis"]["total_errors"] > 0:
        error_rate = (results["error_analysis"]["total_errors"] /
                     results["summary"].get("http_reqs", {}).get("values", {}).get("count", 1)) * 100
        if error_rate > 5:
            report_lines.append("• High error rate detected. Review failed endpoints and error types.")

    if results["summary"].get("http_req_duration", {}).get("values", {}).get("p(95)", 0) > 2000:
        report_lines.append("• P95 response time is high. Consider load balancing or scaling.")

    report_lines.append("")
    report_lines.append("=" * 60)

    return "\n".join(report_lines)

def generate_metrics_csv(results: Dict) -> str:
    """Generate CSV data for metrics."""

    if not results["timeline_data"]:
        return "timestamp,metric,value,tags\n"

    csv_lines = ["timestamp,metric,value,tags"]

    for data_point in results["timeline_data"]:
        timestamp = data_point.get("timestamp", "")
        metric = data_point.get("metric", "")
        value = data_point.get("value", 0)
        tags = json.dumps(data_point.get("tags", {}))

        csv_lines.append(f"{timestamp},{metric},{value},\"{tags}\"")

    return "\n".join(csv_lines)

def generate_error_report(error_analysis: Dict) -> str:
    """Generate detailed error report."""

    if error_analysis["total_errors"] == 0:
        return "No errors detected during the test."

    report_lines = []
    report_lines.append("=" * 50)
    report_lines.append("K6 LOAD TEST ERROR REPORT")
    report_lines.append("=" * 50)
    report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"Total Errors: {error_analysis['total_errors']}")
    report_lines.append("")

    # Error types breakdown
    report_lines.append("ERROR TYPES BREAKDOWN")
    report_lines.append("-" * 25)
    for error_type, count in error_analysis["error_types"].items():
        percentage = (count / error_analysis["total_errors"]) * 100
        report_lines.append(f"{error_type}: {count} ({percentage:.1f}%)")
    report_lines.append("")

    # Failed endpoints
    report_lines.append("FAILED ENDPOINTS")
    report_lines.append("-" * 20)
    for endpoint, count in error_analysis["failed_endpoints"].items():
        report_lines.append(f"{endpoint}: {count} failures")
    report_lines.append("")

    # Status codes
    report_lines.append("HTTP STATUS CODES")
    report_lines.append("-" * 20)
    for status, count in error_analysis["status_codes"].items():
        report_lines.append(f"Status {status}: {count} occurrences")
    report_lines.append("")

    report_lines.append("=" * 50)

    return "\n".join(report_lines)

if __name__ == "__main__":
    main()







