// go run mksyscall_zos_s390x.go -o_sysnum zsysnum_zos_s390x.go -o_syscall zsyscall_zos_s390x.go -i_syscall syscall_zos_s390x.go -o_asm zsymaddr_zos_s390x.s
// Code generated by the command above; see README.md. DO NOT EDIT.

//go:build zos && s390x

package unix

import (
	"runtime"
	"syscall"
	"unsafe"
)

var _ syscall.Errno

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func fcntl(fd int, cmd int, arg int) (val int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FCNTL<<4, uintptr(fd), uintptr(cmd), uintptr(arg))
	runtime.ExitSyscall()
	val = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Flistxattr(fd int, dest []byte) (sz int, err error) {
	var _p0 unsafe.Pointer
	if len(dest) > 0 {
		_p0 = unsafe.Pointer(&dest[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FLISTXATTR_A<<4, uintptr(fd), uintptr(_p0), uintptr(len(dest)))
	runtime.ExitSyscall()
	sz = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FlistxattrAddr() *(func(fd int, dest []byte) (sz int, err error))

var Flistxattr = enter_Flistxattr

func enter_Flistxattr(fd int, dest []byte) (sz int, err error) {
	funcref := get_FlistxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___FLISTXATTR_A<<4, "") == 0 {
		*funcref = impl_Flistxattr
	} else {
		*funcref = error_Flistxattr
	}
	return (*funcref)(fd, dest)
}

func error_Flistxattr(fd int, dest []byte) (sz int, err error) {
	sz = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fremovexattr(fd int, attr string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FREMOVEXATTR_A<<4, uintptr(fd), uintptr(unsafe.Pointer(_p0)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FremovexattrAddr() *(func(fd int, attr string) (err error))

var Fremovexattr = enter_Fremovexattr

func enter_Fremovexattr(fd int, attr string) (err error) {
	funcref := get_FremovexattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___FREMOVEXATTR_A<<4, "") == 0 {
		*funcref = impl_Fremovexattr
	} else {
		*funcref = error_Fremovexattr
	}
	return (*funcref)(fd, attr)
}

func error_Fremovexattr(fd int, attr string) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func read(fd int, p []byte) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(p) > 0 {
		_p0 = unsafe.Pointer(&p[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_READ<<4, uintptr(fd), uintptr(_p0), uintptr(len(p)))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func write(fd int, p []byte) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(p) > 0 {
		_p0 = unsafe.Pointer(&p[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_WRITE<<4, uintptr(fd), uintptr(_p0), uintptr(len(p)))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fgetxattr(fd int, attr string, dest []byte) (sz int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	var _p1 unsafe.Pointer
	if len(dest) > 0 {
		_p1 = unsafe.Pointer(&dest[0])
	} else {
		_p1 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FGETXATTR_A<<4, uintptr(fd), uintptr(unsafe.Pointer(_p0)), uintptr(_p1), uintptr(len(dest)))
	runtime.ExitSyscall()
	sz = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FgetxattrAddr() *(func(fd int, attr string, dest []byte) (sz int, err error))

var Fgetxattr = enter_Fgetxattr

func enter_Fgetxattr(fd int, attr string, dest []byte) (sz int, err error) {
	funcref := get_FgetxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___FGETXATTR_A<<4, "") == 0 {
		*funcref = impl_Fgetxattr
	} else {
		*funcref = error_Fgetxattr
	}
	return (*funcref)(fd, attr, dest)
}

func error_Fgetxattr(fd int, attr string, dest []byte) (sz int, err error) {
	sz = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fsetxattr(fd int, attr string, data []byte, flag int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	var _p1 unsafe.Pointer
	if len(data) > 0 {
		_p1 = unsafe.Pointer(&data[0])
	} else {
		_p1 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FSETXATTR_A<<4, uintptr(fd), uintptr(unsafe.Pointer(_p0)), uintptr(_p1), uintptr(len(data)), uintptr(flag))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FsetxattrAddr() *(func(fd int, attr string, data []byte, flag int) (err error))

var Fsetxattr = enter_Fsetxattr

func enter_Fsetxattr(fd int, attr string, data []byte, flag int) (err error) {
	funcref := get_FsetxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___FSETXATTR_A<<4, "") == 0 {
		*funcref = impl_Fsetxattr
	} else {
		*funcref = error_Fsetxattr
	}
	return (*funcref)(fd, attr, data, flag)
}

func error_Fsetxattr(fd int, attr string, data []byte, flag int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func accept(s int, rsa *RawSockaddrAny, addrlen *_Socklen) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___ACCEPT_A<<4, uintptr(s), uintptr(unsafe.Pointer(rsa)), uintptr(unsafe.Pointer(addrlen)))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_accept4(s int, rsa *RawSockaddrAny, addrlen *_Socklen, flags int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___ACCEPT4_A<<4, uintptr(s), uintptr(unsafe.Pointer(rsa)), uintptr(unsafe.Pointer(addrlen)), uintptr(flags))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_accept4Addr() *(func(s int, rsa *RawSockaddrAny, addrlen *_Socklen, flags int) (fd int, err error))

var accept4 = enter_accept4

func enter_accept4(s int, rsa *RawSockaddrAny, addrlen *_Socklen, flags int) (fd int, err error) {
	funcref := get_accept4Addr()
	if funcptrtest(GetZosLibVec()+SYS___ACCEPT4_A<<4, "") == 0 {
		*funcref = impl_accept4
	} else {
		*funcref = error_accept4
	}
	return (*funcref)(s, rsa, addrlen, flags)
}

func error_accept4(s int, rsa *RawSockaddrAny, addrlen *_Socklen, flags int) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func bind(s int, addr unsafe.Pointer, addrlen _Socklen) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___BIND_A<<4, uintptr(s), uintptr(addr), uintptr(addrlen))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func connect(s int, addr unsafe.Pointer, addrlen _Socklen) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CONNECT_A<<4, uintptr(s), uintptr(addr), uintptr(addrlen))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func getgroups(n int, list *_Gid_t) (nn int, err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETGROUPS<<4, uintptr(n), uintptr(unsafe.Pointer(list)))
	nn = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func setgroups(n int, list *_Gid_t) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETGROUPS<<4, uintptr(n), uintptr(unsafe.Pointer(list)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func getsockopt(s int, level int, name int, val unsafe.Pointer, vallen *_Socklen) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETSOCKOPT<<4, uintptr(s), uintptr(level), uintptr(name), uintptr(val), uintptr(unsafe.Pointer(vallen)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func setsockopt(s int, level int, name int, val unsafe.Pointer, vallen uintptr) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETSOCKOPT<<4, uintptr(s), uintptr(level), uintptr(name), uintptr(val), uintptr(vallen))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func socket(domain int, typ int, proto int) (fd int, err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SOCKET<<4, uintptr(domain), uintptr(typ), uintptr(proto))
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func socketpair(domain int, typ int, proto int, fd *[2]int32) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SOCKETPAIR<<4, uintptr(domain), uintptr(typ), uintptr(proto), uintptr(unsafe.Pointer(fd)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func getpeername(fd int, rsa *RawSockaddrAny, addrlen *_Socklen) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___GETPEERNAME_A<<4, uintptr(fd), uintptr(unsafe.Pointer(rsa)), uintptr(unsafe.Pointer(addrlen)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func getsockname(fd int, rsa *RawSockaddrAny, addrlen *_Socklen) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___GETSOCKNAME_A<<4, uintptr(fd), uintptr(unsafe.Pointer(rsa)), uintptr(unsafe.Pointer(addrlen)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Removexattr(path string, attr string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___REMOVEXATTR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_RemovexattrAddr() *(func(path string, attr string) (err error))

var Removexattr = enter_Removexattr

func enter_Removexattr(path string, attr string) (err error) {
	funcref := get_RemovexattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___REMOVEXATTR_A<<4, "") == 0 {
		*funcref = impl_Removexattr
	} else {
		*funcref = error_Removexattr
	}
	return (*funcref)(path, attr)
}

func error_Removexattr(path string, attr string) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func recvfrom(fd int, p []byte, flags int, from *RawSockaddrAny, fromlen *_Socklen) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(p) > 0 {
		_p0 = unsafe.Pointer(&p[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___RECVFROM_A<<4, uintptr(fd), uintptr(_p0), uintptr(len(p)), uintptr(flags), uintptr(unsafe.Pointer(from)), uintptr(unsafe.Pointer(fromlen)))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func sendto(s int, buf []byte, flags int, to unsafe.Pointer, addrlen _Socklen) (err error) {
	var _p0 unsafe.Pointer
	if len(buf) > 0 {
		_p0 = unsafe.Pointer(&buf[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___SENDTO_A<<4, uintptr(s), uintptr(_p0), uintptr(len(buf)), uintptr(flags), uintptr(to), uintptr(addrlen))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func recvmsg(s int, msg *Msghdr, flags int) (n int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___RECVMSG_A<<4, uintptr(s), uintptr(unsafe.Pointer(msg)), uintptr(flags))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func sendmsg(s int, msg *Msghdr, flags int) (n int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___SENDMSG_A<<4, uintptr(s), uintptr(unsafe.Pointer(msg)), uintptr(flags))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func mmap(addr uintptr, length uintptr, prot int, flag int, fd int, pos int64) (ret uintptr, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_MMAP<<4, uintptr(addr), uintptr(length), uintptr(prot), uintptr(flag), uintptr(fd), uintptr(pos))
	runtime.ExitSyscall()
	ret = uintptr(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func munmap(addr uintptr, length uintptr) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_MUNMAP<<4, uintptr(addr), uintptr(length))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func ioctl(fd int, req int, arg uintptr) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_IOCTL<<4, uintptr(fd), uintptr(req), uintptr(arg))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func ioctlPtr(fd int, req int, arg unsafe.Pointer) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_IOCTL<<4, uintptr(fd), uintptr(req), uintptr(arg))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func shmat(id int, addr uintptr, flag int) (ret uintptr, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SHMAT<<4, uintptr(id), uintptr(addr), uintptr(flag))
	runtime.ExitSyscall()
	ret = uintptr(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func shmctl(id int, cmd int, buf *SysvShmDesc) (result int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SHMCTL64<<4, uintptr(id), uintptr(cmd), uintptr(unsafe.Pointer(buf)))
	runtime.ExitSyscall()
	result = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func shmdt(addr uintptr) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SHMDT<<4, uintptr(addr))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func shmget(key int, size int, flag int) (id int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SHMGET<<4, uintptr(key), uintptr(size), uintptr(flag))
	runtime.ExitSyscall()
	id = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Access(path string, mode uint32) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___ACCESS_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Chdir(path string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CHDIR_A<<4, uintptr(unsafe.Pointer(_p0)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Chown(path string, uid int, gid int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CHOWN_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(uid), uintptr(gid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Chmod(path string, mode uint32) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CHMOD_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Creat(path string, mode uint32) (fd int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CREAT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Dup(oldfd int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_DUP<<4, uintptr(oldfd))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Dup2(oldfd int, newfd int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_DUP2<<4, uintptr(oldfd), uintptr(newfd))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Dup3(oldfd int, newfd int, flags int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_DUP3<<4, uintptr(oldfd), uintptr(newfd), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_Dup3Addr() *(func(oldfd int, newfd int, flags int) (err error))

var Dup3 = enter_Dup3

func enter_Dup3(oldfd int, newfd int, flags int) (err error) {
	funcref := get_Dup3Addr()
	if funcptrtest(GetZosLibVec()+SYS_DUP3<<4, "") == 0 {
		*funcref = impl_Dup3
	} else {
		*funcref = error_Dup3
	}
	return (*funcref)(oldfd, newfd, flags)
}

func error_Dup3(oldfd int, newfd int, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Dirfd(dirp uintptr) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_DIRFD<<4, uintptr(dirp))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_DirfdAddr() *(func(dirp uintptr) (fd int, err error))

var Dirfd = enter_Dirfd

func enter_Dirfd(dirp uintptr) (fd int, err error) {
	funcref := get_DirfdAddr()
	if funcptrtest(GetZosLibVec()+SYS_DIRFD<<4, "") == 0 {
		*funcref = impl_Dirfd
	} else {
		*funcref = error_Dirfd
	}
	return (*funcref)(dirp)
}

func error_Dirfd(dirp uintptr) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_EpollCreate(size int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_EPOLL_CREATE<<4, uintptr(size))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_EpollCreateAddr() *(func(size int) (fd int, err error))

var EpollCreate = enter_EpollCreate

func enter_EpollCreate(size int) (fd int, err error) {
	funcref := get_EpollCreateAddr()
	if funcptrtest(GetZosLibVec()+SYS_EPOLL_CREATE<<4, "") == 0 {
		*funcref = impl_EpollCreate
	} else {
		*funcref = error_EpollCreate
	}
	return (*funcref)(size)
}

func error_EpollCreate(size int) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_EpollCreate1(flags int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_EPOLL_CREATE1<<4, uintptr(flags))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_EpollCreate1Addr() *(func(flags int) (fd int, err error))

var EpollCreate1 = enter_EpollCreate1

func enter_EpollCreate1(flags int) (fd int, err error) {
	funcref := get_EpollCreate1Addr()
	if funcptrtest(GetZosLibVec()+SYS_EPOLL_CREATE1<<4, "") == 0 {
		*funcref = impl_EpollCreate1
	} else {
		*funcref = error_EpollCreate1
	}
	return (*funcref)(flags)
}

func error_EpollCreate1(flags int) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_EpollCtl(epfd int, op int, fd int, event *EpollEvent) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_EPOLL_CTL<<4, uintptr(epfd), uintptr(op), uintptr(fd), uintptr(unsafe.Pointer(event)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_EpollCtlAddr() *(func(epfd int, op int, fd int, event *EpollEvent) (err error))

var EpollCtl = enter_EpollCtl

func enter_EpollCtl(epfd int, op int, fd int, event *EpollEvent) (err error) {
	funcref := get_EpollCtlAddr()
	if funcptrtest(GetZosLibVec()+SYS_EPOLL_CTL<<4, "") == 0 {
		*funcref = impl_EpollCtl
	} else {
		*funcref = error_EpollCtl
	}
	return (*funcref)(epfd, op, fd, event)
}

func error_EpollCtl(epfd int, op int, fd int, event *EpollEvent) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_EpollPwait(epfd int, events []EpollEvent, msec int, sigmask *int) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(events) > 0 {
		_p0 = unsafe.Pointer(&events[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_EPOLL_PWAIT<<4, uintptr(epfd), uintptr(_p0), uintptr(len(events)), uintptr(msec), uintptr(unsafe.Pointer(sigmask)))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_EpollPwaitAddr() *(func(epfd int, events []EpollEvent, msec int, sigmask *int) (n int, err error))

var EpollPwait = enter_EpollPwait

func enter_EpollPwait(epfd int, events []EpollEvent, msec int, sigmask *int) (n int, err error) {
	funcref := get_EpollPwaitAddr()
	if funcptrtest(GetZosLibVec()+SYS_EPOLL_PWAIT<<4, "") == 0 {
		*funcref = impl_EpollPwait
	} else {
		*funcref = error_EpollPwait
	}
	return (*funcref)(epfd, events, msec, sigmask)
}

func error_EpollPwait(epfd int, events []EpollEvent, msec int, sigmask *int) (n int, err error) {
	n = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_EpollWait(epfd int, events []EpollEvent, msec int) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(events) > 0 {
		_p0 = unsafe.Pointer(&events[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_EPOLL_WAIT<<4, uintptr(epfd), uintptr(_p0), uintptr(len(events)), uintptr(msec))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_EpollWaitAddr() *(func(epfd int, events []EpollEvent, msec int) (n int, err error))

var EpollWait = enter_EpollWait

func enter_EpollWait(epfd int, events []EpollEvent, msec int) (n int, err error) {
	funcref := get_EpollWaitAddr()
	if funcptrtest(GetZosLibVec()+SYS_EPOLL_WAIT<<4, "") == 0 {
		*funcref = impl_EpollWait
	} else {
		*funcref = error_EpollWait
	}
	return (*funcref)(epfd, events, msec)
}

func error_EpollWait(epfd int, events []EpollEvent, msec int) (n int, err error) {
	n = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Errno2() (er2 int) {
	runtime.EnterSyscall()
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS___ERRNO2<<4)
	runtime.ExitSyscall()
	er2 = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Eventfd(initval uint, flags int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_EVENTFD<<4, uintptr(initval), uintptr(flags))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_EventfdAddr() *(func(initval uint, flags int) (fd int, err error))

var Eventfd = enter_Eventfd

func enter_Eventfd(initval uint, flags int) (fd int, err error) {
	funcref := get_EventfdAddr()
	if funcptrtest(GetZosLibVec()+SYS_EVENTFD<<4, "") == 0 {
		*funcref = impl_Eventfd
	} else {
		*funcref = error_Eventfd
	}
	return (*funcref)(initval, flags)
}

func error_Eventfd(initval uint, flags int) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Exit(code int) {
	runtime.EnterSyscall()
	CallLeFuncWithErr(GetZosLibVec()+SYS_EXIT<<4, uintptr(code))
	runtime.ExitSyscall()
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Faccessat(dirfd int, path string, mode uint32, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FACCESSAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(mode), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FaccessatAddr() *(func(dirfd int, path string, mode uint32, flags int) (err error))

var Faccessat = enter_Faccessat

func enter_Faccessat(dirfd int, path string, mode uint32, flags int) (err error) {
	funcref := get_FaccessatAddr()
	if funcptrtest(GetZosLibVec()+SYS___FACCESSAT_A<<4, "") == 0 {
		*funcref = impl_Faccessat
	} else {
		*funcref = error_Faccessat
	}
	return (*funcref)(dirfd, path, mode, flags)
}

func error_Faccessat(dirfd int, path string, mode uint32, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Fchdir(fd int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FCHDIR<<4, uintptr(fd))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Fchmod(fd int, mode uint32) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FCHMOD<<4, uintptr(fd), uintptr(mode))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fchmodat(dirfd int, path string, mode uint32, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FCHMODAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(mode), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FchmodatAddr() *(func(dirfd int, path string, mode uint32, flags int) (err error))

var Fchmodat = enter_Fchmodat

func enter_Fchmodat(dirfd int, path string, mode uint32, flags int) (err error) {
	funcref := get_FchmodatAddr()
	if funcptrtest(GetZosLibVec()+SYS___FCHMODAT_A<<4, "") == 0 {
		*funcref = impl_Fchmodat
	} else {
		*funcref = error_Fchmodat
	}
	return (*funcref)(dirfd, path, mode, flags)
}

func error_Fchmodat(dirfd int, path string, mode uint32, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Fchown(fd int, uid int, gid int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FCHOWN<<4, uintptr(fd), uintptr(uid), uintptr(gid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fchownat(fd int, path string, uid int, gid int, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FCHOWNAT_A<<4, uintptr(fd), uintptr(unsafe.Pointer(_p0)), uintptr(uid), uintptr(gid), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FchownatAddr() *(func(fd int, path string, uid int, gid int, flags int) (err error))

var Fchownat = enter_Fchownat

func enter_Fchownat(fd int, path string, uid int, gid int, flags int) (err error) {
	funcref := get_FchownatAddr()
	if funcptrtest(GetZosLibVec()+SYS___FCHOWNAT_A<<4, "") == 0 {
		*funcref = impl_Fchownat
	} else {
		*funcref = error_Fchownat
	}
	return (*funcref)(fd, path, uid, gid, flags)
}

func error_Fchownat(fd int, path string, uid int, gid int, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func FcntlInt(fd uintptr, cmd int, arg int) (retval int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FCNTL<<4, uintptr(fd), uintptr(cmd), uintptr(arg))
	runtime.ExitSyscall()
	retval = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fdatasync(fd int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FDATASYNC<<4, uintptr(fd))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FdatasyncAddr() *(func(fd int) (err error))

var Fdatasync = enter_Fdatasync

func enter_Fdatasync(fd int) (err error) {
	funcref := get_FdatasyncAddr()
	if funcptrtest(GetZosLibVec()+SYS_FDATASYNC<<4, "") == 0 {
		*funcref = impl_Fdatasync
	} else {
		*funcref = error_Fdatasync
	}
	return (*funcref)(fd)
}

func error_Fdatasync(fd int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func fstat(fd int, stat *Stat_LE_t) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FSTAT<<4, uintptr(fd), uintptr(unsafe.Pointer(stat)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_fstatat(dirfd int, path string, stat *Stat_LE_t, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FSTATAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(stat)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_fstatatAddr() *(func(dirfd int, path string, stat *Stat_LE_t, flags int) (err error))

var fstatat = enter_fstatat

func enter_fstatat(dirfd int, path string, stat *Stat_LE_t, flags int) (err error) {
	funcref := get_fstatatAddr()
	if funcptrtest(GetZosLibVec()+SYS___FSTATAT_A<<4, "") == 0 {
		*funcref = impl_fstatat
	} else {
		*funcref = error_fstatat
	}
	return (*funcref)(dirfd, path, stat, flags)
}

func error_fstatat(dirfd int, path string, stat *Stat_LE_t, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Lgetxattr(link string, attr string, dest []byte) (sz int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(link)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	var _p2 unsafe.Pointer
	if len(dest) > 0 {
		_p2 = unsafe.Pointer(&dest[0])
	} else {
		_p2 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LGETXATTR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)), uintptr(_p2), uintptr(len(dest)))
	runtime.ExitSyscall()
	sz = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_LgetxattrAddr() *(func(link string, attr string, dest []byte) (sz int, err error))

var Lgetxattr = enter_Lgetxattr

func enter_Lgetxattr(link string, attr string, dest []byte) (sz int, err error) {
	funcref := get_LgetxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___LGETXATTR_A<<4, "") == 0 {
		*funcref = impl_Lgetxattr
	} else {
		*funcref = error_Lgetxattr
	}
	return (*funcref)(link, attr, dest)
}

func error_Lgetxattr(link string, attr string, dest []byte) (sz int, err error) {
	sz = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Lsetxattr(path string, attr string, data []byte, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	var _p2 unsafe.Pointer
	if len(data) > 0 {
		_p2 = unsafe.Pointer(&data[0])
	} else {
		_p2 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LSETXATTR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)), uintptr(_p2), uintptr(len(data)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_LsetxattrAddr() *(func(path string, attr string, data []byte, flags int) (err error))

var Lsetxattr = enter_Lsetxattr

func enter_Lsetxattr(path string, attr string, data []byte, flags int) (err error) {
	funcref := get_LsetxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___LSETXATTR_A<<4, "") == 0 {
		*funcref = impl_Lsetxattr
	} else {
		*funcref = error_Lsetxattr
	}
	return (*funcref)(path, attr, data, flags)
}

func error_Lsetxattr(path string, attr string, data []byte, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Fstatfs(fd int, buf *Statfs_t) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FSTATFS<<4, uintptr(fd), uintptr(unsafe.Pointer(buf)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FstatfsAddr() *(func(fd int, buf *Statfs_t) (err error))

var Fstatfs = enter_Fstatfs

func enter_Fstatfs(fd int, buf *Statfs_t) (err error) {
	funcref := get_FstatfsAddr()
	if funcptrtest(GetZosLibVec()+SYS_FSTATFS<<4, "") == 0 {
		*funcref = impl_Fstatfs
	} else {
		*funcref = error_Fstatfs
	}
	return (*funcref)(fd, buf)
}

func error_Fstatfs(fd int, buf *Statfs_t) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Fstatvfs(fd int, stat *Statvfs_t) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FSTATVFS<<4, uintptr(fd), uintptr(unsafe.Pointer(stat)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Fsync(fd int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FSYNC<<4, uintptr(fd))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Futimes(fd int, tv []Timeval) (err error) {
	var _p0 unsafe.Pointer
	if len(tv) > 0 {
		_p0 = unsafe.Pointer(&tv[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FUTIMES<<4, uintptr(fd), uintptr(_p0), uintptr(len(tv)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FutimesAddr() *(func(fd int, tv []Timeval) (err error))

var Futimes = enter_Futimes

func enter_Futimes(fd int, tv []Timeval) (err error) {
	funcref := get_FutimesAddr()
	if funcptrtest(GetZosLibVec()+SYS_FUTIMES<<4, "") == 0 {
		*funcref = impl_Futimes
	} else {
		*funcref = error_Futimes
	}
	return (*funcref)(fd, tv)
}

func error_Futimes(fd int, tv []Timeval) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Futimesat(dirfd int, path string, tv []Timeval) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 unsafe.Pointer
	if len(tv) > 0 {
		_p1 = unsafe.Pointer(&tv[0])
	} else {
		_p1 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___FUTIMESAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(_p1), uintptr(len(tv)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_FutimesatAddr() *(func(dirfd int, path string, tv []Timeval) (err error))

var Futimesat = enter_Futimesat

func enter_Futimesat(dirfd int, path string, tv []Timeval) (err error) {
	funcref := get_FutimesatAddr()
	if funcptrtest(GetZosLibVec()+SYS___FUTIMESAT_A<<4, "") == 0 {
		*funcref = impl_Futimesat
	} else {
		*funcref = error_Futimesat
	}
	return (*funcref)(dirfd, path, tv)
}

func error_Futimesat(dirfd int, path string, tv []Timeval) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Ftruncate(fd int, length int64) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_FTRUNCATE<<4, uintptr(fd), uintptr(length))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Getrandom(buf []byte, flags int) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(buf) > 0 {
		_p0 = unsafe.Pointer(&buf[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETRANDOM<<4, uintptr(_p0), uintptr(len(buf)), uintptr(flags))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_GetrandomAddr() *(func(buf []byte, flags int) (n int, err error))

var Getrandom = enter_Getrandom

func enter_Getrandom(buf []byte, flags int) (n int, err error) {
	funcref := get_GetrandomAddr()
	if funcptrtest(GetZosLibVec()+SYS_GETRANDOM<<4, "") == 0 {
		*funcref = impl_Getrandom
	} else {
		*funcref = error_Getrandom
	}
	return (*funcref)(buf, flags)
}

func error_Getrandom(buf []byte, flags int) (n int, err error) {
	n = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_InotifyInit() (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec() + SYS_INOTIFY_INIT<<4)
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_InotifyInitAddr() *(func() (fd int, err error))

var InotifyInit = enter_InotifyInit

func enter_InotifyInit() (fd int, err error) {
	funcref := get_InotifyInitAddr()
	if funcptrtest(GetZosLibVec()+SYS_INOTIFY_INIT<<4, "") == 0 {
		*funcref = impl_InotifyInit
	} else {
		*funcref = error_InotifyInit
	}
	return (*funcref)()
}

func error_InotifyInit() (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_InotifyInit1(flags int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_INOTIFY_INIT1<<4, uintptr(flags))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_InotifyInit1Addr() *(func(flags int) (fd int, err error))

var InotifyInit1 = enter_InotifyInit1

func enter_InotifyInit1(flags int) (fd int, err error) {
	funcref := get_InotifyInit1Addr()
	if funcptrtest(GetZosLibVec()+SYS_INOTIFY_INIT1<<4, "") == 0 {
		*funcref = impl_InotifyInit1
	} else {
		*funcref = error_InotifyInit1
	}
	return (*funcref)(flags)
}

func error_InotifyInit1(flags int) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_InotifyAddWatch(fd int, pathname string, mask uint32) (watchdesc int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(pathname)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___INOTIFY_ADD_WATCH_A<<4, uintptr(fd), uintptr(unsafe.Pointer(_p0)), uintptr(mask))
	runtime.ExitSyscall()
	watchdesc = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_InotifyAddWatchAddr() *(func(fd int, pathname string, mask uint32) (watchdesc int, err error))

var InotifyAddWatch = enter_InotifyAddWatch

func enter_InotifyAddWatch(fd int, pathname string, mask uint32) (watchdesc int, err error) {
	funcref := get_InotifyAddWatchAddr()
	if funcptrtest(GetZosLibVec()+SYS___INOTIFY_ADD_WATCH_A<<4, "") == 0 {
		*funcref = impl_InotifyAddWatch
	} else {
		*funcref = error_InotifyAddWatch
	}
	return (*funcref)(fd, pathname, mask)
}

func error_InotifyAddWatch(fd int, pathname string, mask uint32) (watchdesc int, err error) {
	watchdesc = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_InotifyRmWatch(fd int, watchdesc uint32) (success int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_INOTIFY_RM_WATCH<<4, uintptr(fd), uintptr(watchdesc))
	runtime.ExitSyscall()
	success = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_InotifyRmWatchAddr() *(func(fd int, watchdesc uint32) (success int, err error))

var InotifyRmWatch = enter_InotifyRmWatch

func enter_InotifyRmWatch(fd int, watchdesc uint32) (success int, err error) {
	funcref := get_InotifyRmWatchAddr()
	if funcptrtest(GetZosLibVec()+SYS_INOTIFY_RM_WATCH<<4, "") == 0 {
		*funcref = impl_InotifyRmWatch
	} else {
		*funcref = error_InotifyRmWatch
	}
	return (*funcref)(fd, watchdesc)
}

func error_InotifyRmWatch(fd int, watchdesc uint32) (success int, err error) {
	success = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Listxattr(path string, dest []byte) (sz int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 unsafe.Pointer
	if len(dest) > 0 {
		_p1 = unsafe.Pointer(&dest[0])
	} else {
		_p1 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LISTXATTR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(_p1), uintptr(len(dest)))
	runtime.ExitSyscall()
	sz = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_ListxattrAddr() *(func(path string, dest []byte) (sz int, err error))

var Listxattr = enter_Listxattr

func enter_Listxattr(path string, dest []byte) (sz int, err error) {
	funcref := get_ListxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___LISTXATTR_A<<4, "") == 0 {
		*funcref = impl_Listxattr
	} else {
		*funcref = error_Listxattr
	}
	return (*funcref)(path, dest)
}

func error_Listxattr(path string, dest []byte) (sz int, err error) {
	sz = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Llistxattr(path string, dest []byte) (sz int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 unsafe.Pointer
	if len(dest) > 0 {
		_p1 = unsafe.Pointer(&dest[0])
	} else {
		_p1 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LLISTXATTR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(_p1), uintptr(len(dest)))
	runtime.ExitSyscall()
	sz = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_LlistxattrAddr() *(func(path string, dest []byte) (sz int, err error))

var Llistxattr = enter_Llistxattr

func enter_Llistxattr(path string, dest []byte) (sz int, err error) {
	funcref := get_LlistxattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___LLISTXATTR_A<<4, "") == 0 {
		*funcref = impl_Llistxattr
	} else {
		*funcref = error_Llistxattr
	}
	return (*funcref)(path, dest)
}

func error_Llistxattr(path string, dest []byte) (sz int, err error) {
	sz = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Lremovexattr(path string, attr string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(attr)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LREMOVEXATTR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_LremovexattrAddr() *(func(path string, attr string) (err error))

var Lremovexattr = enter_Lremovexattr

func enter_Lremovexattr(path string, attr string) (err error) {
	funcref := get_LremovexattrAddr()
	if funcptrtest(GetZosLibVec()+SYS___LREMOVEXATTR_A<<4, "") == 0 {
		*funcref = impl_Lremovexattr
	} else {
		*funcref = error_Lremovexattr
	}
	return (*funcref)(path, attr)
}

func error_Lremovexattr(path string, attr string) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Lutimes(path string, tv []Timeval) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 unsafe.Pointer
	if len(tv) > 0 {
		_p1 = unsafe.Pointer(&tv[0])
	} else {
		_p1 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LUTIMES_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(_p1), uintptr(len(tv)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_LutimesAddr() *(func(path string, tv []Timeval) (err error))

var Lutimes = enter_Lutimes

func enter_Lutimes(path string, tv []Timeval) (err error) {
	funcref := get_LutimesAddr()
	if funcptrtest(GetZosLibVec()+SYS___LUTIMES_A<<4, "") == 0 {
		*funcref = impl_Lutimes
	} else {
		*funcref = error_Lutimes
	}
	return (*funcref)(path, tv)
}

func error_Lutimes(path string, tv []Timeval) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Mprotect(b []byte, prot int) (err error) {
	var _p0 unsafe.Pointer
	if len(b) > 0 {
		_p0 = unsafe.Pointer(&b[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_MPROTECT<<4, uintptr(_p0), uintptr(len(b)), uintptr(prot))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Msync(b []byte, flags int) (err error) {
	var _p0 unsafe.Pointer
	if len(b) > 0 {
		_p0 = unsafe.Pointer(&b[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_MSYNC<<4, uintptr(_p0), uintptr(len(b)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Console2(cmsg *ConsMsg2, modstr *byte, concmd *uint32) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CONSOLE2<<4, uintptr(unsafe.Pointer(cmsg)), uintptr(unsafe.Pointer(modstr)), uintptr(unsafe.Pointer(concmd)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Poll(fds []PollFd, timeout int) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(fds) > 0 {
		_p0 = unsafe.Pointer(&fds[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_POLL<<4, uintptr(_p0), uintptr(len(fds)), uintptr(timeout))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Readdir_r(dirp uintptr, entry *direntLE, result **direntLE) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___READDIR_R_A<<4, uintptr(dirp), uintptr(unsafe.Pointer(entry)), uintptr(unsafe.Pointer(result)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Statfs(path string, buf *Statfs_t) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___STATFS_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(buf)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_StatfsAddr() *(func(path string, buf *Statfs_t) (err error))

var Statfs = enter_Statfs

func enter_Statfs(path string, buf *Statfs_t) (err error) {
	funcref := get_StatfsAddr()
	if funcptrtest(GetZosLibVec()+SYS___STATFS_A<<4, "") == 0 {
		*funcref = impl_Statfs
	} else {
		*funcref = error_Statfs
	}
	return (*funcref)(path, buf)
}

func error_Statfs(path string, buf *Statfs_t) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Syncfs(fd int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SYNCFS<<4, uintptr(fd))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_SyncfsAddr() *(func(fd int) (err error))

var Syncfs = enter_Syncfs

func enter_Syncfs(fd int) (err error) {
	funcref := get_SyncfsAddr()
	if funcptrtest(GetZosLibVec()+SYS_SYNCFS<<4, "") == 0 {
		*funcref = impl_Syncfs
	} else {
		*funcref = error_Syncfs
	}
	return (*funcref)(fd)
}

func error_Syncfs(fd int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Times(tms *Tms) (ticks uintptr, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_TIMES<<4, uintptr(unsafe.Pointer(tms)))
	runtime.ExitSyscall()
	ticks = uintptr(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func W_Getmntent(buff *byte, size int) (lastsys int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_W_GETMNTENT<<4, uintptr(unsafe.Pointer(buff)), uintptr(size))
	runtime.ExitSyscall()
	lastsys = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func W_Getmntent_A(buff *byte, size int) (lastsys int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___W_GETMNTENT_A<<4, uintptr(unsafe.Pointer(buff)), uintptr(size))
	runtime.ExitSyscall()
	lastsys = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func mount_LE(path string, filesystem string, fstype string, mtm uint32, parmlen int32, parm string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(filesystem)
	if err != nil {
		return
	}
	var _p2 *byte
	_p2, err = BytePtrFromString(fstype)
	if err != nil {
		return
	}
	var _p3 *byte
	_p3, err = BytePtrFromString(parm)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___MOUNT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)), uintptr(unsafe.Pointer(_p2)), uintptr(mtm), uintptr(parmlen), uintptr(unsafe.Pointer(_p3)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func unmount_LE(filesystem string, mtm int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(filesystem)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___UMOUNT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mtm))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Chroot(path string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___CHROOT_A<<4, uintptr(unsafe.Pointer(_p0)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Select(nmsgsfds int, r *FdSet, w *FdSet, e *FdSet, timeout *Timeval) (ret int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SELECT<<4, uintptr(nmsgsfds), uintptr(unsafe.Pointer(r)), uintptr(unsafe.Pointer(w)), uintptr(unsafe.Pointer(e)), uintptr(unsafe.Pointer(timeout)))
	runtime.ExitSyscall()
	ret = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Uname(buf *Utsname) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_____OSNAME_A<<4, uintptr(unsafe.Pointer(buf)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Unshare(flags int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_UNSHARE<<4, uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_UnshareAddr() *(func(flags int) (err error))

var Unshare = enter_Unshare

func enter_Unshare(flags int) (err error) {
	funcref := get_UnshareAddr()
	if funcptrtest(GetZosLibVec()+SYS_UNSHARE<<4, "") == 0 {
		*funcref = impl_Unshare
	} else {
		*funcref = error_Unshare
	}
	return (*funcref)(flags)
}

func error_Unshare(flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Gethostname(buf []byte) (err error) {
	var _p0 unsafe.Pointer
	if len(buf) > 0 {
		_p0 = unsafe.Pointer(&buf[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___GETHOSTNAME_A<<4, uintptr(_p0), uintptr(len(buf)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getgid() (gid int) {
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS_GETGID<<4)
	gid = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getpid() (pid int) {
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS_GETPID<<4)
	pid = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getpgid(pid int) (pgid int, err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETPGID<<4, uintptr(pid))
	pgid = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getppid() (pid int) {
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS_GETPPID<<4)
	pid = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getpriority(which int, who int) (prio int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETPRIORITY<<4, uintptr(which), uintptr(who))
	runtime.ExitSyscall()
	prio = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getrlimit(resource int, rlim *Rlimit) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETRLIMIT<<4, uintptr(resource), uintptr(unsafe.Pointer(rlim)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func getrusage(who int, rusage *rusage_zos) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETRUSAGE<<4, uintptr(who), uintptr(unsafe.Pointer(rusage)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getegid() (egid int) {
	runtime.EnterSyscall()
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS_GETEGID<<4)
	runtime.ExitSyscall()
	egid = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Geteuid() (euid int) {
	runtime.EnterSyscall()
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS_GETEUID<<4)
	runtime.ExitSyscall()
	euid = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getsid(pid int) (sid int, err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETSID<<4, uintptr(pid))
	sid = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Getuid() (uid int) {
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec() + SYS_GETUID<<4)
	uid = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Kill(pid int, sig Signal) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_KILL<<4, uintptr(pid), uintptr(sig))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Lchown(path string, uid int, gid int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LCHOWN_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(uid), uintptr(gid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Link(path string, link string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(link)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LINK_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Linkat(oldDirFd int, oldPath string, newDirFd int, newPath string, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(oldPath)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(newPath)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LINKAT_A<<4, uintptr(oldDirFd), uintptr(unsafe.Pointer(_p0)), uintptr(newDirFd), uintptr(unsafe.Pointer(_p1)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_LinkatAddr() *(func(oldDirFd int, oldPath string, newDirFd int, newPath string, flags int) (err error))

var Linkat = enter_Linkat

func enter_Linkat(oldDirFd int, oldPath string, newDirFd int, newPath string, flags int) (err error) {
	funcref := get_LinkatAddr()
	if funcptrtest(GetZosLibVec()+SYS___LINKAT_A<<4, "") == 0 {
		*funcref = impl_Linkat
	} else {
		*funcref = error_Linkat
	}
	return (*funcref)(oldDirFd, oldPath, newDirFd, newPath, flags)
}

func error_Linkat(oldDirFd int, oldPath string, newDirFd int, newPath string, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Listen(s int, n int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_LISTEN<<4, uintptr(s), uintptr(n))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func lstat(path string, stat *Stat_LE_t) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___LSTAT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(stat)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Mkdir(path string, mode uint32) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___MKDIR_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Mkdirat(dirfd int, path string, mode uint32) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___MKDIRAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(mode))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_MkdiratAddr() *(func(dirfd int, path string, mode uint32) (err error))

var Mkdirat = enter_Mkdirat

func enter_Mkdirat(dirfd int, path string, mode uint32) (err error) {
	funcref := get_MkdiratAddr()
	if funcptrtest(GetZosLibVec()+SYS___MKDIRAT_A<<4, "") == 0 {
		*funcref = impl_Mkdirat
	} else {
		*funcref = error_Mkdirat
	}
	return (*funcref)(dirfd, path, mode)
}

func error_Mkdirat(dirfd int, path string, mode uint32) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Mkfifo(path string, mode uint32) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___MKFIFO_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Mknod(path string, mode uint32, dev int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___MKNOD_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode), uintptr(dev))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Mknodat(dirfd int, path string, mode uint32, dev int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___MKNODAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(mode), uintptr(dev))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_MknodatAddr() *(func(dirfd int, path string, mode uint32, dev int) (err error))

var Mknodat = enter_Mknodat

func enter_Mknodat(dirfd int, path string, mode uint32, dev int) (err error) {
	funcref := get_MknodatAddr()
	if funcptrtest(GetZosLibVec()+SYS___MKNODAT_A<<4, "") == 0 {
		*funcref = impl_Mknodat
	} else {
		*funcref = error_Mknodat
	}
	return (*funcref)(dirfd, path, mode, dev)
}

func error_Mknodat(dirfd int, path string, mode uint32, dev int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_PivotRoot(newroot string, oldroot string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(newroot)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(oldroot)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___PIVOT_ROOT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_PivotRootAddr() *(func(newroot string, oldroot string) (err error))

var PivotRoot = enter_PivotRoot

func enter_PivotRoot(newroot string, oldroot string) (err error) {
	funcref := get_PivotRootAddr()
	if funcptrtest(GetZosLibVec()+SYS___PIVOT_ROOT_A<<4, "") == 0 {
		*funcref = impl_PivotRoot
	} else {
		*funcref = error_PivotRoot
	}
	return (*funcref)(newroot, oldroot)
}

func error_PivotRoot(newroot string, oldroot string) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Pread(fd int, p []byte, offset int64) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(p) > 0 {
		_p0 = unsafe.Pointer(&p[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_PREAD<<4, uintptr(fd), uintptr(_p0), uintptr(len(p)), uintptr(offset))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Pwrite(fd int, p []byte, offset int64) (n int, err error) {
	var _p0 unsafe.Pointer
	if len(p) > 0 {
		_p0 = unsafe.Pointer(&p[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_PWRITE<<4, uintptr(fd), uintptr(_p0), uintptr(len(p)), uintptr(offset))
	runtime.ExitSyscall()
	n = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Prctl(option int, arg2 uintptr, arg3 uintptr, arg4 uintptr, arg5 uintptr) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___PRCTL_A<<4, uintptr(option), uintptr(arg2), uintptr(arg3), uintptr(arg4), uintptr(arg5))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_PrctlAddr() *(func(option int, arg2 uintptr, arg3 uintptr, arg4 uintptr, arg5 uintptr) (err error))

var Prctl = enter_Prctl

func enter_Prctl(option int, arg2 uintptr, arg3 uintptr, arg4 uintptr, arg5 uintptr) (err error) {
	funcref := get_PrctlAddr()
	if funcptrtest(GetZosLibVec()+SYS___PRCTL_A<<4, "") == 0 {
		*funcref = impl_Prctl
	} else {
		*funcref = error_Prctl
	}
	return (*funcref)(option, arg2, arg3, arg4, arg5)
}

func error_Prctl(option int, arg2 uintptr, arg3 uintptr, arg4 uintptr, arg5 uintptr) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Prlimit(pid int, resource int, newlimit *Rlimit, old *Rlimit) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_PRLIMIT<<4, uintptr(pid), uintptr(resource), uintptr(unsafe.Pointer(newlimit)), uintptr(unsafe.Pointer(old)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_PrlimitAddr() *(func(pid int, resource int, newlimit *Rlimit, old *Rlimit) (err error))

var Prlimit = enter_Prlimit

func enter_Prlimit(pid int, resource int, newlimit *Rlimit, old *Rlimit) (err error) {
	funcref := get_PrlimitAddr()
	if funcptrtest(GetZosLibVec()+SYS_PRLIMIT<<4, "") == 0 {
		*funcref = impl_Prlimit
	} else {
		*funcref = error_Prlimit
	}
	return (*funcref)(pid, resource, newlimit, old)
}

func error_Prlimit(pid int, resource int, newlimit *Rlimit, old *Rlimit) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Rename(from string, to string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(from)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(to)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___RENAME_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Renameat(olddirfd int, oldpath string, newdirfd int, newpath string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(oldpath)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(newpath)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___RENAMEAT_A<<4, uintptr(olddirfd), uintptr(unsafe.Pointer(_p0)), uintptr(newdirfd), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_RenameatAddr() *(func(olddirfd int, oldpath string, newdirfd int, newpath string) (err error))

var Renameat = enter_Renameat

func enter_Renameat(olddirfd int, oldpath string, newdirfd int, newpath string) (err error) {
	funcref := get_RenameatAddr()
	if funcptrtest(GetZosLibVec()+SYS___RENAMEAT_A<<4, "") == 0 {
		*funcref = impl_Renameat
	} else {
		*funcref = error_Renameat
	}
	return (*funcref)(olddirfd, oldpath, newdirfd, newpath)
}

func error_Renameat(olddirfd int, oldpath string, newdirfd int, newpath string) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Renameat2(olddirfd int, oldpath string, newdirfd int, newpath string, flags uint) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(oldpath)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(newpath)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___RENAMEAT2_A<<4, uintptr(olddirfd), uintptr(unsafe.Pointer(_p0)), uintptr(newdirfd), uintptr(unsafe.Pointer(_p1)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_Renameat2Addr() *(func(olddirfd int, oldpath string, newdirfd int, newpath string, flags uint) (err error))

var Renameat2 = enter_Renameat2

func enter_Renameat2(olddirfd int, oldpath string, newdirfd int, newpath string, flags uint) (err error) {
	funcref := get_Renameat2Addr()
	if funcptrtest(GetZosLibVec()+SYS___RENAMEAT2_A<<4, "") == 0 {
		*funcref = impl_Renameat2
	} else {
		*funcref = error_Renameat2
	}
	return (*funcref)(olddirfd, oldpath, newdirfd, newpath, flags)
}

func error_Renameat2(olddirfd int, oldpath string, newdirfd int, newpath string, flags uint) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Rmdir(path string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___RMDIR_A<<4, uintptr(unsafe.Pointer(_p0)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Seek(fd int, offset int64, whence int) (off int64, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_LSEEK<<4, uintptr(fd), uintptr(offset), uintptr(whence))
	runtime.ExitSyscall()
	off = int64(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setegid(egid int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETEGID<<4, uintptr(egid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Seteuid(euid int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETEUID<<4, uintptr(euid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Sethostname(p []byte) (err error) {
	var _p0 unsafe.Pointer
	if len(p) > 0 {
		_p0 = unsafe.Pointer(&p[0])
	} else {
		_p0 = unsafe.Pointer(&_zero)
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___SETHOSTNAME_A<<4, uintptr(_p0), uintptr(len(p)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_SethostnameAddr() *(func(p []byte) (err error))

var Sethostname = enter_Sethostname

func enter_Sethostname(p []byte) (err error) {
	funcref := get_SethostnameAddr()
	if funcptrtest(GetZosLibVec()+SYS___SETHOSTNAME_A<<4, "") == 0 {
		*funcref = impl_Sethostname
	} else {
		*funcref = error_Sethostname
	}
	return (*funcref)(p)
}

func error_Sethostname(p []byte) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Setns(fd int, nstype int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETNS<<4, uintptr(fd), uintptr(nstype))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_SetnsAddr() *(func(fd int, nstype int) (err error))

var Setns = enter_Setns

func enter_Setns(fd int, nstype int) (err error) {
	funcref := get_SetnsAddr()
	if funcptrtest(GetZosLibVec()+SYS_SETNS<<4, "") == 0 {
		*funcref = impl_Setns
	} else {
		*funcref = error_Setns
	}
	return (*funcref)(fd, nstype)
}

func error_Setns(fd int, nstype int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setpriority(which int, who int, prio int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETPRIORITY<<4, uintptr(which), uintptr(who), uintptr(prio))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setpgid(pid int, pgid int) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETPGID<<4, uintptr(pid), uintptr(pgid))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setrlimit(resource int, lim *Rlimit) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETRLIMIT<<4, uintptr(resource), uintptr(unsafe.Pointer(lim)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setregid(rgid int, egid int) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETREGID<<4, uintptr(rgid), uintptr(egid))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setreuid(ruid int, euid int) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETREUID<<4, uintptr(ruid), uintptr(euid))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setsid() (pid int, err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec() + SYS_SETSID<<4)
	pid = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setuid(uid int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETUID<<4, uintptr(uid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Setgid(uid int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SETGID<<4, uintptr(uid))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Shutdown(fd int, how int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_SHUTDOWN<<4, uintptr(fd), uintptr(how))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func stat(path string, statLE *Stat_LE_t) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___STAT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(statLE)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Symlink(path string, link string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(link)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___SYMLINK_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Symlinkat(oldPath string, dirfd int, newPath string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(oldPath)
	if err != nil {
		return
	}
	var _p1 *byte
	_p1, err = BytePtrFromString(newPath)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___SYMLINKAT_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(dirfd), uintptr(unsafe.Pointer(_p1)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_SymlinkatAddr() *(func(oldPath string, dirfd int, newPath string) (err error))

var Symlinkat = enter_Symlinkat

func enter_Symlinkat(oldPath string, dirfd int, newPath string) (err error) {
	funcref := get_SymlinkatAddr()
	if funcptrtest(GetZosLibVec()+SYS___SYMLINKAT_A<<4, "") == 0 {
		*funcref = impl_Symlinkat
	} else {
		*funcref = error_Symlinkat
	}
	return (*funcref)(oldPath, dirfd, newPath)
}

func error_Symlinkat(oldPath string, dirfd int, newPath string) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Sync() {
	runtime.EnterSyscall()
	CallLeFuncWithErr(GetZosLibVec() + SYS_SYNC<<4)
	runtime.ExitSyscall()
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Truncate(path string, length int64) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___TRUNCATE_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(length))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Tcgetattr(fildes int, termptr *Termios) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_TCGETATTR<<4, uintptr(fildes), uintptr(unsafe.Pointer(termptr)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Tcsetattr(fildes int, when int, termptr *Termios) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_TCSETATTR<<4, uintptr(fildes), uintptr(when), uintptr(unsafe.Pointer(termptr)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Umask(mask int) (oldmask int) {
	runtime.EnterSyscall()
	r0, _, _ := CallLeFuncWithErr(GetZosLibVec()+SYS_UMASK<<4, uintptr(mask))
	runtime.ExitSyscall()
	oldmask = int(r0)
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Unlink(path string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___UNLINK_A<<4, uintptr(unsafe.Pointer(_p0)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_Unlinkat(dirfd int, path string, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___UNLINKAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_UnlinkatAddr() *(func(dirfd int, path string, flags int) (err error))

var Unlinkat = enter_Unlinkat

func enter_Unlinkat(dirfd int, path string, flags int) (err error) {
	funcref := get_UnlinkatAddr()
	if funcptrtest(GetZosLibVec()+SYS___UNLINKAT_A<<4, "") == 0 {
		*funcref = impl_Unlinkat
	} else {
		*funcref = error_Unlinkat
	}
	return (*funcref)(dirfd, path, flags)
}

func error_Unlinkat(dirfd int, path string, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Utime(path string, utim *Utimbuf) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___UTIME_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(utim)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func open(path string, mode int, perm uint32) (fd int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___OPEN_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(mode), uintptr(perm))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_openat(dirfd int, path string, flags int, mode uint32) (fd int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___OPENAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(flags), uintptr(mode))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_openatAddr() *(func(dirfd int, path string, flags int, mode uint32) (fd int, err error))

var openat = enter_openat

func enter_openat(dirfd int, path string, flags int, mode uint32) (fd int, err error) {
	funcref := get_openatAddr()
	if funcptrtest(GetZosLibVec()+SYS___OPENAT_A<<4, "") == 0 {
		*funcref = impl_openat
	} else {
		*funcref = error_openat
	}
	return (*funcref)(dirfd, path, flags, mode)
}

func error_openat(dirfd int, path string, flags int, mode uint32) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_openat2(dirfd int, path string, open_how *OpenHow, size int) (fd int, err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___OPENAT2_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(open_how)), uintptr(size))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_openat2Addr() *(func(dirfd int, path string, open_how *OpenHow, size int) (fd int, err error))

var openat2 = enter_openat2

func enter_openat2(dirfd int, path string, open_how *OpenHow, size int) (fd int, err error) {
	funcref := get_openat2Addr()
	if funcptrtest(GetZosLibVec()+SYS___OPENAT2_A<<4, "") == 0 {
		*funcref = impl_openat2
	} else {
		*funcref = error_openat2
	}
	return (*funcref)(dirfd, path, open_how, size)
}

func error_openat2(dirfd int, path string, open_how *OpenHow, size int) (fd int, err error) {
	fd = -1
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func remove(path string) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_REMOVE<<4, uintptr(unsafe.Pointer(_p0)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func waitid(idType int, id int, info *Siginfo, options int) (err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_WAITID<<4, uintptr(idType), uintptr(id), uintptr(unsafe.Pointer(info)), uintptr(options))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func waitpid(pid int, wstatus *_C_int, options int) (wpid int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_WAITPID<<4, uintptr(pid), uintptr(unsafe.Pointer(wstatus)), uintptr(options))
	runtime.ExitSyscall()
	wpid = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func gettimeofday(tv *timeval_zos) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GETTIMEOFDAY<<4, uintptr(unsafe.Pointer(tv)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func pipe(p *[2]_C_int) (err error) {
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_PIPE<<4, uintptr(unsafe.Pointer(p)))
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func utimes(path string, timeval *[2]Timeval) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___UTIMES_A<<4, uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(timeval)))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func impl_utimensat(dirfd int, path string, ts *[2]Timespec, flags int) (err error) {
	var _p0 *byte
	_p0, err = BytePtrFromString(path)
	if err != nil {
		return
	}
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS___UTIMENSAT_A<<4, uintptr(dirfd), uintptr(unsafe.Pointer(_p0)), uintptr(unsafe.Pointer(ts)), uintptr(flags))
	runtime.ExitSyscall()
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

//go:nosplit
func get_utimensatAddr() *(func(dirfd int, path string, ts *[2]Timespec, flags int) (err error))

var utimensat = enter_utimensat

func enter_utimensat(dirfd int, path string, ts *[2]Timespec, flags int) (err error) {
	funcref := get_utimensatAddr()
	if funcptrtest(GetZosLibVec()+SYS___UTIMENSAT_A<<4, "") == 0 {
		*funcref = impl_utimensat
	} else {
		*funcref = error_utimensat
	}
	return (*funcref)(dirfd, path, ts, flags)
}

func error_utimensat(dirfd int, path string, ts *[2]Timespec, flags int) (err error) {
	err = ENOSYS
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Posix_openpt(oflag int) (fd int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_POSIX_OPENPT<<4, uintptr(oflag))
	runtime.ExitSyscall()
	fd = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Grantpt(fildes int) (rc int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_GRANTPT<<4, uintptr(fildes))
	runtime.ExitSyscall()
	rc = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}

// THIS FILE IS GENERATED BY THE COMMAND AT THE TOP; DO NOT EDIT

func Unlockpt(fildes int) (rc int, err error) {
	runtime.EnterSyscall()
	r0, e2, e1 := CallLeFuncWithErr(GetZosLibVec()+SYS_UNLOCKPT<<4, uintptr(fildes))
	runtime.ExitSyscall()
	rc = int(r0)
	if int64(r0) == -1 {
		err = errnoErr2(e1, e2)
	}
	return
}
