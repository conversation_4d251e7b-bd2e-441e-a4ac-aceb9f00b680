// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: google/protobuf/type.proto

package typepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	sourcecontextpb "google.golang.org/protobuf/types/known/sourcecontextpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

// The syntax in which a protocol buffer element is defined.
type Syntax int32

const (
	// Syntax `proto2`.
	Syntax_SYNTAX_PROTO2 Syntax = 0
	// Syntax `proto3`.
	Syntax_SYNTAX_PROTO3 Syntax = 1
	// Syntax `editions`.
	Syntax_SYNTAX_EDITIONS Syntax = 2
)

// Enum value maps for Syntax.
var (
	Syntax_name = map[int32]string{
		0: "SYNTAX_PROTO2",
		1: "SYNTAX_PROTO3",
		2: "SYNTAX_EDITIONS",
	}
	Syntax_value = map[string]int32{
		"SYNTAX_PROTO2":   0,
		"SYNTAX_PROTO3":   1,
		"SYNTAX_EDITIONS": 2,
	}
)

func (x Syntax) Enum() *Syntax {
	p := new(Syntax)
	*p = x
	return p
}

func (x Syntax) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Syntax) Descriptor() protoreflect.EnumDescriptor {
	return file_google_protobuf_type_proto_enumTypes[0].Descriptor()
}

func (Syntax) Type() protoreflect.EnumType {
	return &file_google_protobuf_type_proto_enumTypes[0]
}

func (x Syntax) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Syntax.Descriptor instead.
func (Syntax) EnumDescriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{0}
}

// Basic field types.
type Field_Kind int32

const (
	// Field type unknown.
	Field_TYPE_UNKNOWN Field_Kind = 0
	// Field type double.
	Field_TYPE_DOUBLE Field_Kind = 1
	// Field type float.
	Field_TYPE_FLOAT Field_Kind = 2
	// Field type int64.
	Field_TYPE_INT64 Field_Kind = 3
	// Field type uint64.
	Field_TYPE_UINT64 Field_Kind = 4
	// Field type int32.
	Field_TYPE_INT32 Field_Kind = 5
	// Field type fixed64.
	Field_TYPE_FIXED64 Field_Kind = 6
	// Field type fixed32.
	Field_TYPE_FIXED32 Field_Kind = 7
	// Field type bool.
	Field_TYPE_BOOL Field_Kind = 8
	// Field type string.
	Field_TYPE_STRING Field_Kind = 9
	// Field type group. Proto2 syntax only, and deprecated.
	Field_TYPE_GROUP Field_Kind = 10
	// Field type message.
	Field_TYPE_MESSAGE Field_Kind = 11
	// Field type bytes.
	Field_TYPE_BYTES Field_Kind = 12
	// Field type uint32.
	Field_TYPE_UINT32 Field_Kind = 13
	// Field type enum.
	Field_TYPE_ENUM Field_Kind = 14
	// Field type sfixed32.
	Field_TYPE_SFIXED32 Field_Kind = 15
	// Field type sfixed64.
	Field_TYPE_SFIXED64 Field_Kind = 16
	// Field type sint32.
	Field_TYPE_SINT32 Field_Kind = 17
	// Field type sint64.
	Field_TYPE_SINT64 Field_Kind = 18
)

// Enum value maps for Field_Kind.
var (
	Field_Kind_name = map[int32]string{
		0:  "TYPE_UNKNOWN",
		1:  "TYPE_DOUBLE",
		2:  "TYPE_FLOAT",
		3:  "TYPE_INT64",
		4:  "TYPE_UINT64",
		5:  "TYPE_INT32",
		6:  "TYPE_FIXED64",
		7:  "TYPE_FIXED32",
		8:  "TYPE_BOOL",
		9:  "TYPE_STRING",
		10: "TYPE_GROUP",
		11: "TYPE_MESSAGE",
		12: "TYPE_BYTES",
		13: "TYPE_UINT32",
		14: "TYPE_ENUM",
		15: "TYPE_SFIXED32",
		16: "TYPE_SFIXED64",
		17: "TYPE_SINT32",
		18: "TYPE_SINT64",
	}
	Field_Kind_value = map[string]int32{
		"TYPE_UNKNOWN":  0,
		"TYPE_DOUBLE":   1,
		"TYPE_FLOAT":    2,
		"TYPE_INT64":    3,
		"TYPE_UINT64":   4,
		"TYPE_INT32":    5,
		"TYPE_FIXED64":  6,
		"TYPE_FIXED32":  7,
		"TYPE_BOOL":     8,
		"TYPE_STRING":   9,
		"TYPE_GROUP":    10,
		"TYPE_MESSAGE":  11,
		"TYPE_BYTES":    12,
		"TYPE_UINT32":   13,
		"TYPE_ENUM":     14,
		"TYPE_SFIXED32": 15,
		"TYPE_SFIXED64": 16,
		"TYPE_SINT32":   17,
		"TYPE_SINT64":   18,
	}
)

func (x Field_Kind) Enum() *Field_Kind {
	p := new(Field_Kind)
	*p = x
	return p
}

func (x Field_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Field_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_google_protobuf_type_proto_enumTypes[1].Descriptor()
}

func (Field_Kind) Type() protoreflect.EnumType {
	return &file_google_protobuf_type_proto_enumTypes[1]
}

func (x Field_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Field_Kind.Descriptor instead.
func (Field_Kind) EnumDescriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{1, 0}
}

// Whether a field is optional, required, or repeated.
type Field_Cardinality int32

const (
	// For fields with unknown cardinality.
	Field_CARDINALITY_UNKNOWN Field_Cardinality = 0
	// For optional fields.
	Field_CARDINALITY_OPTIONAL Field_Cardinality = 1
	// For required fields. Proto2 syntax only.
	Field_CARDINALITY_REQUIRED Field_Cardinality = 2
	// For repeated fields.
	Field_CARDINALITY_REPEATED Field_Cardinality = 3
)

// Enum value maps for Field_Cardinality.
var (
	Field_Cardinality_name = map[int32]string{
		0: "CARDINALITY_UNKNOWN",
		1: "CARDINALITY_OPTIONAL",
		2: "CARDINALITY_REQUIRED",
		3: "CARDINALITY_REPEATED",
	}
	Field_Cardinality_value = map[string]int32{
		"CARDINALITY_UNKNOWN":  0,
		"CARDINALITY_OPTIONAL": 1,
		"CARDINALITY_REQUIRED": 2,
		"CARDINALITY_REPEATED": 3,
	}
)

func (x Field_Cardinality) Enum() *Field_Cardinality {
	p := new(Field_Cardinality)
	*p = x
	return p
}

func (x Field_Cardinality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Field_Cardinality) Descriptor() protoreflect.EnumDescriptor {
	return file_google_protobuf_type_proto_enumTypes[2].Descriptor()
}

func (Field_Cardinality) Type() protoreflect.EnumType {
	return &file_google_protobuf_type_proto_enumTypes[2]
}

func (x Field_Cardinality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Field_Cardinality.Descriptor instead.
func (Field_Cardinality) EnumDescriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{1, 1}
}

// A protocol buffer message type.
type Type struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The fully qualified message name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The list of fields.
	Fields []*Field `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	// The list of types appearing in `oneof` definitions in this type.
	Oneofs []string `protobuf:"bytes,3,rep,name=oneofs,proto3" json:"oneofs,omitempty"`
	// The protocol buffer options.
	Options []*Option `protobuf:"bytes,4,rep,name=options,proto3" json:"options,omitempty"`
	// The source context.
	SourceContext *sourcecontextpb.SourceContext `protobuf:"bytes,5,opt,name=source_context,json=sourceContext,proto3" json:"source_context,omitempty"`
	// The source syntax.
	Syntax Syntax `protobuf:"varint,6,opt,name=syntax,proto3,enum=google.protobuf.Syntax" json:"syntax,omitempty"`
	// The source edition string, only valid when syntax is SYNTAX_EDITIONS.
	Edition       string `protobuf:"bytes,7,opt,name=edition,proto3" json:"edition,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Type) Reset() {
	*x = Type{}
	mi := &file_google_protobuf_type_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Type) ProtoMessage() {}

func (x *Type) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_type_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Type.ProtoReflect.Descriptor instead.
func (*Type) Descriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{0}
}

func (x *Type) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Type) GetFields() []*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *Type) GetOneofs() []string {
	if x != nil {
		return x.Oneofs
	}
	return nil
}

func (x *Type) GetOptions() []*Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Type) GetSourceContext() *sourcecontextpb.SourceContext {
	if x != nil {
		return x.SourceContext
	}
	return nil
}

func (x *Type) GetSyntax() Syntax {
	if x != nil {
		return x.Syntax
	}
	return Syntax_SYNTAX_PROTO2
}

func (x *Type) GetEdition() string {
	if x != nil {
		return x.Edition
	}
	return ""
}

// A single field of a message type.
type Field struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The field type.
	Kind Field_Kind `protobuf:"varint,1,opt,name=kind,proto3,enum=google.protobuf.Field_Kind" json:"kind,omitempty"`
	// The field cardinality.
	Cardinality Field_Cardinality `protobuf:"varint,2,opt,name=cardinality,proto3,enum=google.protobuf.Field_Cardinality" json:"cardinality,omitempty"`
	// The field number.
	Number int32 `protobuf:"varint,3,opt,name=number,proto3" json:"number,omitempty"`
	// The field name.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// The field type URL, without the scheme, for message or enumeration
	// types. Example: `"type.googleapis.com/google.protobuf.Timestamp"`.
	TypeUrl string `protobuf:"bytes,6,opt,name=type_url,json=typeUrl,proto3" json:"type_url,omitempty"`
	// The index of the field type in `Type.oneofs`, for message or enumeration
	// types. The first type has index 1; zero means the type is not in the list.
	OneofIndex int32 `protobuf:"varint,7,opt,name=oneof_index,json=oneofIndex,proto3" json:"oneof_index,omitempty"`
	// Whether to use alternative packed wire representation.
	Packed bool `protobuf:"varint,8,opt,name=packed,proto3" json:"packed,omitempty"`
	// The protocol buffer options.
	Options []*Option `protobuf:"bytes,9,rep,name=options,proto3" json:"options,omitempty"`
	// The field JSON name.
	JsonName string `protobuf:"bytes,10,opt,name=json_name,json=jsonName,proto3" json:"json_name,omitempty"`
	// The string value of the default value of this field. Proto2 syntax only.
	DefaultValue  string `protobuf:"bytes,11,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Field) Reset() {
	*x = Field{}
	mi := &file_google_protobuf_type_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Field) ProtoMessage() {}

func (x *Field) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_type_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Field.ProtoReflect.Descriptor instead.
func (*Field) Descriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{1}
}

func (x *Field) GetKind() Field_Kind {
	if x != nil {
		return x.Kind
	}
	return Field_TYPE_UNKNOWN
}

func (x *Field) GetCardinality() Field_Cardinality {
	if x != nil {
		return x.Cardinality
	}
	return Field_CARDINALITY_UNKNOWN
}

func (x *Field) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *Field) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Field) GetTypeUrl() string {
	if x != nil {
		return x.TypeUrl
	}
	return ""
}

func (x *Field) GetOneofIndex() int32 {
	if x != nil {
		return x.OneofIndex
	}
	return 0
}

func (x *Field) GetPacked() bool {
	if x != nil {
		return x.Packed
	}
	return false
}

func (x *Field) GetOptions() []*Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Field) GetJsonName() string {
	if x != nil {
		return x.JsonName
	}
	return ""
}

func (x *Field) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

// Enum type definition.
type Enum struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enum type name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Enum value definitions.
	Enumvalue []*EnumValue `protobuf:"bytes,2,rep,name=enumvalue,proto3" json:"enumvalue,omitempty"`
	// Protocol buffer options.
	Options []*Option `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	// The source context.
	SourceContext *sourcecontextpb.SourceContext `protobuf:"bytes,4,opt,name=source_context,json=sourceContext,proto3" json:"source_context,omitempty"`
	// The source syntax.
	Syntax Syntax `protobuf:"varint,5,opt,name=syntax,proto3,enum=google.protobuf.Syntax" json:"syntax,omitempty"`
	// The source edition string, only valid when syntax is SYNTAX_EDITIONS.
	Edition       string `protobuf:"bytes,6,opt,name=edition,proto3" json:"edition,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Enum) Reset() {
	*x = Enum{}
	mi := &file_google_protobuf_type_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Enum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enum) ProtoMessage() {}

func (x *Enum) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_type_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enum.ProtoReflect.Descriptor instead.
func (*Enum) Descriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{2}
}

func (x *Enum) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Enum) GetEnumvalue() []*EnumValue {
	if x != nil {
		return x.Enumvalue
	}
	return nil
}

func (x *Enum) GetOptions() []*Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Enum) GetSourceContext() *sourcecontextpb.SourceContext {
	if x != nil {
		return x.SourceContext
	}
	return nil
}

func (x *Enum) GetSyntax() Syntax {
	if x != nil {
		return x.Syntax
	}
	return Syntax_SYNTAX_PROTO2
}

func (x *Enum) GetEdition() string {
	if x != nil {
		return x.Edition
	}
	return ""
}

// Enum value definition.
type EnumValue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enum value name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Enum value number.
	Number int32 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
	// Protocol buffer options.
	Options       []*Option `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnumValue) Reset() {
	*x = EnumValue{}
	mi := &file_google_protobuf_type_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnumValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumValue) ProtoMessage() {}

func (x *EnumValue) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_type_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumValue.ProtoReflect.Descriptor instead.
func (*EnumValue) Descriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{3}
}

func (x *EnumValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EnumValue) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *EnumValue) GetOptions() []*Option {
	if x != nil {
		return x.Options
	}
	return nil
}

// A protocol buffer option, which can be attached to a message, field,
// enumeration, etc.
type Option struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The option's name. For protobuf built-in options (options defined in
	// descriptor.proto), this is the short name. For example, `"map_entry"`.
	// For custom options, it should be the fully-qualified name. For example,
	// `"google.api.http"`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The option's value packed in an Any message. If the value is a primitive,
	// the corresponding wrapper type defined in google/protobuf/wrappers.proto
	// should be used. If the value is an enum, it should be stored as an int32
	// value using the google.protobuf.Int32Value type.
	Value         *anypb.Any `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Option) Reset() {
	*x = Option{}
	mi := &file_google_protobuf_type_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Option) ProtoMessage() {}

func (x *Option) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_type_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Option.ProtoReflect.Descriptor instead.
func (*Option) Descriptor() ([]byte, []int) {
	return file_google_protobuf_type_proto_rawDescGZIP(), []int{4}
}

func (x *Option) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Option) GetValue() *anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_google_protobuf_type_proto protoreflect.FileDescriptor

const file_google_protobuf_type_proto_rawDesc = "" +
	"\n" +
	"\x1agoogle/protobuf/type.proto\x12\x0fgoogle.protobuf\x1a\x19google/protobuf/any.proto\x1a$google/protobuf/source_context.proto\"\xa7\x02\n" +
	"\x04Type\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12.\n" +
	"\x06fields\x18\x02 \x03(\v2\x16.google.protobuf.FieldR\x06fields\x12\x16\n" +
	"\x06oneofs\x18\x03 \x03(\tR\x06oneofs\x121\n" +
	"\aoptions\x18\x04 \x03(\v2\x17.google.protobuf.OptionR\aoptions\x12E\n" +
	"\x0esource_context\x18\x05 \x01(\v2\x1e.google.protobuf.SourceContextR\rsourceContext\x12/\n" +
	"\x06syntax\x18\x06 \x01(\x0e2\x17.google.protobuf.SyntaxR\x06syntax\x12\x18\n" +
	"\aedition\x18\a \x01(\tR\aedition\"\xb4\x06\n" +
	"\x05Field\x12/\n" +
	"\x04kind\x18\x01 \x01(\x0e2\x1b.google.protobuf.Field.KindR\x04kind\x12D\n" +
	"\vcardinality\x18\x02 \x01(\x0e2\".google.protobuf.Field.CardinalityR\vcardinality\x12\x16\n" +
	"\x06number\x18\x03 \x01(\x05R\x06number\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x19\n" +
	"\btype_url\x18\x06 \x01(\tR\atypeUrl\x12\x1f\n" +
	"\voneof_index\x18\a \x01(\x05R\n" +
	"oneofIndex\x12\x16\n" +
	"\x06packed\x18\b \x01(\bR\x06packed\x121\n" +
	"\aoptions\x18\t \x03(\v2\x17.google.protobuf.OptionR\aoptions\x12\x1b\n" +
	"\tjson_name\x18\n" +
	" \x01(\tR\bjsonName\x12#\n" +
	"\rdefault_value\x18\v \x01(\tR\fdefaultValue\"\xc8\x02\n" +
	"\x04Kind\x12\x10\n" +
	"\fTYPE_UNKNOWN\x10\x00\x12\x0f\n" +
	"\vTYPE_DOUBLE\x10\x01\x12\x0e\n" +
	"\n" +
	"TYPE_FLOAT\x10\x02\x12\x0e\n" +
	"\n" +
	"TYPE_INT64\x10\x03\x12\x0f\n" +
	"\vTYPE_UINT64\x10\x04\x12\x0e\n" +
	"\n" +
	"TYPE_INT32\x10\x05\x12\x10\n" +
	"\fTYPE_FIXED64\x10\x06\x12\x10\n" +
	"\fTYPE_FIXED32\x10\a\x12\r\n" +
	"\tTYPE_BOOL\x10\b\x12\x0f\n" +
	"\vTYPE_STRING\x10\t\x12\x0e\n" +
	"\n" +
	"TYPE_GROUP\x10\n" +
	"\x12\x10\n" +
	"\fTYPE_MESSAGE\x10\v\x12\x0e\n" +
	"\n" +
	"TYPE_BYTES\x10\f\x12\x0f\n" +
	"\vTYPE_UINT32\x10\r\x12\r\n" +
	"\tTYPE_ENUM\x10\x0e\x12\x11\n" +
	"\rTYPE_SFIXED32\x10\x0f\x12\x11\n" +
	"\rTYPE_SFIXED64\x10\x10\x12\x0f\n" +
	"\vTYPE_SINT32\x10\x11\x12\x0f\n" +
	"\vTYPE_SINT64\x10\x12\"t\n" +
	"\vCardinality\x12\x17\n" +
	"\x13CARDINALITY_UNKNOWN\x10\x00\x12\x18\n" +
	"\x14CARDINALITY_OPTIONAL\x10\x01\x12\x18\n" +
	"\x14CARDINALITY_REQUIRED\x10\x02\x12\x18\n" +
	"\x14CARDINALITY_REPEATED\x10\x03\"\x99\x02\n" +
	"\x04Enum\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x128\n" +
	"\tenumvalue\x18\x02 \x03(\v2\x1a.google.protobuf.EnumValueR\tenumvalue\x121\n" +
	"\aoptions\x18\x03 \x03(\v2\x17.google.protobuf.OptionR\aoptions\x12E\n" +
	"\x0esource_context\x18\x04 \x01(\v2\x1e.google.protobuf.SourceContextR\rsourceContext\x12/\n" +
	"\x06syntax\x18\x05 \x01(\x0e2\x17.google.protobuf.SyntaxR\x06syntax\x12\x18\n" +
	"\aedition\x18\x06 \x01(\tR\aedition\"j\n" +
	"\tEnumValue\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06number\x18\x02 \x01(\x05R\x06number\x121\n" +
	"\aoptions\x18\x03 \x03(\v2\x17.google.protobuf.OptionR\aoptions\"H\n" +
	"\x06Option\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12*\n" +
	"\x05value\x18\x02 \x01(\v2\x14.google.protobuf.AnyR\x05value*C\n" +
	"\x06Syntax\x12\x11\n" +
	"\rSYNTAX_PROTO2\x10\x00\x12\x11\n" +
	"\rSYNTAX_PROTO3\x10\x01\x12\x13\n" +
	"\x0fSYNTAX_EDITIONS\x10\x02B{\n" +
	"\x13com.google.protobufB\tTypeProtoP\x01Z-google.golang.org/protobuf/types/known/typepb\xf8\x01\x01\xa2\x02\x03GPB\xaa\x02\x1eGoogle.Protobuf.WellKnownTypesb\x06proto3"

var (
	file_google_protobuf_type_proto_rawDescOnce sync.Once
	file_google_protobuf_type_proto_rawDescData []byte
)

func file_google_protobuf_type_proto_rawDescGZIP() []byte {
	file_google_protobuf_type_proto_rawDescOnce.Do(func() {
		file_google_protobuf_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_google_protobuf_type_proto_rawDesc), len(file_google_protobuf_type_proto_rawDesc)))
	})
	return file_google_protobuf_type_proto_rawDescData
}

var file_google_protobuf_type_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_protobuf_type_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_google_protobuf_type_proto_goTypes = []any{
	(Syntax)(0),                           // 0: google.protobuf.Syntax
	(Field_Kind)(0),                       // 1: google.protobuf.Field.Kind
	(Field_Cardinality)(0),                // 2: google.protobuf.Field.Cardinality
	(*Type)(nil),                          // 3: google.protobuf.Type
	(*Field)(nil),                         // 4: google.protobuf.Field
	(*Enum)(nil),                          // 5: google.protobuf.Enum
	(*EnumValue)(nil),                     // 6: google.protobuf.EnumValue
	(*Option)(nil),                        // 7: google.protobuf.Option
	(*sourcecontextpb.SourceContext)(nil), // 8: google.protobuf.SourceContext
	(*anypb.Any)(nil),                     // 9: google.protobuf.Any
}
var file_google_protobuf_type_proto_depIdxs = []int32{
	4,  // 0: google.protobuf.Type.fields:type_name -> google.protobuf.Field
	7,  // 1: google.protobuf.Type.options:type_name -> google.protobuf.Option
	8,  // 2: google.protobuf.Type.source_context:type_name -> google.protobuf.SourceContext
	0,  // 3: google.protobuf.Type.syntax:type_name -> google.protobuf.Syntax
	1,  // 4: google.protobuf.Field.kind:type_name -> google.protobuf.Field.Kind
	2,  // 5: google.protobuf.Field.cardinality:type_name -> google.protobuf.Field.Cardinality
	7,  // 6: google.protobuf.Field.options:type_name -> google.protobuf.Option
	6,  // 7: google.protobuf.Enum.enumvalue:type_name -> google.protobuf.EnumValue
	7,  // 8: google.protobuf.Enum.options:type_name -> google.protobuf.Option
	8,  // 9: google.protobuf.Enum.source_context:type_name -> google.protobuf.SourceContext
	0,  // 10: google.protobuf.Enum.syntax:type_name -> google.protobuf.Syntax
	7,  // 11: google.protobuf.EnumValue.options:type_name -> google.protobuf.Option
	9,  // 12: google.protobuf.Option.value:type_name -> google.protobuf.Any
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_google_protobuf_type_proto_init() }
func file_google_protobuf_type_proto_init() {
	if File_google_protobuf_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_google_protobuf_type_proto_rawDesc), len(file_google_protobuf_type_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_protobuf_type_proto_goTypes,
		DependencyIndexes: file_google_protobuf_type_proto_depIdxs,
		EnumInfos:         file_google_protobuf_type_proto_enumTypes,
		MessageInfos:      file_google_protobuf_type_proto_msgTypes,
	}.Build()
	File_google_protobuf_type_proto = out.File
	file_google_protobuf_type_proto_goTypes = nil
	file_google_protobuf_type_proto_depIdxs = nil
}
