import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

export const options = {
  "summaryTrendStats": [
    "avg",
    "min",
    "med",
    "max",
    "p(95)"
  ],
  "vus": 10,
  "duration": "15s",
  "iterations": 2,
  "maxRedirects": 10,
  "userAgent": "k6-load-test/1.0"
};

const scenarios = [
  {
    "name": "httpbin",
    "url": "https://httpbin.org/get?name=Sadashiva&project=load-test",
    "method": "GET",
    "weight": 100,
    "timeout": 30,
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer token"
    },
    "body": null,
    "expected_status": [
      200
    ],
    "response_checks": "// Example: check(response, {\n//   \"status is 200\": (r) => r.status === 200,\n//   \"response time < 500ms\": (r) => r.timings.duration < 500\n// });"
  }
];

function getRandomScenario() {
    const totalWeight = scenarios.reduce((sum, s) => sum + s.weight, 0);
    let random = Math.random() * totalWeight;

    for (const scenario of scenarios) {
        random -= scenario.weight;
        if (random <= 0) {
            return scenario;
        }
    }
    return scenarios[0];
}


export default function() {
    const scenario = getRandomScenario();

    const params = {
        headers: scenario.headers || {},
        timeout: (scenario.timeout || 30) + 's'
    };

    let response;

    if (scenario.method === 'GET') {
        response = http.get(scenario.url, params);
    } else if (scenario.method === 'POST') {
        response = http.post(scenario.url, JSON.stringify(scenario.body || {}), params);
    } else if (scenario.method === 'PUT') {
        response = http.put(scenario.url, JSON.stringify(scenario.body || {}), params);
    } else if (scenario.method === 'DELETE') {
        response = http.del(scenario.url, null, params);
    } else if (scenario.method === 'PATCH') {
        response = http.patch(scenario.url, JSON.stringify(scenario.body || {}), params);
    }

    // Basic checks
    const isSuccess = scenario.expected_status.includes(response.status);
    check(response, {
        [`${scenario.name} - Status is expected`]: (r) => scenario.expected_status.includes(r.status),
        [`${scenario.name} - Response time < ${scenario.timeout}s`]: (r) => r.timings.duration < (scenario.timeout * 1000)
    });

    // Custom response checks
    if (scenario.response_checks && scenario.response_checks.trim()) {
        try {
            eval(scenario.response_checks);
        } catch (e) {
            console.error(`Error in custom checks for ${scenario.name}: ${e.message}`);
        }
    }

    // Record errors
    errorRate.add(!isSuccess);

    // Think time
    if (0.5 > 0) {
        sleep(0.5);
    }
}
