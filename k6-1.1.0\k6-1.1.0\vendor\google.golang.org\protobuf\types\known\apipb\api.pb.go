// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: google/protobuf/api.proto

package apipb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	sourcecontextpb "google.golang.org/protobuf/types/known/sourcecontextpb"
	typepb "google.golang.org/protobuf/types/known/typepb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

// Api is a light-weight descriptor for an API Interface.
//
// Interfaces are also described as "protocol buffer services" in some contexts,
// such as by the "service" keyword in a .proto file, but they are different
// from API Services, which represent a concrete implementation of an interface
// as opposed to simply a description of methods and bindings. They are also
// sometimes simply referred to as "APIs" in other contexts, such as the name of
// this message itself. See https://cloud.google.com/apis/design/glossary for
// detailed terminology.
type Api struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The fully qualified name of this interface, including package name
	// followed by the interface's simple name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The methods of this interface, in unspecified order.
	Methods []*Method `protobuf:"bytes,2,rep,name=methods,proto3" json:"methods,omitempty"`
	// Any metadata attached to the interface.
	Options []*typepb.Option `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	// A version string for this interface. If specified, must have the form
	// `major-version.minor-version`, as in `1.10`. If the minor version is
	// omitted, it defaults to zero. If the entire version field is empty, the
	// major version is derived from the package name, as outlined below. If the
	// field is not empty, the version in the package name will be verified to be
	// consistent with what is provided here.
	//
	// The versioning schema uses [semantic
	// versioning](http://semver.org) where the major version number
	// indicates a breaking change and the minor version an additive,
	// non-breaking change. Both version numbers are signals to users
	// what to expect from different versions, and should be carefully
	// chosen based on the product plan.
	//
	// The major version is also reflected in the package name of the
	// interface, which must end in `v<major-version>`, as in
	// `google.feature.v1`. For major versions 0 and 1, the suffix can
	// be omitted. Zero major versions must only be used for
	// experimental, non-GA interfaces.
	Version string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	// Source context for the protocol buffer service represented by this
	// message.
	SourceContext *sourcecontextpb.SourceContext `protobuf:"bytes,5,opt,name=source_context,json=sourceContext,proto3" json:"source_context,omitempty"`
	// Included interfaces. See [Mixin][].
	Mixins []*Mixin `protobuf:"bytes,6,rep,name=mixins,proto3" json:"mixins,omitempty"`
	// The source syntax of the service.
	Syntax        typepb.Syntax `protobuf:"varint,7,opt,name=syntax,proto3,enum=google.protobuf.Syntax" json:"syntax,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Api) Reset() {
	*x = Api{}
	mi := &file_google_protobuf_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Api) ProtoMessage() {}

func (x *Api) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Api.ProtoReflect.Descriptor instead.
func (*Api) Descriptor() ([]byte, []int) {
	return file_google_protobuf_api_proto_rawDescGZIP(), []int{0}
}

func (x *Api) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Api) GetMethods() []*Method {
	if x != nil {
		return x.Methods
	}
	return nil
}

func (x *Api) GetOptions() []*typepb.Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Api) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Api) GetSourceContext() *sourcecontextpb.SourceContext {
	if x != nil {
		return x.SourceContext
	}
	return nil
}

func (x *Api) GetMixins() []*Mixin {
	if x != nil {
		return x.Mixins
	}
	return nil
}

func (x *Api) GetSyntax() typepb.Syntax {
	if x != nil {
		return x.Syntax
	}
	return typepb.Syntax(0)
}

// Method represents a method of an API interface.
type Method struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The simple name of this method.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// A URL of the input message type.
	RequestTypeUrl string `protobuf:"bytes,2,opt,name=request_type_url,json=requestTypeUrl,proto3" json:"request_type_url,omitempty"`
	// If true, the request is streamed.
	RequestStreaming bool `protobuf:"varint,3,opt,name=request_streaming,json=requestStreaming,proto3" json:"request_streaming,omitempty"`
	// The URL of the output message type.
	ResponseTypeUrl string `protobuf:"bytes,4,opt,name=response_type_url,json=responseTypeUrl,proto3" json:"response_type_url,omitempty"`
	// If true, the response is streamed.
	ResponseStreaming bool `protobuf:"varint,5,opt,name=response_streaming,json=responseStreaming,proto3" json:"response_streaming,omitempty"`
	// Any metadata attached to the method.
	Options []*typepb.Option `protobuf:"bytes,6,rep,name=options,proto3" json:"options,omitempty"`
	// The source syntax of this method.
	Syntax        typepb.Syntax `protobuf:"varint,7,opt,name=syntax,proto3,enum=google.protobuf.Syntax" json:"syntax,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Method) Reset() {
	*x = Method{}
	mi := &file_google_protobuf_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Method) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Method) ProtoMessage() {}

func (x *Method) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Method.ProtoReflect.Descriptor instead.
func (*Method) Descriptor() ([]byte, []int) {
	return file_google_protobuf_api_proto_rawDescGZIP(), []int{1}
}

func (x *Method) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Method) GetRequestTypeUrl() string {
	if x != nil {
		return x.RequestTypeUrl
	}
	return ""
}

func (x *Method) GetRequestStreaming() bool {
	if x != nil {
		return x.RequestStreaming
	}
	return false
}

func (x *Method) GetResponseTypeUrl() string {
	if x != nil {
		return x.ResponseTypeUrl
	}
	return ""
}

func (x *Method) GetResponseStreaming() bool {
	if x != nil {
		return x.ResponseStreaming
	}
	return false
}

func (x *Method) GetOptions() []*typepb.Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Method) GetSyntax() typepb.Syntax {
	if x != nil {
		return x.Syntax
	}
	return typepb.Syntax(0)
}

// Declares an API Interface to be included in this interface. The including
// interface must redeclare all the methods from the included interface, but
// documentation and options are inherited as follows:
//
//   - If after comment and whitespace stripping, the documentation
//     string of the redeclared method is empty, it will be inherited
//     from the original method.
//
//   - Each annotation belonging to the service config (http,
//     visibility) which is not set in the redeclared method will be
//     inherited.
//
//   - If an http annotation is inherited, the path pattern will be
//     modified as follows. Any version prefix will be replaced by the
//     version of the including interface plus the [root][] path if
//     specified.
//
// Example of a simple mixin:
//
//	package google.acl.v1;
//	service AccessControl {
//	  // Get the underlying ACL object.
//	  rpc GetAcl(GetAclRequest) returns (Acl) {
//	    option (google.api.http).get = "/v1/{resource=**}:getAcl";
//	  }
//	}
//
//	package google.storage.v2;
//	service Storage {
//	  rpc GetAcl(GetAclRequest) returns (Acl);
//
//	  // Get a data record.
//	  rpc GetData(GetDataRequest) returns (Data) {
//	    option (google.api.http).get = "/v2/{resource=**}";
//	  }
//	}
//
// Example of a mixin configuration:
//
//	apis:
//	- name: google.storage.v2.Storage
//	  mixins:
//	  - name: google.acl.v1.AccessControl
//
// The mixin construct implies that all methods in `AccessControl` are
// also declared with same name and request/response types in
// `Storage`. A documentation generator or annotation processor will
// see the effective `Storage.GetAcl` method after inheriting
// documentation and annotations as follows:
//
//	service Storage {
//	  // Get the underlying ACL object.
//	  rpc GetAcl(GetAclRequest) returns (Acl) {
//	    option (google.api.http).get = "/v2/{resource=**}:getAcl";
//	  }
//	  ...
//	}
//
// Note how the version in the path pattern changed from `v1` to `v2`.
//
// If the `root` field in the mixin is specified, it should be a
// relative path under which inherited HTTP paths are placed. Example:
//
//	apis:
//	- name: google.storage.v2.Storage
//	  mixins:
//	  - name: google.acl.v1.AccessControl
//	    root: acls
//
// This implies the following inherited HTTP annotation:
//
//	service Storage {
//	  // Get the underlying ACL object.
//	  rpc GetAcl(GetAclRequest) returns (Acl) {
//	    option (google.api.http).get = "/v2/acls/{resource=**}:getAcl";
//	  }
//	  ...
//	}
type Mixin struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The fully qualified name of the interface which is included.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// If non-empty specifies a path under which inherited HTTP paths
	// are rooted.
	Root          string `protobuf:"bytes,2,opt,name=root,proto3" json:"root,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mixin) Reset() {
	*x = Mixin{}
	mi := &file_google_protobuf_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mixin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mixin) ProtoMessage() {}

func (x *Mixin) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mixin.ProtoReflect.Descriptor instead.
func (*Mixin) Descriptor() ([]byte, []int) {
	return file_google_protobuf_api_proto_rawDescGZIP(), []int{2}
}

func (x *Mixin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Mixin) GetRoot() string {
	if x != nil {
		return x.Root
	}
	return ""
}

var File_google_protobuf_api_proto protoreflect.FileDescriptor

const file_google_protobuf_api_proto_rawDesc = "" +
	"\n" +
	"\x19google/protobuf/api.proto\x12\x0fgoogle.protobuf\x1a$google/protobuf/source_context.proto\x1a\x1agoogle/protobuf/type.proto\"\xc1\x02\n" +
	"\x03Api\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x121\n" +
	"\amethods\x18\x02 \x03(\v2\x17.google.protobuf.MethodR\amethods\x121\n" +
	"\aoptions\x18\x03 \x03(\v2\x17.google.protobuf.OptionR\aoptions\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12E\n" +
	"\x0esource_context\x18\x05 \x01(\v2\x1e.google.protobuf.SourceContextR\rsourceContext\x12.\n" +
	"\x06mixins\x18\x06 \x03(\v2\x16.google.protobuf.MixinR\x06mixins\x12/\n" +
	"\x06syntax\x18\a \x01(\x0e2\x17.google.protobuf.SyntaxR\x06syntax\"\xb2\x02\n" +
	"\x06Method\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12(\n" +
	"\x10request_type_url\x18\x02 \x01(\tR\x0erequestTypeUrl\x12+\n" +
	"\x11request_streaming\x18\x03 \x01(\bR\x10requestStreaming\x12*\n" +
	"\x11response_type_url\x18\x04 \x01(\tR\x0fresponseTypeUrl\x12-\n" +
	"\x12response_streaming\x18\x05 \x01(\bR\x11responseStreaming\x121\n" +
	"\aoptions\x18\x06 \x03(\v2\x17.google.protobuf.OptionR\aoptions\x12/\n" +
	"\x06syntax\x18\a \x01(\x0e2\x17.google.protobuf.SyntaxR\x06syntax\"/\n" +
	"\x05Mixin\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04root\x18\x02 \x01(\tR\x04rootBv\n" +
	"\x13com.google.protobufB\bApiProtoP\x01Z,google.golang.org/protobuf/types/known/apipb\xa2\x02\x03GPB\xaa\x02\x1eGoogle.Protobuf.WellKnownTypesb\x06proto3"

var (
	file_google_protobuf_api_proto_rawDescOnce sync.Once
	file_google_protobuf_api_proto_rawDescData []byte
)

func file_google_protobuf_api_proto_rawDescGZIP() []byte {
	file_google_protobuf_api_proto_rawDescOnce.Do(func() {
		file_google_protobuf_api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_google_protobuf_api_proto_rawDesc), len(file_google_protobuf_api_proto_rawDesc)))
	})
	return file_google_protobuf_api_proto_rawDescData
}

var file_google_protobuf_api_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_google_protobuf_api_proto_goTypes = []any{
	(*Api)(nil),                           // 0: google.protobuf.Api
	(*Method)(nil),                        // 1: google.protobuf.Method
	(*Mixin)(nil),                         // 2: google.protobuf.Mixin
	(*typepb.Option)(nil),                 // 3: google.protobuf.Option
	(*sourcecontextpb.SourceContext)(nil), // 4: google.protobuf.SourceContext
	(typepb.Syntax)(0),                    // 5: google.protobuf.Syntax
}
var file_google_protobuf_api_proto_depIdxs = []int32{
	1, // 0: google.protobuf.Api.methods:type_name -> google.protobuf.Method
	3, // 1: google.protobuf.Api.options:type_name -> google.protobuf.Option
	4, // 2: google.protobuf.Api.source_context:type_name -> google.protobuf.SourceContext
	2, // 3: google.protobuf.Api.mixins:type_name -> google.protobuf.Mixin
	5, // 4: google.protobuf.Api.syntax:type_name -> google.protobuf.Syntax
	3, // 5: google.protobuf.Method.options:type_name -> google.protobuf.Option
	5, // 6: google.protobuf.Method.syntax:type_name -> google.protobuf.Syntax
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_google_protobuf_api_proto_init() }
func file_google_protobuf_api_proto_init() {
	if File_google_protobuf_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_google_protobuf_api_proto_rawDesc), len(file_google_protobuf_api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_protobuf_api_proto_goTypes,
		DependencyIndexes: file_google_protobuf_api_proto_depIdxs,
		MessageInfos:      file_google_protobuf_api_proto_msgTypes,
	}.Build()
	File_google_protobuf_api_proto = out.File
	file_google_protobuf_api_proto_goTypes = nil
	file_google_protobuf_api_proto_depIdxs = nil
}
