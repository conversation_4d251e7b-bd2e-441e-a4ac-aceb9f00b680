// go run mksyscall_zos_s390x.go -o_sysnum zsysnum_zos_s390x.go -o_syscall zsyscall_zos_s390x.go -i_syscall syscall_zos_s390x.go -o_asm zsymaddr_zos_s390x.s
// Code generated by the command above; see README.md. DO NOT EDIT.

//go:build zos && s390x

package unix

const (
	SYS_LOG                             = 0x17  // 23
	SYS_COSH                            = 0x18  // 24
	SYS_TANH                            = 0x19  // 25
	SYS_EXP                             = 0x1A  // 26
	SYS_MODF                            = 0x1B  // 27
	SYS_LOG10                           = 0x1C  // 28
	SYS_FREXP                           = 0x1D  // 29
	SYS_LDEXP                           = 0x1E  // 30
	SYS_CEIL                            = 0x1F  // 31
	SYS_POW                             = 0x20  // 32
	SYS_SQRT                            = 0x21  // 33
	SYS_FLOOR                           = 0x22  // 34
	SYS_J1                              = 0x23  // 35
	SYS_FABS                            = 0x24  // 36
	SYS_FMOD                            = 0x25  // 37
	SYS_J0                              = 0x26  // 38
	SYS_YN                              = 0x27  // 39
	SYS_JN                              = 0x28  // 40
	SYS_Y0                              = 0x29  // 41
	SYS_Y1                              = 0x2A  // 42
	SYS_HYPOT                           = 0x2B  // 43
	SYS_ERF                             = 0x2C  // 44
	SYS_ERFC                            = 0x2D  // 45
	SYS_GAMMA                           = 0x2E  // 46
	SYS_ISALPHA                         = 0x30  // 48
	SYS_ISALNUM                         = 0x31  // 49
	SYS_ISLOWER                         = 0x32  // 50
	SYS_ISCNTRL                         = 0x33  // 51
	SYS_ISDIGIT                         = 0x34  // 52
	SYS_ISGRAPH                         = 0x35  // 53
	SYS_ISUPPER                         = 0x36  // 54
	SYS_ISPRINT                         = 0x37  // 55
	SYS_ISPUNCT                         = 0x38  // 56
	SYS_ISSPACE                         = 0x39  // 57
	SYS_SETLOCAL                        = 0x3A  // 58
	SYS_SETLOCALE                       = 0x3A  // 58
	SYS_ISXDIGIT                        = 0x3B  // 59
	SYS_TOLOWER                         = 0x3C  // 60
	SYS_TOUPPER                         = 0x3D  // 61
	SYS_ASIN                            = 0x3E  // 62
	SYS_SIN                             = 0x3F  // 63
	SYS_COS                             = 0x40  // 64
	SYS_TAN                             = 0x41  // 65
	SYS_SINH                            = 0x42  // 66
	SYS_ACOS                            = 0x43  // 67
	SYS_ATAN                            = 0x44  // 68
	SYS_ATAN2                           = 0x45  // 69
	SYS_FTELL                           = 0x46  // 70
	SYS_FGETPOS                         = 0x47  // 71
	SYS_FSEEK                           = 0x48  // 72
	SYS_FSETPOS                         = 0x49  // 73
	SYS_FERROR                          = 0x4A  // 74
	SYS_REWIND                          = 0x4B  // 75
	SYS_CLEARERR                        = 0x4C  // 76
	SYS_FEOF                            = 0x4D  // 77
	SYS_ATOL                            = 0x4E  // 78
	SYS_PERROR                          = 0x4F  // 79
	SYS_ATOF                            = 0x50  // 80
	SYS_ATOI                            = 0x51  // 81
	SYS_RAND                            = 0x52  // 82
	SYS_STRTOD                          = 0x53  // 83
	SYS_STRTOL                          = 0x54  // 84
	SYS_STRTOUL                         = 0x55  // 85
	SYS_MALLOC                          = 0x56  // 86
	SYS_SRAND                           = 0x57  // 87
	SYS_CALLOC                          = 0x58  // 88
	SYS_FREE                            = 0x59  // 89
	SYS_EXIT                            = 0x5A  // 90
	SYS_REALLOC                         = 0x5B  // 91
	SYS_ABORT                           = 0x5C  // 92
	SYS___ABORT                         = 0x5C  // 92
	SYS_ATEXIT                          = 0x5D  // 93
	SYS_RAISE                           = 0x5E  // 94
	SYS_SETJMP                          = 0x5F  // 95
	SYS_LONGJMP                         = 0x60  // 96
	SYS_SIGNAL                          = 0x61  // 97
	SYS_TMPNAM                          = 0x62  // 98
	SYS_REMOVE                          = 0x63  // 99
	SYS_RENAME                          = 0x64  // 100
	SYS_TMPFILE                         = 0x65  // 101
	SYS_FREOPEN                         = 0x66  // 102
	SYS_FCLOSE                          = 0x67  // 103
	SYS_FFLUSH                          = 0x68  // 104
	SYS_FOPEN                           = 0x69  // 105
	SYS_FSCANF                          = 0x6A  // 106
	SYS_SETBUF                          = 0x6B  // 107
	SYS_SETVBUF                         = 0x6C  // 108
	SYS_FPRINTF                         = 0x6D  // 109
	SYS_SSCANF                          = 0x6E  // 110
	SYS_PRINTF                          = 0x6F  // 111
	SYS_SCANF                           = 0x70  // 112
	SYS_SPRINTF                         = 0x71  // 113
	SYS_FGETC                           = 0x72  // 114
	SYS_VFPRINTF                        = 0x73  // 115
	SYS_VPRINTF                         = 0x74  // 116
	SYS_VSPRINTF                        = 0x75  // 117
	SYS_GETC                            = 0x76  // 118
	SYS_FGETS                           = 0x77  // 119
	SYS_FPUTC                           = 0x78  // 120
	SYS_FPUTS                           = 0x79  // 121
	SYS_PUTCHAR                         = 0x7A  // 122
	SYS_GETCHAR                         = 0x7B  // 123
	SYS_GETS                            = 0x7C  // 124
	SYS_PUTC                            = 0x7D  // 125
	SYS_FWRITE                          = 0x7E  // 126
	SYS_PUTS                            = 0x7F  // 127
	SYS_UNGETC                          = 0x80  // 128
	SYS_FREAD                           = 0x81  // 129
	SYS_WCSTOMBS                        = 0x82  // 130
	SYS_MBTOWC                          = 0x83  // 131
	SYS_WCTOMB                          = 0x84  // 132
	SYS_MBSTOWCS                        = 0x85  // 133
	SYS_WCSCPY                          = 0x86  // 134
	SYS_WCSCAT                          = 0x87  // 135
	SYS_WCSCHR                          = 0x88  // 136
	SYS_WCSCMP                          = 0x89  // 137
	SYS_WCSNCMP                         = 0x8A  // 138
	SYS_WCSCSPN                         = 0x8B  // 139
	SYS_WCSLEN                          = 0x8C  // 140
	SYS_WCSNCAT                         = 0x8D  // 141
	SYS_WCSSPN                          = 0x8E  // 142
	SYS_WCSNCPY                         = 0x8F  // 143
	SYS_ABS                             = 0x90  // 144
	SYS_DIV                             = 0x91  // 145
	SYS_LABS                            = 0x92  // 146
	SYS_STRNCPY                         = 0x93  // 147
	SYS_MEMCPY                          = 0x94  // 148
	SYS_MEMMOVE                         = 0x95  // 149
	SYS_STRCPY                          = 0x96  // 150
	SYS_STRCMP                          = 0x97  // 151
	SYS_STRCAT                          = 0x98  // 152
	SYS_STRNCAT                         = 0x99  // 153
	SYS_MEMCMP                          = 0x9A  // 154
	SYS_MEMCHR                          = 0x9B  // 155
	SYS_STRCOLL                         = 0x9C  // 156
	SYS_STRNCMP                         = 0x9D  // 157
	SYS_STRXFRM                         = 0x9E  // 158
	SYS_STRRCHR                         = 0x9F  // 159
	SYS_STRCHR                          = 0xA0  // 160
	SYS_STRCSPN                         = 0xA1  // 161
	SYS_STRPBRK                         = 0xA2  // 162
	SYS_MEMSET                          = 0xA3  // 163
	SYS_STRSPN                          = 0xA4  // 164
	SYS_STRSTR                          = 0xA5  // 165
	SYS_STRTOK                          = 0xA6  // 166
	SYS_DIFFTIME                        = 0xA7  // 167
	SYS_STRERROR                        = 0xA8  // 168
	SYS_STRLEN                          = 0xA9  // 169
	SYS_CLOCK                           = 0xAA  // 170
	SYS_CTIME                           = 0xAB  // 171
	SYS_MKTIME                          = 0xAC  // 172
	SYS_TIME                            = 0xAD  // 173
	SYS_ASCTIME                         = 0xAE  // 174
	SYS_MBLEN                           = 0xAF  // 175
	SYS_GMTIME                          = 0xB0  // 176
	SYS_LOCALTIM                        = 0xB1  // 177
	SYS_LOCALTIME                       = 0xB1  // 177
	SYS_STRFTIME                        = 0xB2  // 178
	SYS___GETCB                         = 0xB4  // 180
	SYS_FUPDATE                         = 0xB5  // 181
	SYS___FUPDT                         = 0xB5  // 181
	SYS_CLRMEMF                         = 0xBD  // 189
	SYS___CLRMF                         = 0xBD  // 189
	SYS_FETCHEP                         = 0xBF  // 191
	SYS___FTCHEP                        = 0xBF  // 191
	SYS_FLDATA                          = 0xC1  // 193
	SYS___FLDATA                        = 0xC1  // 193
	SYS_DYNFREE                         = 0xC2  // 194
	SYS___DYNFRE                        = 0xC2  // 194
	SYS_DYNALLOC                        = 0xC3  // 195
	SYS___DYNALL                        = 0xC3  // 195
	SYS___CDUMP                         = 0xC4  // 196
	SYS_CSNAP                           = 0xC5  // 197
	SYS___CSNAP                         = 0xC5  // 197
	SYS_CTRACE                          = 0xC6  // 198
	SYS___CTRACE                        = 0xC6  // 198
	SYS___CTEST                         = 0xC7  // 199
	SYS_SETENV                          = 0xC8  // 200
	SYS___SETENV                        = 0xC8  // 200
	SYS_CLEARENV                        = 0xC9  // 201
	SYS___CLRENV                        = 0xC9  // 201
	SYS___REGCOMP_STD                   = 0xEA  // 234
	SYS_NL_LANGINFO                     = 0xFC  // 252
	SYS_GETSYNTX                        = 0xFD  // 253
	SYS_ISBLANK                         = 0xFE  // 254
	SYS___ISBLNK                        = 0xFE  // 254
	SYS_ISWALNUM                        = 0xFF  // 255
	SYS_ISWALPHA                        = 0x100 // 256
	SYS_ISWBLANK                        = 0x101 // 257
	SYS___ISWBLK                        = 0x101 // 257
	SYS_ISWCNTRL                        = 0x102 // 258
	SYS_ISWDIGIT                        = 0x103 // 259
	SYS_ISWGRAPH                        = 0x104 // 260
	SYS_ISWLOWER                        = 0x105 // 261
	SYS_ISWPRINT                        = 0x106 // 262
	SYS_ISWPUNCT                        = 0x107 // 263
	SYS_ISWSPACE                        = 0x108 // 264
	SYS_ISWUPPER                        = 0x109 // 265
	SYS_ISWXDIGI                        = 0x10A // 266
	SYS_ISWXDIGIT                       = 0x10A // 266
	SYS_WCTYPE                          = 0x10B // 267
	SYS_ISWCTYPE                        = 0x10C // 268
	SYS_TOWLOWER                        = 0x10D // 269
	SYS_TOWUPPER                        = 0x10E // 270
	SYS_MBSINIT                         = 0x10F // 271
	SYS_WCTOB                           = 0x110 // 272
	SYS_MBRLEN                          = 0x111 // 273
	SYS_MBRTOWC                         = 0x112 // 274
	SYS_MBSRTOWC                        = 0x113 // 275
	SYS_MBSRTOWCS                       = 0x113 // 275
	SYS_WCRTOMB                         = 0x114 // 276
	SYS_WCSRTOMB                        = 0x115 // 277
	SYS_WCSRTOMBS                       = 0x115 // 277
	SYS___CSID                          = 0x116 // 278
	SYS___WCSID                         = 0x117 // 279
	SYS_STRPTIME                        = 0x118 // 280
	SYS___STRPTM                        = 0x118 // 280
	SYS_STRFMON                         = 0x119 // 281
	SYS___RPMTCH                        = 0x11A // 282
	SYS_WCSSTR                          = 0x11B // 283
	SYS_WCSTOK                          = 0x12C // 300
	SYS_WCSTOL                          = 0x12D // 301
	SYS_WCSTOD                          = 0x12E // 302
	SYS_WCSTOUL                         = 0x12F // 303
	SYS_WCSCOLL                         = 0x130 // 304
	SYS_WCSXFRM                         = 0x131 // 305
	SYS_WCSWIDTH                        = 0x132 // 306
	SYS_WCWIDTH                         = 0x133 // 307
	SYS_WCSFTIME                        = 0x134 // 308
	SYS_SWPRINTF                        = 0x135 // 309
	SYS_VSWPRINT                        = 0x136 // 310
	SYS_VSWPRINTF                       = 0x136 // 310
	SYS_SWSCANF                         = 0x137 // 311
	SYS_REGCOMP                         = 0x138 // 312
	SYS_REGEXEC                         = 0x139 // 313
	SYS_REGFREE                         = 0x13A // 314
	SYS_REGERROR                        = 0x13B // 315
	SYS_FGETWC                          = 0x13C // 316
	SYS_FGETWS                          = 0x13D // 317
	SYS_FPUTWC                          = 0x13E // 318
	SYS_FPUTWS                          = 0x13F // 319
	SYS_GETWC                           = 0x140 // 320
	SYS_GETWCHAR                        = 0x141 // 321
	SYS_PUTWC                           = 0x142 // 322
	SYS_PUTWCHAR                        = 0x143 // 323
	SYS_UNGETWC                         = 0x144 // 324
	SYS_ICONV_OPEN                      = 0x145 // 325
	SYS_ICONV                           = 0x146 // 326
	SYS_ICONV_CLOSE                     = 0x147 // 327
	SYS_ISMCCOLLEL                      = 0x14C // 332
	SYS_STRTOCOLL                       = 0x14D // 333
	SYS_COLLTOSTR                       = 0x14E // 334
	SYS_COLLEQUIV                       = 0x14F // 335
	SYS_COLLRANGE                       = 0x150 // 336
	SYS_CCLASS                          = 0x151 // 337
	SYS_COLLORDER                       = 0x152 // 338
	SYS___DEMANGLE                      = 0x154 // 340
	SYS_FDOPEN                          = 0x155 // 341
	SYS___ERRNO                         = 0x156 // 342
	SYS___ERRNO2                        = 0x157 // 343
	SYS___TERROR                        = 0x158 // 344
	SYS_MAXCOLL                         = 0x169 // 361
	SYS_GETMCCOLL                       = 0x16A // 362
	SYS_GETWMCCOLL                      = 0x16B // 363
	SYS___ERR2AD                        = 0x16C // 364
	SYS_DLLQUERYFN                      = 0x16D // 365
	SYS_DLLQUERYVAR                     = 0x16E // 366
	SYS_DLLFREE                         = 0x16F // 367
	SYS_DLLLOAD                         = 0x170 // 368
	SYS__EXIT                           = 0x174 // 372
	SYS_ACCESS                          = 0x175 // 373
	SYS_ALARM                           = 0x176 // 374
	SYS_CFGETISPEED                     = 0x177 // 375
	SYS_CFGETOSPEED                     = 0x178 // 376
	SYS_CFSETISPEED                     = 0x179 // 377
	SYS_CFSETOSPEED                     = 0x17A // 378
	SYS_CHDIR                           = 0x17B // 379
	SYS_CHMOD                           = 0x17C // 380
	SYS_CHOWN                           = 0x17D // 381
	SYS_CLOSE                           = 0x17E // 382
	SYS_CLOSEDIR                        = 0x17F // 383
	SYS_CREAT                           = 0x180 // 384
	SYS_CTERMID                         = 0x181 // 385
	SYS_DUP                             = 0x182 // 386
	SYS_DUP2                            = 0x183 // 387
	SYS_EXECL                           = 0x184 // 388
	SYS_EXECLE                          = 0x185 // 389
	SYS_EXECLP                          = 0x186 // 390
	SYS_EXECV                           = 0x187 // 391
	SYS_EXECVE                          = 0x188 // 392
	SYS_EXECVP                          = 0x189 // 393
	SYS_FCHMOD                          = 0x18A // 394
	SYS_FCHOWN                          = 0x18B // 395
	SYS_FCNTL                           = 0x18C // 396
	SYS_FILENO                          = 0x18D // 397
	SYS_FORK                            = 0x18E // 398
	SYS_FPATHCONF                       = 0x18F // 399
	SYS_FSTAT                           = 0x190 // 400
	SYS_FSYNC                           = 0x191 // 401
	SYS_FTRUNCATE                       = 0x192 // 402
	SYS_GETCWD                          = 0x193 // 403
	SYS_GETEGID                         = 0x194 // 404
	SYS_GETEUID                         = 0x195 // 405
	SYS_GETGID                          = 0x196 // 406
	SYS_GETGRGID                        = 0x197 // 407
	SYS_GETGRNAM                        = 0x198 // 408
	SYS_GETGROUPS                       = 0x199 // 409
	SYS_GETLOGIN                        = 0x19A // 410
	SYS_W_GETMNTENT                     = 0x19B // 411
	SYS_GETPGRP                         = 0x19C // 412
	SYS_GETPID                          = 0x19D // 413
	SYS_GETPPID                         = 0x19E // 414
	SYS_GETPWNAM                        = 0x19F // 415
	SYS_GETPWUID                        = 0x1A0 // 416
	SYS_GETUID                          = 0x1A1 // 417
	SYS_W_IOCTL                         = 0x1A2 // 418
	SYS_ISATTY                          = 0x1A3 // 419
	SYS_KILL                            = 0x1A4 // 420
	SYS_LINK                            = 0x1A5 // 421
	SYS_LSEEK                           = 0x1A6 // 422
	SYS_LSTAT                           = 0x1A7 // 423
	SYS_MKDIR                           = 0x1A8 // 424
	SYS_MKFIFO                          = 0x1A9 // 425
	SYS_MKNOD                           = 0x1AA // 426
	SYS_MOUNT                           = 0x1AB // 427
	SYS_OPEN                            = 0x1AC // 428
	SYS_OPENDIR                         = 0x1AD // 429
	SYS_PATHCONF                        = 0x1AE // 430
	SYS_PAUSE                           = 0x1AF // 431
	SYS_PIPE                            = 0x1B0 // 432
	SYS_W_GETPSENT                      = 0x1B1 // 433
	SYS_READ                            = 0x1B2 // 434
	SYS_READDIR                         = 0x1B3 // 435
	SYS_READLINK                        = 0x1B4 // 436
	SYS_REWINDDIR                       = 0x1B5 // 437
	SYS_RMDIR                           = 0x1B6 // 438
	SYS_SETEGID                         = 0x1B7 // 439
	SYS_SETEUID                         = 0x1B8 // 440
	SYS_SETGID                          = 0x1B9 // 441
	SYS_SETPGID                         = 0x1BA // 442
	SYS_SETSID                          = 0x1BB // 443
	SYS_SETUID                          = 0x1BC // 444
	SYS_SIGACTION                       = 0x1BD // 445
	SYS_SIGADDSET                       = 0x1BE // 446
	SYS_SIGDELSET                       = 0x1BF // 447
	SYS_SIGEMPTYSET                     = 0x1C0 // 448
	SYS_SIGFILLSET                      = 0x1C1 // 449
	SYS_SIGISMEMBER                     = 0x1C2 // 450
	SYS_SIGLONGJMP                      = 0x1C3 // 451
	SYS_SIGPENDING                      = 0x1C4 // 452
	SYS_SIGPROCMASK                     = 0x1C5 // 453
	SYS_SIGSETJMP                       = 0x1C6 // 454
	SYS_SIGSUSPEND                      = 0x1C7 // 455
	SYS_SLEEP                           = 0x1C8 // 456
	SYS_STAT                            = 0x1C9 // 457
	SYS_W_STATFS                        = 0x1CA // 458
	SYS_SYMLINK                         = 0x1CB // 459
	SYS_SYSCONF                         = 0x1CC // 460
	SYS_TCDRAIN                         = 0x1CD // 461
	SYS_TCFLOW                          = 0x1CE // 462
	SYS_TCFLUSH                         = 0x1CF // 463
	SYS_TCGETATTR                       = 0x1D0 // 464
	SYS_TCGETPGRP                       = 0x1D1 // 465
	SYS_TCSENDBREAK                     = 0x1D2 // 466
	SYS_TCSETATTR                       = 0x1D3 // 467
	SYS_TCSETPGRP                       = 0x1D4 // 468
	SYS_TIMES                           = 0x1D5 // 469
	SYS_TTYNAME                         = 0x1D6 // 470
	SYS_TZSET                           = 0x1D7 // 471
	SYS_UMASK                           = 0x1D8 // 472
	SYS_UMOUNT                          = 0x1D9 // 473
	SYS_UNAME                           = 0x1DA // 474
	SYS_UNLINK                          = 0x1DB // 475
	SYS_UTIME                           = 0x1DC // 476
	SYS_WAIT                            = 0x1DD // 477
	SYS_WAITPID                         = 0x1DE // 478
	SYS_WRITE                           = 0x1DF // 479
	SYS_CHAUDIT                         = 0x1E0 // 480
	SYS_FCHAUDIT                        = 0x1E1 // 481
	SYS_GETGROUPSBYNAME                 = 0x1E2 // 482
	SYS_SIGWAIT                         = 0x1E3 // 483
	SYS_PTHREAD_EXIT                    = 0x1E4 // 484
	SYS_PTHREAD_KILL                    = 0x1E5 // 485
	SYS_PTHREAD_ATTR_INIT               = 0x1E6 // 486
	SYS_PTHREAD_ATTR_DESTROY            = 0x1E7 // 487
	SYS_PTHREAD_ATTR_SETSTACKSIZE       = 0x1E8 // 488
	SYS_PTHREAD_ATTR_GETSTACKSIZE       = 0x1E9 // 489
	SYS_PTHREAD_ATTR_SETDETACHSTATE     = 0x1EA // 490
	SYS_PTHREAD_ATTR_GETDETACHSTATE     = 0x1EB // 491
	SYS_PTHREAD_ATTR_SETWEIGHT_NP       = 0x1EC // 492
	SYS_PTHREAD_ATTR_GETWEIGHT_NP       = 0x1ED // 493
	SYS_PTHREAD_CANCEL                  = 0x1EE // 494
	SYS_PTHREAD_CLEANUP_PUSH            = 0x1EF // 495
	SYS_PTHREAD_CLEANUP_POP             = 0x1F0 // 496
	SYS_PTHREAD_CONDATTR_INIT           = 0x1F1 // 497
	SYS_PTHREAD_CONDATTR_DESTROY        = 0x1F2 // 498
	SYS_PTHREAD_COND_INIT               = 0x1F3 // 499
	SYS_PTHREAD_COND_DESTROY            = 0x1F4 // 500
	SYS_PTHREAD_COND_SIGNAL             = 0x1F5 // 501
	SYS_PTHREAD_COND_BROADCAST          = 0x1F6 // 502
	SYS_PTHREAD_COND_WAIT               = 0x1F7 // 503
	SYS_PTHREAD_COND_TIMEDWAIT          = 0x1F8 // 504
	SYS_PTHREAD_CREATE                  = 0x1F9 // 505
	SYS_PTHREAD_DETACH                  = 0x1FA // 506
	SYS_PTHREAD_EQUAL                   = 0x1FB // 507
	SYS_PTHREAD_GETSPECIFIC             = 0x1FC // 508
	SYS_PTHREAD_JOIN                    = 0x1FD // 509
	SYS_PTHREAD_KEY_CREATE              = 0x1FE // 510
	SYS_PTHREAD_MUTEXATTR_INIT          = 0x1FF // 511
	SYS_PTHREAD_MUTEXATTR_DESTROY       = 0x200 // 512
	SYS_PTHREAD_MUTEXATTR_SETKIND_NP    = 0x201 // 513
	SYS_PTHREAD_MUTEXATTR_GETKIND_NP    = 0x202 // 514
	SYS_PTHREAD_MUTEX_INIT              = 0x203 // 515
	SYS_PTHREAD_MUTEX_DESTROY           = 0x204 // 516
	SYS_PTHREAD_MUTEX_LOCK              = 0x205 // 517
	SYS_PTHREAD_MUTEX_TRYLOCK           = 0x206 // 518
	SYS_PTHREAD_MUTEX_UNLOCK            = 0x207 // 519
	SYS_PTHREAD_ONCE                    = 0x209 // 521
	SYS_PTHREAD_SELF                    = 0x20A // 522
	SYS_PTHREAD_SETINTR                 = 0x20B // 523
	SYS_PTHREAD_SETINTRTYPE             = 0x20C // 524
	SYS_PTHREAD_SETSPECIFIC             = 0x20D // 525
	SYS_PTHREAD_TESTINTR                = 0x20E // 526
	SYS_PTHREAD_YIELD                   = 0x20F // 527
	SYS_TW_OPEN                         = 0x210 // 528
	SYS_TW_FCNTL                        = 0x211 // 529
	SYS_PTHREAD_JOIN_D4_NP              = 0x212 // 530
	SYS_PTHREAD_CONDATTR_SETKIND_NP     = 0x213 // 531
	SYS_PTHREAD_CONDATTR_GETKIND_NP     = 0x214 // 532
	SYS_EXTLINK_NP                      = 0x215 // 533
	SYS___PASSWD                        = 0x216 // 534
	SYS_SETGROUPS                       = 0x217 // 535
	SYS_INITGROUPS                      = 0x218 // 536
	SYS_WCSPBRK                         = 0x23F // 575
	SYS_WCSRCHR                         = 0x240 // 576
	SYS_SVC99                           = 0x241 // 577
	SYS___SVC99                         = 0x241 // 577
	SYS_WCSWCS                          = 0x242 // 578
	SYS_LOCALECO                        = 0x243 // 579
	SYS_LOCALECONV                      = 0x243 // 579
	SYS___LIBREL                        = 0x244 // 580
	SYS_RELEASE                         = 0x245 // 581
	SYS___RLSE                          = 0x245 // 581
	SYS_FLOCATE                         = 0x246 // 582
	SYS___FLOCT                         = 0x246 // 582
	SYS_FDELREC                         = 0x247 // 583
	SYS___FDLREC                        = 0x247 // 583
	SYS_FETCH                           = 0x248 // 584
	SYS___FETCH                         = 0x248 // 584
	SYS_QSORT                           = 0x249 // 585
	SYS_GETENV                          = 0x24A // 586
	SYS_SYSTEM                          = 0x24B // 587
	SYS_BSEARCH                         = 0x24C // 588
	SYS_LDIV                            = 0x24D // 589
	SYS___THROW                         = 0x25E // 606
	SYS___RETHROW                       = 0x25F // 607
	SYS___CLEANUPCATCH                  = 0x260 // 608
	SYS___CATCHMATCH                    = 0x261 // 609
	SYS___CLEAN2UPCATCH                 = 0x262 // 610
	SYS_PUTENV                          = 0x26A // 618
	SYS___GETENV                        = 0x26F // 623
	SYS_GETPRIORITY                     = 0x270 // 624
	SYS_NICE                            = 0x271 // 625
	SYS_SETPRIORITY                     = 0x272 // 626
	SYS_GETITIMER                       = 0x273 // 627
	SYS_SETITIMER                       = 0x274 // 628
	SYS_MSGCTL                          = 0x275 // 629
	SYS_MSGGET                          = 0x276 // 630
	SYS_MSGRCV                          = 0x277 // 631
	SYS_MSGSND                          = 0x278 // 632
	SYS_MSGXRCV                         = 0x279 // 633
	SYS___MSGXR                         = 0x279 // 633
	SYS_SEMCTL                          = 0x27A // 634
	SYS_SEMGET                          = 0x27B // 635
	SYS_SEMOP                           = 0x27C // 636
	SYS_SHMAT                           = 0x27D // 637
	SYS_SHMCTL                          = 0x27E // 638
	SYS_SHMDT                           = 0x27F // 639
	SYS_SHMGET                          = 0x280 // 640
	SYS___GETIPC                        = 0x281 // 641
	SYS_SETGRENT                        = 0x282 // 642
	SYS_GETGRENT                        = 0x283 // 643
	SYS_ENDGRENT                        = 0x284 // 644
	SYS_SETPWENT                        = 0x285 // 645
	SYS_GETPWENT                        = 0x286 // 646
	SYS_ENDPWENT                        = 0x287 // 647
	SYS_BSD_SIGNAL                      = 0x288 // 648
	SYS_KILLPG                          = 0x289 // 649
	SYS_SIGALTSTACK                     = 0x28A // 650
	SYS_SIGHOLD                         = 0x28B // 651
	SYS_SIGIGNORE                       = 0x28C // 652
	SYS_SIGINTERRUPT                    = 0x28D // 653
	SYS_SIGPAUSE                        = 0x28E // 654
	SYS_SIGRELSE                        = 0x28F // 655
	SYS_SIGSET                          = 0x290 // 656
	SYS_SIGSTACK                        = 0x291 // 657
	SYS_GETRLIMIT                       = 0x292 // 658
	SYS_SETRLIMIT                       = 0x293 // 659
	SYS_GETRUSAGE                       = 0x294 // 660
	SYS_MMAP                            = 0x295 // 661
	SYS_MPROTECT                        = 0x296 // 662
	SYS_MSYNC                           = 0x297 // 663
	SYS_MUNMAP                          = 0x298 // 664
	SYS_CONFSTR                         = 0x299 // 665
	SYS_GETOPT                          = 0x29A // 666
	SYS_LCHOWN                          = 0x29B // 667
	SYS_TRUNCATE                        = 0x29C // 668
	SYS_GETSUBOPT                       = 0x29D // 669
	SYS_SETPGRP                         = 0x29E // 670
	SYS___GDERR                         = 0x29F // 671
	SYS___TZONE                         = 0x2A0 // 672
	SYS___DLGHT                         = 0x2A1 // 673
	SYS___OPARGF                        = 0x2A2 // 674
	SYS___OPOPTF                        = 0x2A3 // 675
	SYS___OPINDF                        = 0x2A4 // 676
	SYS___OPERRF                        = 0x2A5 // 677
	SYS_GETDATE                         = 0x2A6 // 678
	SYS_WAIT3                           = 0x2A7 // 679
	SYS_WAITID                          = 0x2A8 // 680
	SYS___CATTRM                        = 0x2A9 // 681
	SYS___GDTRM                         = 0x2AA // 682
	SYS___RNDTRM                        = 0x2AB // 683
	SYS_CRYPT                           = 0x2AC // 684
	SYS_ENCRYPT                         = 0x2AD // 685
	SYS_SETKEY                          = 0x2AE // 686
	SYS___CNVBLK                        = 0x2AF // 687
	SYS___CRYTRM                        = 0x2B0 // 688
	SYS___ECRTRM                        = 0x2B1 // 689
	SYS_DRAND48                         = 0x2B2 // 690
	SYS_ERAND48                         = 0x2B3 // 691
	SYS_FSTATVFS                        = 0x2B4 // 692
	SYS_STATVFS                         = 0x2B5 // 693
	SYS_CATCLOSE                        = 0x2B6 // 694
	SYS_CATGETS                         = 0x2B7 // 695
	SYS_CATOPEN                         = 0x2B8 // 696
	SYS_BCMP                            = 0x2B9 // 697
	SYS_BCOPY                           = 0x2BA // 698
	SYS_BZERO                           = 0x2BB // 699
	SYS_FFS                             = 0x2BC // 700
	SYS_INDEX                           = 0x2BD // 701
	SYS_RINDEX                          = 0x2BE // 702
	SYS_STRCASECMP                      = 0x2BF // 703
	SYS_STRDUP                          = 0x2C0 // 704
	SYS_STRNCASECMP                     = 0x2C1 // 705
	SYS_INITSTATE                       = 0x2C2 // 706
	SYS_SETSTATE                        = 0x2C3 // 707
	SYS_RANDOM                          = 0x2C4 // 708
	SYS_SRANDOM                         = 0x2C5 // 709
	SYS_HCREATE                         = 0x2C6 // 710
	SYS_HDESTROY                        = 0x2C7 // 711
	SYS_HSEARCH                         = 0x2C8 // 712
	SYS_LFIND                           = 0x2C9 // 713
	SYS_LSEARCH                         = 0x2CA // 714
	SYS_TDELETE                         = 0x2CB // 715
	SYS_TFIND                           = 0x2CC // 716
	SYS_TSEARCH                         = 0x2CD // 717
	SYS_TWALK                           = 0x2CE // 718
	SYS_INSQUE                          = 0x2CF // 719
	SYS_REMQUE                          = 0x2D0 // 720
	SYS_POPEN                           = 0x2D1 // 721
	SYS_PCLOSE                          = 0x2D2 // 722
	SYS_SWAB                            = 0x2D3 // 723
	SYS_MEMCCPY                         = 0x2D4 // 724
	SYS_GETPAGESIZE                     = 0x2D8 // 728
	SYS_FCHDIR                          = 0x2D9 // 729
	SYS___OCLCK                         = 0x2DA // 730
	SYS___ATOE                          = 0x2DB // 731
	SYS___ATOE_L                        = 0x2DC // 732
	SYS___ETOA                          = 0x2DD // 733
	SYS___ETOA_L                        = 0x2DE // 734
	SYS_SETUTXENT                       = 0x2DF // 735
	SYS_GETUTXENT                       = 0x2E0 // 736
	SYS_ENDUTXENT                       = 0x2E1 // 737
	SYS_GETUTXID                        = 0x2E2 // 738
	SYS_GETUTXLINE                      = 0x2E3 // 739
	SYS_PUTUTXLINE                      = 0x2E4 // 740
	SYS_FMTMSG                          = 0x2E5 // 741
	SYS_JRAND48                         = 0x2E6 // 742
	SYS_LRAND48                         = 0x2E7 // 743
	SYS_MRAND48                         = 0x2E8 // 744
	SYS_NRAND48                         = 0x2E9 // 745
	SYS_LCONG48                         = 0x2EA // 746
	SYS_SRAND48                         = 0x2EB // 747
	SYS_SEED48                          = 0x2EC // 748
	SYS_ISASCII                         = 0x2ED // 749
	SYS_TOASCII                         = 0x2EE // 750
	SYS_A64L                            = 0x2EF // 751
	SYS_L64A                            = 0x2F0 // 752
	SYS_UALARM                          = 0x2F1 // 753
	SYS_USLEEP                          = 0x2F2 // 754
	SYS___UTXTRM                        = 0x2F3 // 755
	SYS___SRCTRM                        = 0x2F4 // 756
	SYS_FTIME                           = 0x2F5 // 757
	SYS_GETTIMEOFDAY                    = 0x2F6 // 758
	SYS_DBM_CLEARERR                    = 0x2F7 // 759
	SYS_DBM_CLOSE                       = 0x2F8 // 760
	SYS_DBM_DELETE                      = 0x2F9 // 761
	SYS_DBM_ERROR                       = 0x2FA // 762
	SYS_DBM_FETCH                       = 0x2FB // 763
	SYS_DBM_FIRSTKEY                    = 0x2FC // 764
	SYS_DBM_NEXTKEY                     = 0x2FD // 765
	SYS_DBM_OPEN                        = 0x2FE // 766
	SYS_DBM_STORE                       = 0x2FF // 767
	SYS___NDMTRM                        = 0x300 // 768
	SYS_FTOK                            = 0x301 // 769
	SYS_BASENAME                        = 0x302 // 770
	SYS_DIRNAME                         = 0x303 // 771
	SYS_GETDTABLESIZE                   = 0x304 // 772
	SYS_MKSTEMP                         = 0x305 // 773
	SYS_MKTEMP                          = 0x306 // 774
	SYS_NFTW                            = 0x307 // 775
	SYS_GETWD                           = 0x308 // 776
	SYS_LOCKF                           = 0x309 // 777
	SYS__LONGJMP                        = 0x30D // 781
	SYS__SETJMP                         = 0x30E // 782
	SYS_VFORK                           = 0x30F // 783
	SYS_WORDEXP                         = 0x310 // 784
	SYS_WORDFREE                        = 0x311 // 785
	SYS_GETPGID                         = 0x312 // 786
	SYS_GETSID                          = 0x313 // 787
	SYS___UTMPXNAME                     = 0x314 // 788
	SYS_CUSERID                         = 0x315 // 789
	SYS_GETPASS                         = 0x316 // 790
	SYS_FNMATCH                         = 0x317 // 791
	SYS_FTW                             = 0x318 // 792
	SYS_GETW                            = 0x319 // 793
	SYS_GLOB                            = 0x31A // 794
	SYS_GLOBFREE                        = 0x31B // 795
	SYS_PUTW                            = 0x31C // 796
	SYS_SEEKDIR                         = 0x31D // 797
	SYS_TELLDIR                         = 0x31E // 798
	SYS_TEMPNAM                         = 0x31F // 799
	SYS_ACOSH                           = 0x320 // 800
	SYS_ASINH                           = 0x321 // 801
	SYS_ATANH                           = 0x322 // 802
	SYS_CBRT                            = 0x323 // 803
	SYS_EXPM1                           = 0x324 // 804
	SYS_ILOGB                           = 0x325 // 805
	SYS_LOGB                            = 0x326 // 806
	SYS_LOG1P                           = 0x327 // 807
	SYS_NEXTAFTER                       = 0x328 // 808
	SYS_RINT                            = 0x329 // 809
	SYS_REMAINDER                       = 0x32A // 810
	SYS_SCALB                           = 0x32B // 811
	SYS_LGAMMA                          = 0x32C // 812
	SYS_TTYSLOT                         = 0x32D // 813
	SYS_GETTIMEOFDAY_R                  = 0x32E // 814
	SYS_SYNC                            = 0x32F // 815
	SYS_SPAWN                           = 0x330 // 816
	SYS_SPAWNP                          = 0x331 // 817
	SYS_GETLOGIN_UU                     = 0x332 // 818
	SYS_ECVT                            = 0x333 // 819
	SYS_FCVT                            = 0x334 // 820
	SYS_GCVT                            = 0x335 // 821
	SYS_ACCEPT                          = 0x336 // 822
	SYS_BIND                            = 0x337 // 823
	SYS_CONNECT                         = 0x338 // 824
	SYS_ENDHOSTENT                      = 0x339 // 825
	SYS_ENDPROTOENT                     = 0x33A // 826
	SYS_ENDSERVENT                      = 0x33B // 827
	SYS_GETHOSTBYADDR_R                 = 0x33C // 828
	SYS_GETHOSTBYADDR                   = 0x33D // 829
	SYS_GETHOSTBYNAME_R                 = 0x33E // 830
	SYS_GETHOSTBYNAME                   = 0x33F // 831
	SYS_GETHOSTENT                      = 0x340 // 832
	SYS_GETHOSTID                       = 0x341 // 833
	SYS_GETHOSTNAME                     = 0x342 // 834
	SYS_GETNETBYADDR                    = 0x343 // 835
	SYS_GETNETBYNAME                    = 0x344 // 836
	SYS_GETNETENT                       = 0x345 // 837
	SYS_GETPEERNAME                     = 0x346 // 838
	SYS_GETPROTOBYNAME                  = 0x347 // 839
	SYS_GETPROTOBYNUMBER                = 0x348 // 840
	SYS_GETPROTOENT                     = 0x349 // 841
	SYS_GETSERVBYNAME                   = 0x34A // 842
	SYS_GETSERVBYPORT                   = 0x34B // 843
	SYS_GETSERVENT                      = 0x34C // 844
	SYS_GETSOCKNAME                     = 0x34D // 845
	SYS_GETSOCKOPT                      = 0x34E // 846
	SYS_INET_ADDR                       = 0x34F // 847
	SYS_INET_LNAOF                      = 0x350 // 848
	SYS_INET_MAKEADDR                   = 0x351 // 849
	SYS_INET_NETOF                      = 0x352 // 850
	SYS_INET_NETWORK                    = 0x353 // 851
	SYS_INET_NTOA                       = 0x354 // 852
	SYS_IOCTL                           = 0x355 // 853
	SYS_LISTEN                          = 0x356 // 854
	SYS_READV                           = 0x357 // 855
	SYS_RECV                            = 0x358 // 856
	SYS_RECVFROM                        = 0x359 // 857
	SYS_SELECT                          = 0x35B // 859
	SYS_SELECTEX                        = 0x35C // 860
	SYS_SEND                            = 0x35D // 861
	SYS_SENDTO                          = 0x35F // 863
	SYS_SETHOSTENT                      = 0x360 // 864
	SYS_SETNETENT                       = 0x361 // 865
	SYS_SETPEER                         = 0x362 // 866
	SYS_SETPROTOENT                     = 0x363 // 867
	SYS_SETSERVENT                      = 0x364 // 868
	SYS_SETSOCKOPT                      = 0x365 // 869
	SYS_SHUTDOWN                        = 0x366 // 870
	SYS_SOCKET                          = 0x367 // 871
	SYS_SOCKETPAIR                      = 0x368 // 872
	SYS_WRITEV                          = 0x369 // 873
	SYS_CHROOT                          = 0x36A // 874
	SYS_W_STATVFS                       = 0x36B // 875
	SYS_ULIMIT                          = 0x36C // 876
	SYS_ISNAN                           = 0x36D // 877
	SYS_UTIMES                          = 0x36E // 878
	SYS___H_ERRNO                       = 0x36F // 879
	SYS_ENDNETENT                       = 0x370 // 880
	SYS_CLOSELOG                        = 0x371 // 881
	SYS_OPENLOG                         = 0x372 // 882
	SYS_SETLOGMASK                      = 0x373 // 883
	SYS_SYSLOG                          = 0x374 // 884
	SYS_PTSNAME                         = 0x375 // 885
	SYS_SETREUID                        = 0x376 // 886
	SYS_SETREGID                        = 0x377 // 887
	SYS_REALPATH                        = 0x378 // 888
	SYS___SIGNGAM                       = 0x379 // 889
	SYS_GRANTPT                         = 0x37A // 890
	SYS_UNLOCKPT                        = 0x37B // 891
	SYS_TCGETSID                        = 0x37C // 892
	SYS___TCGETCP                       = 0x37D // 893
	SYS___TCSETCP                       = 0x37E // 894
	SYS___TCSETTABLES                   = 0x37F // 895
	SYS_POLL                            = 0x380 // 896
	SYS_REXEC                           = 0x381 // 897
	SYS___ISASCII2                      = 0x382 // 898
	SYS___TOASCII2                      = 0x383 // 899
	SYS_CHPRIORITY                      = 0x384 // 900
	SYS_PTHREAD_ATTR_SETSYNCTYPE_NP     = 0x385 // 901
	SYS_PTHREAD_ATTR_GETSYNCTYPE_NP     = 0x386 // 902
	SYS_PTHREAD_SET_LIMIT_NP            = 0x387 // 903
	SYS___STNETENT                      = 0x388 // 904
	SYS___STPROTOENT                    = 0x389 // 905
	SYS___STSERVENT                     = 0x38A // 906
	SYS___STHOSTENT                     = 0x38B // 907
	SYS_NLIST                           = 0x38C // 908
	SYS___IPDBCS                        = 0x38D // 909
	SYS___IPDSPX                        = 0x38E // 910
	SYS___IPMSGC                        = 0x38F // 911
	SYS___SELECT1                       = 0x390 // 912
	SYS_PTHREAD_SECURITY_NP             = 0x391 // 913
	SYS___CHECK_RESOURCE_AUTH_NP        = 0x392 // 914
	SYS___CONVERT_ID_NP                 = 0x393 // 915
	SYS___OPENVMREL                     = 0x394 // 916
	SYS_WMEMCHR                         = 0x395 // 917
	SYS_WMEMCMP                         = 0x396 // 918
	SYS_WMEMCPY                         = 0x397 // 919
	SYS_WMEMMOVE                        = 0x398 // 920
	SYS_WMEMSET                         = 0x399 // 921
	SYS___FPUTWC                        = 0x400 // 1024
	SYS___PUTWC                         = 0x401 // 1025
	SYS___PWCHAR                        = 0x402 // 1026
	SYS___WCSFTM                        = 0x403 // 1027
	SYS___WCSTOK                        = 0x404 // 1028
	SYS___WCWDTH                        = 0x405 // 1029
	SYS_T_ACCEPT                        = 0x409 // 1033
	SYS_T_ALLOC                         = 0x40A // 1034
	SYS_T_BIND                          = 0x40B // 1035
	SYS_T_CLOSE                         = 0x40C // 1036
	SYS_T_CONNECT                       = 0x40D // 1037
	SYS_T_ERROR                         = 0x40E // 1038
	SYS_T_FREE                          = 0x40F // 1039
	SYS_T_GETINFO                       = 0x410 // 1040
	SYS_T_GETPROTADDR                   = 0x411 // 1041
	SYS_T_GETSTATE                      = 0x412 // 1042
	SYS_T_LISTEN                        = 0x413 // 1043
	SYS_T_LOOK                          = 0x414 // 1044
	SYS_T_OPEN                          = 0x415 // 1045
	SYS_T_OPTMGMT                       = 0x416 // 1046
	SYS_T_RCV                           = 0x417 // 1047
	SYS_T_RCVCONNECT                    = 0x418 // 1048
	SYS_T_RCVDIS                        = 0x419 // 1049
	SYS_T_RCVREL                        = 0x41A // 1050
	SYS_T_RCVUDATA                      = 0x41B // 1051
	SYS_T_RCVUDERR                      = 0x41C // 1052
	SYS_T_SND                           = 0x41D // 1053
	SYS_T_SNDDIS                        = 0x41E // 1054
	SYS_T_SNDREL                        = 0x41F // 1055
	SYS_T_SNDUDATA                      = 0x420 // 1056
	SYS_T_STRERROR                      = 0x421 // 1057
	SYS_T_SYNC                          = 0x422 // 1058
	SYS_T_UNBIND                        = 0x423 // 1059
	SYS___T_ERRNO                       = 0x424 // 1060
	SYS___RECVMSG2                      = 0x425 // 1061
	SYS___SENDMSG2                      = 0x426 // 1062
	SYS_FATTACH                         = 0x427 // 1063
	SYS_FDETACH                         = 0x428 // 1064
	SYS_GETMSG                          = 0x429 // 1065
	SYS_GETPMSG                         = 0x42A // 1066
	SYS_ISASTREAM                       = 0x42B // 1067
	SYS_PUTMSG                          = 0x42C // 1068
	SYS_PUTPMSG                         = 0x42D // 1069
	SYS___ISPOSIXON                     = 0x42E // 1070
	SYS___OPENMVSREL                    = 0x42F // 1071
	SYS_GETCONTEXT                      = 0x430 // 1072
	SYS_SETCONTEXT                      = 0x431 // 1073
	SYS_MAKECONTEXT                     = 0x432 // 1074
	SYS_SWAPCONTEXT                     = 0x433 // 1075
	SYS_PTHREAD_GETSPECIFIC_D8_NP       = 0x434 // 1076
	SYS_GETCLIENTID                     = 0x470 // 1136
	SYS___GETCLIENTID                   = 0x471 // 1137
	SYS_GETSTABLESIZE                   = 0x472 // 1138
	SYS_GETIBMOPT                       = 0x473 // 1139
	SYS_GETIBMSOCKOPT                   = 0x474 // 1140
	SYS_GIVESOCKET                      = 0x475 // 1141
	SYS_IBMSFLUSH                       = 0x476 // 1142
	SYS_MAXDESC                         = 0x477 // 1143
	SYS_SETIBMOPT                       = 0x478 // 1144
	SYS_SETIBMSOCKOPT                   = 0x479 // 1145
	SYS_SOCK_DEBUG                      = 0x47A // 1146
	SYS_SOCK_DO_TESTSTOR                = 0x47D // 1149
	SYS_TAKESOCKET                      = 0x47E // 1150
	SYS___SERVER_INIT                   = 0x47F // 1151
	SYS___SERVER_PWU                    = 0x480 // 1152
	SYS_PTHREAD_TAG_NP                  = 0x481 // 1153
	SYS___CONSOLE                       = 0x482 // 1154
	SYS___WSINIT                        = 0x483 // 1155
	SYS___IPTCPN                        = 0x489 // 1161
	SYS___SMF_RECORD                    = 0x48A // 1162
	SYS___IPHOST                        = 0x48B // 1163
	SYS___IPNODE                        = 0x48C // 1164
	SYS___SERVER_CLASSIFY_CREATE        = 0x48D // 1165
	SYS___SERVER_CLASSIFY_DESTROY       = 0x48E // 1166
	SYS___SERVER_CLASSIFY_RESET         = 0x48F // 1167
	SYS___SERVER_CLASSIFY               = 0x490 // 1168
	SYS___HEAPRPT                       = 0x496 // 1174
	SYS___FNWSA                         = 0x49B // 1179
	SYS___SPAWN2                        = 0x49D // 1181
	SYS___SPAWNP2                       = 0x49E // 1182
	SYS___GDRR                          = 0x4A1 // 1185
	SYS___HRRNO                         = 0x4A2 // 1186
	SYS___OPRG                          = 0x4A3 // 1187
	SYS___OPRR                          = 0x4A4 // 1188
	SYS___OPND                          = 0x4A5 // 1189
	SYS___OPPT                          = 0x4A6 // 1190
	SYS___SIGGM                         = 0x4A7 // 1191
	SYS___DGHT                          = 0x4A8 // 1192
	SYS___TZNE                          = 0x4A9 // 1193
	SYS___TZZN                          = 0x4AA // 1194
	SYS___TRRNO                         = 0x4AF // 1199
	SYS___ENVN                          = 0x4B0 // 1200
	SYS___MLOCKALL                      = 0x4B1 // 1201
	SYS_CREATEWO                        = 0x4B2 // 1202
	SYS_CREATEWORKUNIT                  = 0x4B2 // 1202
	SYS_CONTINUE                        = 0x4B3 // 1203
	SYS_CONTINUEWORKUNIT                = 0x4B3 // 1203
	SYS_CONNECTW                        = 0x4B4 // 1204
	SYS_CONNECTWORKMGR                  = 0x4B4 // 1204
	SYS_CONNECTS                        = 0x4B5 // 1205
	SYS_CONNECTSERVER                   = 0x4B5 // 1205
	SYS_DISCONNE                        = 0x4B6 // 1206
	SYS_DISCONNECTSERVER                = 0x4B6 // 1206
	SYS_JOINWORK                        = 0x4B7 // 1207
	SYS_JOINWORKUNIT                    = 0x4B7 // 1207
	SYS_LEAVEWOR                        = 0x4B8 // 1208
	SYS_LEAVEWORKUNIT                   = 0x4B8 // 1208
	SYS_DELETEWO                        = 0x4B9 // 1209
	SYS_DELETEWORKUNIT                  = 0x4B9 // 1209
	SYS_QUERYMET                        = 0x4BA // 1210
	SYS_QUERYMETRICS                    = 0x4BA // 1210
	SYS_QUERYSCH                        = 0x4BB // 1211
	SYS_QUERYSCHENV                     = 0x4BB // 1211
	SYS_CHECKSCH                        = 0x4BC // 1212
	SYS_CHECKSCHENV                     = 0x4BC // 1212
	SYS___PID_AFFINITY                  = 0x4BD // 1213
	SYS___ASINH_B                       = 0x4BE // 1214
	SYS___ATAN_B                        = 0x4BF // 1215
	SYS___CBRT_B                        = 0x4C0 // 1216
	SYS___CEIL_B                        = 0x4C1 // 1217
	SYS_COPYSIGN                        = 0x4C2 // 1218
	SYS___COS_B                         = 0x4C3 // 1219
	SYS___ERF_B                         = 0x4C4 // 1220
	SYS___ERFC_B                        = 0x4C5 // 1221
	SYS___EXPM1_B                       = 0x4C6 // 1222
	SYS___FABS_B                        = 0x4C7 // 1223
	SYS_FINITE                          = 0x4C8 // 1224
	SYS___FLOOR_B                       = 0x4C9 // 1225
	SYS___FREXP_B                       = 0x4CA // 1226
	SYS___ILOGB_B                       = 0x4CB // 1227
	SYS___ISNAN_B                       = 0x4CC // 1228
	SYS___LDEXP_B                       = 0x4CD // 1229
	SYS___LOG1P_B                       = 0x4CE // 1230
	SYS___LOGB_B                        = 0x4CF // 1231
	SYS_MATHERR                         = 0x4D0 // 1232
	SYS___MODF_B                        = 0x4D1 // 1233
	SYS___NEXTAFTER_B                   = 0x4D2 // 1234
	SYS___RINT_B                        = 0x4D3 // 1235
	SYS_SCALBN                          = 0x4D4 // 1236
	SYS_SIGNIFIC                        = 0x4D5 // 1237
	SYS_SIGNIFICAND                     = 0x4D5 // 1237
	SYS___SIN_B                         = 0x4D6 // 1238
	SYS___TAN_B                         = 0x4D7 // 1239
	SYS___TANH_B                        = 0x4D8 // 1240
	SYS___ACOS_B                        = 0x4D9 // 1241
	SYS___ACOSH_B                       = 0x4DA // 1242
	SYS___ASIN_B                        = 0x4DB // 1243
	SYS___ATAN2_B                       = 0x4DC // 1244
	SYS___ATANH_B                       = 0x4DD // 1245
	SYS___COSH_B                        = 0x4DE // 1246
	SYS___EXP_B                         = 0x4DF // 1247
	SYS___FMOD_B                        = 0x4E0 // 1248
	SYS___GAMMA_B                       = 0x4E1 // 1249
	SYS_GAMMA_R                         = 0x4E2 // 1250
	SYS___HYPOT_B                       = 0x4E3 // 1251
	SYS___J0_B                          = 0x4E4 // 1252
	SYS___Y0_B                          = 0x4E5 // 1253
	SYS___J1_B                          = 0x4E6 // 1254
	SYS___Y1_B                          = 0x4E7 // 1255
	SYS___JN_B                          = 0x4E8 // 1256
	SYS___YN_B                          = 0x4E9 // 1257
	SYS___LGAMMA_B                      = 0x4EA // 1258
	SYS_LGAMMA_R                        = 0x4EB // 1259
	SYS___LOG_B                         = 0x4EC // 1260
	SYS___LOG10_B                       = 0x4ED // 1261
	SYS___POW_B                         = 0x4EE // 1262
	SYS___REMAINDER_B                   = 0x4EF // 1263
	SYS___SCALB_B                       = 0x4F0 // 1264
	SYS___SINH_B                        = 0x4F1 // 1265
	SYS___SQRT_B                        = 0x4F2 // 1266
	SYS___OPENDIR2                      = 0x4F3 // 1267
	SYS___READDIR2                      = 0x4F4 // 1268
	SYS___LOGIN                         = 0x4F5 // 1269
	SYS___OPEN_STAT                     = 0x4F6 // 1270
	SYS_ACCEPT_AND_RECV                 = 0x4F7 // 1271
	SYS___FP_SETMODE                    = 0x4F8 // 1272
	SYS___SIGACTIONSET                  = 0x4FB // 1275
	SYS___UCREATE                       = 0x4FC // 1276
	SYS___UMALLOC                       = 0x4FD // 1277
	SYS___UFREE                         = 0x4FE // 1278
	SYS___UHEAPREPORT                   = 0x4FF // 1279
	SYS___ISBFP                         = 0x500 // 1280
	SYS___FP_CAST                       = 0x501 // 1281
	SYS___CERTIFICATE                   = 0x502 // 1282
	SYS_SEND_FILE                       = 0x503 // 1283
	SYS_AIO_CANCEL                      = 0x504 // 1284
	SYS_AIO_ERROR                       = 0x505 // 1285
	SYS_AIO_READ                        = 0x506 // 1286
	SYS_AIO_RETURN                      = 0x507 // 1287
	SYS_AIO_SUSPEND                     = 0x508 // 1288
	SYS_AIO_WRITE                       = 0x509 // 1289
	SYS_PTHREAD_MUTEXATTR_GETPSHARED    = 0x50A // 1290
	SYS_PTHREAD_MUTEXATTR_SETPSHARED    = 0x50B // 1291
	SYS_PTHREAD_RWLOCK_DESTROY          = 0x50C // 1292
	SYS_PTHREAD_RWLOCK_INIT             = 0x50D // 1293
	SYS_PTHREAD_RWLOCK_RDLOCK           = 0x50E // 1294
	SYS_PTHREAD_RWLOCK_TRYRDLOCK        = 0x50F // 1295
	SYS_PTHREAD_RWLOCK_TRYWRLOCK        = 0x510 // 1296
	SYS_PTHREAD_RWLOCK_UNLOCK           = 0x511 // 1297
	SYS_PTHREAD_RWLOCK_WRLOCK           = 0x512 // 1298
	SYS_PTHREAD_RWLOCKATTR_GETPSHARED   = 0x513 // 1299
	SYS_PTHREAD_RWLOCKATTR_SETPSHARED   = 0x514 // 1300
	SYS_PTHREAD_RWLOCKATTR_INIT         = 0x515 // 1301
	SYS_PTHREAD_RWLOCKATTR_DESTROY      = 0x516 // 1302
	SYS___CTTBL                         = 0x517 // 1303
	SYS_PTHREAD_MUTEXATTR_SETTYPE       = 0x518 // 1304
	SYS_PTHREAD_MUTEXATTR_GETTYPE       = 0x519 // 1305
	SYS___FP_CLR_FLAG                   = 0x51A // 1306
	SYS___FP_READ_FLAG                  = 0x51B // 1307
	SYS___FP_RAISE_XCP                  = 0x51C // 1308
	SYS___FP_CLASS                      = 0x51D // 1309
	SYS___FP_FINITE                     = 0x51E // 1310
	SYS___FP_ISNAN                      = 0x51F // 1311
	SYS___FP_UNORDERED                  = 0x520 // 1312
	SYS___FP_READ_RND                   = 0x521 // 1313
	SYS___FP_READ_RND_B                 = 0x522 // 1314
	SYS___FP_SWAP_RND                   = 0x523 // 1315
	SYS___FP_SWAP_RND_B                 = 0x524 // 1316
	SYS___FP_LEVEL                      = 0x525 // 1317
	SYS___FP_BTOH                       = 0x526 // 1318
	SYS___FP_HTOB                       = 0x527 // 1319
	SYS___FPC_RD                        = 0x528 // 1320
	SYS___FPC_WR                        = 0x529 // 1321
	SYS___FPC_RW                        = 0x52A // 1322
	SYS___FPC_SM                        = 0x52B // 1323
	SYS___FPC_RS                        = 0x52C // 1324
	SYS_SIGTIMEDWAIT                    = 0x52D // 1325
	SYS_SIGWAITINFO                     = 0x52E // 1326
	SYS___CHKBFP                        = 0x52F // 1327
	SYS___W_PIOCTL                      = 0x59E // 1438
	SYS___OSENV                         = 0x59F // 1439
	SYS_EXPORTWO                        = 0x5A1 // 1441
	SYS_EXPORTWORKUNIT                  = 0x5A1 // 1441
	SYS_UNDOEXPO                        = 0x5A2 // 1442
	SYS_UNDOEXPORTWORKUNIT              = 0x5A2 // 1442
	SYS_IMPORTWO                        = 0x5A3 // 1443
	SYS_IMPORTWORKUNIT                  = 0x5A3 // 1443
	SYS_UNDOIMPO                        = 0x5A4 // 1444
	SYS_UNDOIMPORTWORKUNIT              = 0x5A4 // 1444
	SYS_EXTRACTW                        = 0x5A5 // 1445
	SYS_EXTRACTWORKUNIT                 = 0x5A5 // 1445
	SYS___CPL                           = 0x5A6 // 1446
	SYS___MAP_INIT                      = 0x5A7 // 1447
	SYS___MAP_SERVICE                   = 0x5A8 // 1448
	SYS_SIGQUEUE                        = 0x5A9 // 1449
	SYS___MOUNT                         = 0x5AA // 1450
	SYS___GETUSERID                     = 0x5AB // 1451
	SYS___IPDOMAINNAME                  = 0x5AC // 1452
	SYS_QUERYENC                        = 0x5AD // 1453
	SYS_QUERYWORKUNITCLASSIFICATION     = 0x5AD // 1453
	SYS_CONNECTE                        = 0x5AE // 1454
	SYS_CONNECTEXPORTIMPORT             = 0x5AE // 1454
	SYS___FP_SWAPMODE                   = 0x5AF // 1455
	SYS_STRTOLL                         = 0x5B0 // 1456
	SYS_STRTOULL                        = 0x5B1 // 1457
	SYS___DSA_PREV                      = 0x5B2 // 1458
	SYS___EP_FIND                       = 0x5B3 // 1459
	SYS___SERVER_THREADS_QUERY          = 0x5B4 // 1460
	SYS___MSGRCV_TIMED                  = 0x5B7 // 1463
	SYS___SEMOP_TIMED                   = 0x5B8 // 1464
	SYS___GET_CPUID                     = 0x5B9 // 1465
	SYS___GET_SYSTEM_SETTINGS           = 0x5BA // 1466
	SYS_FTELLO                          = 0x5C8 // 1480
	SYS_FSEEKO                          = 0x5C9 // 1481
	SYS_LLDIV                           = 0x5CB // 1483
	SYS_WCSTOLL                         = 0x5CC // 1484
	SYS_WCSTOULL                        = 0x5CD // 1485
	SYS_LLABS                           = 0x5CE // 1486
	SYS___CONSOLE2                      = 0x5D2 // 1490
	SYS_INET_NTOP                       = 0x5D3 // 1491
	SYS_INET_PTON                       = 0x5D4 // 1492
	SYS___RES                           = 0x5D6 // 1494
	SYS_RES_MKQUERY                     = 0x5D7 // 1495
	SYS_RES_INIT                        = 0x5D8 // 1496
	SYS_RES_QUERY                       = 0x5D9 // 1497
	SYS_RES_SEARCH                      = 0x5DA // 1498
	SYS_RES_SEND                        = 0x5DB // 1499
	SYS_RES_QUERYDOMAIN                 = 0x5DC // 1500
	SYS_DN_EXPAND                       = 0x5DD // 1501
	SYS_DN_SKIPNAME                     = 0x5DE // 1502
	SYS_DN_COMP                         = 0x5DF // 1503
	SYS_ASCTIME_R                       = 0x5E0 // 1504
	SYS_CTIME_R                         = 0x5E1 // 1505
	SYS_GMTIME_R                        = 0x5E2 // 1506
	SYS_LOCALTIME_R                     = 0x5E3 // 1507
	SYS_RAND_R                          = 0x5E4 // 1508
	SYS_STRTOK_R                        = 0x5E5 // 1509
	SYS_READDIR_R                       = 0x5E6 // 1510
	SYS_GETGRGID_R                      = 0x5E7 // 1511
	SYS_GETGRNAM_R                      = 0x5E8 // 1512
	SYS_GETLOGIN_R                      = 0x5E9 // 1513
	SYS_GETPWNAM_R                      = 0x5EA // 1514
	SYS_GETPWUID_R                      = 0x5EB // 1515
	SYS_TTYNAME_R                       = 0x5EC // 1516
	SYS_PTHREAD_ATFORK                  = 0x5ED // 1517
	SYS_PTHREAD_ATTR_GETGUARDSIZE       = 0x5EE // 1518
	SYS_PTHREAD_ATTR_GETSTACKADDR       = 0x5EF // 1519
	SYS_PTHREAD_ATTR_SETGUARDSIZE       = 0x5F0 // 1520
	SYS_PTHREAD_ATTR_SETSTACKADDR       = 0x5F1 // 1521
	SYS_PTHREAD_CONDATTR_GETPSHARED     = 0x5F2 // 1522
	SYS_PTHREAD_CONDATTR_SETPSHARED     = 0x5F3 // 1523
	SYS_PTHREAD_GETCONCURRENCY          = 0x5F4 // 1524
	SYS_PTHREAD_KEY_DELETE              = 0x5F5 // 1525
	SYS_PTHREAD_SETCONCURRENCY          = 0x5F6 // 1526
	SYS_PTHREAD_SIGMASK                 = 0x5F7 // 1527
	SYS___DISCARDDATA                   = 0x5F8 // 1528
	SYS_PTHREAD_ATTR_GETSCHEDPARAM      = 0x5F9 // 1529
	SYS_PTHREAD_ATTR_SETSCHEDPARAM      = 0x5FA // 1530
	SYS_PTHREAD_ATTR_GETDETACHSTATE_U98 = 0x5FB // 1531
	SYS_PTHREAD_ATTR_SETDETACHSTATE_U98 = 0x5FC // 1532
	SYS_PTHREAD_DETACH_U98              = 0x5FD // 1533
	SYS_PTHREAD_GETSPECIFIC_U98         = 0x5FE // 1534
	SYS_PTHREAD_SETCANCELSTATE          = 0x5FF // 1535
	SYS_PTHREAD_SETCANCELTYPE           = 0x600 // 1536
	SYS_PTHREAD_TESTCANCEL              = 0x601 // 1537
	SYS___ATANF_B                       = 0x602 // 1538
	SYS___ATANL_B                       = 0x603 // 1539
	SYS___CEILF_B                       = 0x604 // 1540
	SYS___CEILL_B                       = 0x605 // 1541
	SYS___COSF_B                        = 0x606 // 1542
	SYS___COSL_B                        = 0x607 // 1543
	SYS___FABSF_B                       = 0x608 // 1544
	SYS___FABSL_B                       = 0x609 // 1545
	SYS___FLOORF_B                      = 0x60A // 1546
	SYS___FLOORL_B                      = 0x60B // 1547
	SYS___FREXPF_B                      = 0x60C // 1548
	SYS___FREXPL_B                      = 0x60D // 1549
	SYS___LDEXPF_B                      = 0x60E // 1550
	SYS___LDEXPL_B                      = 0x60F // 1551
	SYS___SINF_B                        = 0x610 // 1552
	SYS___SINL_B                        = 0x611 // 1553
	SYS___TANF_B                        = 0x612 // 1554
	SYS___TANL_B                        = 0x613 // 1555
	SYS___TANHF_B                       = 0x614 // 1556
	SYS___TANHL_B                       = 0x615 // 1557
	SYS___ACOSF_B                       = 0x616 // 1558
	SYS___ACOSL_B                       = 0x617 // 1559
	SYS___ASINF_B                       = 0x618 // 1560
	SYS___ASINL_B                       = 0x619 // 1561
	SYS___ATAN2F_B                      = 0x61A // 1562
	SYS___ATAN2L_B                      = 0x61B // 1563
	SYS___COSHF_B                       = 0x61C // 1564
	SYS___COSHL_B                       = 0x61D // 1565
	SYS___EXPF_B                        = 0x61E // 1566
	SYS___EXPL_B                        = 0x61F // 1567
	SYS___LOGF_B                        = 0x620 // 1568
	SYS___LOGL_B                        = 0x621 // 1569
	SYS___LOG10F_B                      = 0x622 // 1570
	SYS___LOG10L_B                      = 0x623 // 1571
	SYS___POWF_B                        = 0x624 // 1572
	SYS___POWL_B                        = 0x625 // 1573
	SYS___SINHF_B                       = 0x626 // 1574
	SYS___SINHL_B                       = 0x627 // 1575
	SYS___SQRTF_B                       = 0x628 // 1576
	SYS___SQRTL_B                       = 0x629 // 1577
	SYS___ABSF_B                        = 0x62A // 1578
	SYS___ABS_B                         = 0x62B // 1579
	SYS___ABSL_B                        = 0x62C // 1580
	SYS___FMODF_B                       = 0x62D // 1581
	SYS___FMODL_B                       = 0x62E // 1582
	SYS___MODFF_B                       = 0x62F // 1583
	SYS___MODFL_B                       = 0x630 // 1584
	SYS_ABSF                            = 0x631 // 1585
	SYS_ABSL                            = 0x632 // 1586
	SYS_ACOSF                           = 0x633 // 1587
	SYS_ACOSL                           = 0x634 // 1588
	SYS_ASINF                           = 0x635 // 1589
	SYS_ASINL                           = 0x636 // 1590
	SYS_ATAN2F                          = 0x637 // 1591
	SYS_ATAN2L                          = 0x638 // 1592
	SYS_ATANF                           = 0x639 // 1593
	SYS_ATANL                           = 0x63A // 1594
	SYS_CEILF                           = 0x63B // 1595
	SYS_CEILL                           = 0x63C // 1596
	SYS_COSF                            = 0x63D // 1597
	SYS_COSL                            = 0x63E // 1598
	SYS_COSHF                           = 0x63F // 1599
	SYS_COSHL                           = 0x640 // 1600
	SYS_EXPF                            = 0x641 // 1601
	SYS_EXPL                            = 0x642 // 1602
	SYS_TANHF                           = 0x643 // 1603
	SYS_TANHL                           = 0x644 // 1604
	SYS_LOG10F                          = 0x645 // 1605
	SYS_LOG10L                          = 0x646 // 1606
	SYS_LOGF                            = 0x647 // 1607
	SYS_LOGL                            = 0x648 // 1608
	SYS_POWF                            = 0x649 // 1609
	SYS_POWL                            = 0x64A // 1610
	SYS_SINF                            = 0x64B // 1611
	SYS_SINL                            = 0x64C // 1612
	SYS_SQRTF                           = 0x64D // 1613
	SYS_SQRTL                           = 0x64E // 1614
	SYS_SINHF                           = 0x64F // 1615
	SYS_SINHL                           = 0x650 // 1616
	SYS_TANF                            = 0x651 // 1617
	SYS_TANL                            = 0x652 // 1618
	SYS_FABSF                           = 0x653 // 1619
	SYS_FABSL                           = 0x654 // 1620
	SYS_FLOORF                          = 0x655 // 1621
	SYS_FLOORL                          = 0x656 // 1622
	SYS_FMODF                           = 0x657 // 1623
	SYS_FMODL                           = 0x658 // 1624
	SYS_FREXPF                          = 0x659 // 1625
	SYS_FREXPL                          = 0x65A // 1626
	SYS_LDEXPF                          = 0x65B // 1627
	SYS_LDEXPL                          = 0x65C // 1628
	SYS_MODFF                           = 0x65D // 1629
	SYS_MODFL                           = 0x65E // 1630
	SYS_BTOWC                           = 0x65F // 1631
	SYS___CHATTR                        = 0x660 // 1632
	SYS___FCHATTR                       = 0x661 // 1633
	SYS___TOCCSID                       = 0x662 // 1634
	SYS___CSNAMETYPE                    = 0x663 // 1635
	SYS___TOCSNAME                      = 0x664 // 1636
	SYS___CCSIDTYPE                     = 0x665 // 1637
	SYS___AE_CORRESTBL_QUERY            = 0x666 // 1638
	SYS___AE_AUTOCONVERT_STATE          = 0x667 // 1639
	SYS_DN_FIND                         = 0x668 // 1640
	SYS___GETHOSTBYADDR_A               = 0x669 // 1641
	SYS___GETHOSTBYNAME_A               = 0x66A // 1642
	SYS___RES_INIT_A                    = 0x66B // 1643
	SYS___GETHOSTBYADDR_R_A             = 0x66C // 1644
	SYS___GETHOSTBYNAME_R_A             = 0x66D // 1645
	SYS___CHARMAP_INIT_A                = 0x66E // 1646
	SYS___MBLEN_A                       = 0x66F // 1647
	SYS___MBLEN_SB_A                    = 0x670 // 1648
	SYS___MBLEN_STD_A                   = 0x671 // 1649
	SYS___MBLEN_UTF                     = 0x672 // 1650
	SYS___MBSTOWCS_A                    = 0x673 // 1651
	SYS___MBSTOWCS_STD_A                = 0x674 // 1652
	SYS___MBTOWC_A                      = 0x675 // 1653
	SYS___MBTOWC_ISO1                   = 0x676 // 1654
	SYS___MBTOWC_SBCS                   = 0x677 // 1655
	SYS___MBTOWC_MBCS                   = 0x678 // 1656
	SYS___MBTOWC_UTF                    = 0x679 // 1657
	SYS___WCSTOMBS_A                    = 0x67A // 1658
	SYS___WCSTOMBS_STD_A                = 0x67B // 1659
	SYS___WCSWIDTH_A                    = 0x67C // 1660
	SYS___GETGRGID_R_A                  = 0x67D // 1661
	SYS___WCSWIDTH_STD_A                = 0x67E // 1662
	SYS___WCSWIDTH_ASIA                 = 0x67F // 1663
	SYS___CSID_A                        = 0x680 // 1664
	SYS___CSID_STD_A                    = 0x681 // 1665
	SYS___WCSID_A                       = 0x682 // 1666
	SYS___WCSID_STD_A                   = 0x683 // 1667
	SYS___WCTOMB_A                      = 0x684 // 1668
	SYS___WCTOMB_ISO1                   = 0x685 // 1669
	SYS___WCTOMB_STD_A                  = 0x686 // 1670
	SYS___WCTOMB_UTF                    = 0x687 // 1671
	SYS___WCWIDTH_A                     = 0x688 // 1672
	SYS___GETGRNAM_R_A                  = 0x689 // 1673
	SYS___WCWIDTH_STD_A                 = 0x68A // 1674
	SYS___WCWIDTH_ASIA                  = 0x68B // 1675
	SYS___GETPWNAM_R_A                  = 0x68C // 1676
	SYS___GETPWUID_R_A                  = 0x68D // 1677
	SYS___GETLOGIN_R_A                  = 0x68E // 1678
	SYS___TTYNAME_R_A                   = 0x68F // 1679
	SYS___READDIR_R_A                   = 0x690 // 1680
	SYS___E2A_S                         = 0x691 // 1681
	SYS___FNMATCH_A                     = 0x692 // 1682
	SYS___FNMATCH_C_A                   = 0x693 // 1683
	SYS___EXECL_A                       = 0x694 // 1684
	SYS___FNMATCH_STD_A                 = 0x695 // 1685
	SYS___REGCOMP_A                     = 0x696 // 1686
	SYS___REGCOMP_STD_A                 = 0x697 // 1687
	SYS___REGERROR_A                    = 0x698 // 1688
	SYS___REGERROR_STD_A                = 0x699 // 1689
	SYS___REGEXEC_A                     = 0x69A // 1690
	SYS___REGEXEC_STD_A                 = 0x69B // 1691
	SYS___REGFREE_A                     = 0x69C // 1692
	SYS___REGFREE_STD_A                 = 0x69D // 1693
	SYS___STRCOLL_A                     = 0x69E // 1694
	SYS___STRCOLL_C_A                   = 0x69F // 1695
	SYS___EXECLE_A                      = 0x6A0 // 1696
	SYS___STRCOLL_STD_A                 = 0x6A1 // 1697
	SYS___STRXFRM_A                     = 0x6A2 // 1698
	SYS___STRXFRM_C_A                   = 0x6A3 // 1699
	SYS___EXECLP_A                      = 0x6A4 // 1700
	SYS___STRXFRM_STD_A                 = 0x6A5 // 1701
	SYS___WCSCOLL_A                     = 0x6A6 // 1702
	SYS___WCSCOLL_C_A                   = 0x6A7 // 1703
	SYS___WCSCOLL_STD_A                 = 0x6A8 // 1704
	SYS___WCSXFRM_A                     = 0x6A9 // 1705
	SYS___WCSXFRM_C_A                   = 0x6AA // 1706
	SYS___WCSXFRM_STD_A                 = 0x6AB // 1707
	SYS___COLLATE_INIT_A                = 0x6AC // 1708
	SYS___WCTYPE_A                      = 0x6AD // 1709
	SYS___GET_WCTYPE_STD_A              = 0x6AE // 1710
	SYS___CTYPE_INIT_A                  = 0x6AF // 1711
	SYS___ISWCTYPE_A                    = 0x6B0 // 1712
	SYS___EXECV_A                       = 0x6B1 // 1713
	SYS___IS_WCTYPE_STD_A               = 0x6B2 // 1714
	SYS___TOWLOWER_A                    = 0x6B3 // 1715
	SYS___TOWLOWER_STD_A                = 0x6B4 // 1716
	SYS___TOWUPPER_A                    = 0x6B5 // 1717
	SYS___TOWUPPER_STD_A                = 0x6B6 // 1718
	SYS___LOCALE_INIT_A                 = 0x6B7 // 1719
	SYS___LOCALECONV_A                  = 0x6B8 // 1720
	SYS___LOCALECONV_STD_A              = 0x6B9 // 1721
	SYS___NL_LANGINFO_A                 = 0x6BA // 1722
	SYS___NL_LNAGINFO_STD_A             = 0x6BB // 1723
	SYS___MONETARY_INIT_A               = 0x6BC // 1724
	SYS___STRFMON_A                     = 0x6BD // 1725
	SYS___STRFMON_STD_A                 = 0x6BE // 1726
	SYS___GETADDRINFO_A                 = 0x6BF // 1727
	SYS___CATGETS_A                     = 0x6C0 // 1728
	SYS___EXECVE_A                      = 0x6C1 // 1729
	SYS___EXECVP_A                      = 0x6C2 // 1730
	SYS___SPAWN_A                       = 0x6C3 // 1731
	SYS___GETNAMEINFO_A                 = 0x6C4 // 1732
	SYS___SPAWNP_A                      = 0x6C5 // 1733
	SYS___NUMERIC_INIT_A                = 0x6C6 // 1734
	SYS___RESP_INIT_A                   = 0x6C7 // 1735
	SYS___RPMATCH_A                     = 0x6C8 // 1736
	SYS___RPMATCH_C_A                   = 0x6C9 // 1737
	SYS___RPMATCH_STD_A                 = 0x6CA // 1738
	SYS___TIME_INIT_A                   = 0x6CB // 1739
	SYS___STRFTIME_A                    = 0x6CC // 1740
	SYS___STRFTIME_STD_A                = 0x6CD // 1741
	SYS___STRPTIME_A                    = 0x6CE // 1742
	SYS___STRPTIME_STD_A                = 0x6CF // 1743
	SYS___WCSFTIME_A                    = 0x6D0 // 1744
	SYS___WCSFTIME_STD_A                = 0x6D1 // 1745
	SYS_____SPAWN2_A                    = 0x6D2 // 1746
	SYS_____SPAWNP2_A                   = 0x6D3 // 1747
	SYS___SYNTAX_INIT_A                 = 0x6D4 // 1748
	SYS___TOD_INIT_A                    = 0x6D5 // 1749
	SYS___NL_CSINFO_A                   = 0x6D6 // 1750
	SYS___NL_MONINFO_A                  = 0x6D7 // 1751
	SYS___NL_NUMINFO_A                  = 0x6D8 // 1752
	SYS___NL_RESPINFO_A                 = 0x6D9 // 1753
	SYS___NL_TIMINFO_A                  = 0x6DA // 1754
	SYS___IF_NAMETOINDEX_A              = 0x6DB // 1755
	SYS___IF_INDEXTONAME_A              = 0x6DC // 1756
	SYS___PRINTF_A                      = 0x6DD // 1757
	SYS___ICONV_OPEN_A                  = 0x6DE // 1758
	SYS___DLLLOAD_A                     = 0x6DF // 1759
	SYS___DLLQUERYFN_A                  = 0x6E0 // 1760
	SYS___DLLQUERYVAR_A                 = 0x6E1 // 1761
	SYS_____CHATTR_A                    = 0x6E2 // 1762
	SYS___E2A_L                         = 0x6E3 // 1763
	SYS_____TOCCSID_A                   = 0x6E4 // 1764
	SYS_____TOCSNAME_A                  = 0x6E5 // 1765
	SYS_____CCSIDTYPE_A                 = 0x6E6 // 1766
	SYS_____CSNAMETYPE_A                = 0x6E7 // 1767
	SYS___CHMOD_A                       = 0x6E8 // 1768
	SYS___MKDIR_A                       = 0x6E9 // 1769
	SYS___STAT_A                        = 0x6EA // 1770
	SYS___STAT_O_A                      = 0x6EB // 1771
	SYS___MKFIFO_A                      = 0x6EC // 1772
	SYS_____OPEN_STAT_A                 = 0x6ED // 1773
	SYS___LSTAT_A                       = 0x6EE // 1774
	SYS___LSTAT_O_A                     = 0x6EF // 1775
	SYS___MKNOD_A                       = 0x6F0 // 1776
	SYS___MOUNT_A                       = 0x6F1 // 1777
	SYS___UMOUNT_A                      = 0x6F2 // 1778
	SYS___CHAUDIT_A                     = 0x6F4 // 1780
	SYS___W_GETMNTENT_A                 = 0x6F5 // 1781
	SYS___CREAT_A                       = 0x6F6 // 1782
	SYS___OPEN_A                        = 0x6F7 // 1783
	SYS___SETLOCALE_A                   = 0x6F9 // 1785
	SYS___FPRINTF_A                     = 0x6FA // 1786
	SYS___SPRINTF_A                     = 0x6FB // 1787
	SYS___VFPRINTF_A                    = 0x6FC // 1788
	SYS___VPRINTF_A                     = 0x6FD // 1789
	SYS___VSPRINTF_A                    = 0x6FE // 1790
	SYS___VSWPRINTF_A                   = 0x6FF // 1791
	SYS___SWPRINTF_A                    = 0x700 // 1792
	SYS___FSCANF_A                      = 0x701 // 1793
	SYS___SCANF_A                       = 0x702 // 1794
	SYS___SSCANF_A                      = 0x703 // 1795
	SYS___SWSCANF_A                     = 0x704 // 1796
	SYS___ATOF_A                        = 0x705 // 1797
	SYS___ATOI_A                        = 0x706 // 1798
	SYS___ATOL_A                        = 0x707 // 1799
	SYS___STRTOD_A                      = 0x708 // 1800
	SYS___STRTOL_A                      = 0x709 // 1801
	SYS___STRTOUL_A                     = 0x70A // 1802
	SYS_____AE_CORRESTBL_QUERY_A        = 0x70B // 1803
	SYS___A64L_A                        = 0x70C // 1804
	SYS___ECVT_A                        = 0x70D // 1805
	SYS___FCVT_A                        = 0x70E // 1806
	SYS___GCVT_A                        = 0x70F // 1807
	SYS___L64A_A                        = 0x710 // 1808
	SYS___STRERROR_A                    = 0x711 // 1809
	SYS___PERROR_A                      = 0x712 // 1810
	SYS___FETCH_A                       = 0x713 // 1811
	SYS___GETENV_A                      = 0x714 // 1812
	SYS___MKSTEMP_A                     = 0x717 // 1815
	SYS___PTSNAME_A                     = 0x718 // 1816
	SYS___PUTENV_A                      = 0x719 // 1817
	SYS___REALPATH_A                    = 0x71A // 1818
	SYS___SETENV_A                      = 0x71B // 1819
	SYS___SYSTEM_A                      = 0x71C // 1820
	SYS___GETOPT_A                      = 0x71D // 1821
	SYS___CATOPEN_A                     = 0x71E // 1822
	SYS___ACCESS_A                      = 0x71F // 1823
	SYS___CHDIR_A                       = 0x720 // 1824
	SYS___CHOWN_A                       = 0x721 // 1825
	SYS___CHROOT_A                      = 0x722 // 1826
	SYS___GETCWD_A                      = 0x723 // 1827
	SYS___GETWD_A                       = 0x724 // 1828
	SYS___LCHOWN_A                      = 0x725 // 1829
	SYS___LINK_A                        = 0x726 // 1830
	SYS___PATHCONF_A                    = 0x727 // 1831
	SYS___IF_NAMEINDEX_A                = 0x728 // 1832
	SYS___READLINK_A                    = 0x729 // 1833
	SYS___RMDIR_A                       = 0x72A // 1834
	SYS___STATVFS_A                     = 0x72B // 1835
	SYS___SYMLINK_A                     = 0x72C // 1836
	SYS___TRUNCATE_A                    = 0x72D // 1837
	SYS___UNLINK_A                      = 0x72E // 1838
	SYS___GAI_STRERROR_A                = 0x72F // 1839
	SYS___EXTLINK_NP_A                  = 0x730 // 1840
	SYS___ISALNUM_A                     = 0x731 // 1841
	SYS___ISALPHA_A                     = 0x732 // 1842
	SYS___A2E_S                         = 0x733 // 1843
	SYS___ISCNTRL_A                     = 0x734 // 1844
	SYS___ISDIGIT_A                     = 0x735 // 1845
	SYS___ISGRAPH_A                     = 0x736 // 1846
	SYS___ISLOWER_A                     = 0x737 // 1847
	SYS___ISPRINT_A                     = 0x738 // 1848
	SYS___ISPUNCT_A                     = 0x739 // 1849
	SYS___ISSPACE_A                     = 0x73A // 1850
	SYS___ISUPPER_A                     = 0x73B // 1851
	SYS___ISXDIGIT_A                    = 0x73C // 1852
	SYS___TOLOWER_A                     = 0x73D // 1853
	SYS___TOUPPER_A                     = 0x73E // 1854
	SYS___ISWALNUM_A                    = 0x73F // 1855
	SYS___ISWALPHA_A                    = 0x740 // 1856
	SYS___A2E_L                         = 0x741 // 1857
	SYS___ISWCNTRL_A                    = 0x742 // 1858
	SYS___ISWDIGIT_A                    = 0x743 // 1859
	SYS___ISWGRAPH_A                    = 0x744 // 1860
	SYS___ISWLOWER_A                    = 0x745 // 1861
	SYS___ISWPRINT_A                    = 0x746 // 1862
	SYS___ISWPUNCT_A                    = 0x747 // 1863
	SYS___ISWSPACE_A                    = 0x748 // 1864
	SYS___ISWUPPER_A                    = 0x749 // 1865
	SYS___ISWXDIGIT_A                   = 0x74A // 1866
	SYS___CONFSTR_A                     = 0x74B // 1867
	SYS___FTOK_A                        = 0x74C // 1868
	SYS___MKTEMP_A                      = 0x74D // 1869
	SYS___FDOPEN_A                      = 0x74E // 1870
	SYS___FLDATA_A                      = 0x74F // 1871
	SYS___REMOVE_A                      = 0x750 // 1872
	SYS___RENAME_A                      = 0x751 // 1873
	SYS___TMPNAM_A                      = 0x752 // 1874
	SYS___FOPEN_A                       = 0x753 // 1875
	SYS___FREOPEN_A                     = 0x754 // 1876
	SYS___CUSERID_A                     = 0x755 // 1877
	SYS___POPEN_A                       = 0x756 // 1878
	SYS___TEMPNAM_A                     = 0x757 // 1879
	SYS___FTW_A                         = 0x758 // 1880
	SYS___GETGRENT_A                    = 0x759 // 1881
	SYS___GETGRGID_A                    = 0x75A // 1882
	SYS___GETGRNAM_A                    = 0x75B // 1883
	SYS___GETGROUPSBYNAME_A             = 0x75C // 1884
	SYS___GETHOSTENT_A                  = 0x75D // 1885
	SYS___GETHOSTNAME_A                 = 0x75E // 1886
	SYS___GETLOGIN_A                    = 0x75F // 1887
	SYS___INET_NTOP_A                   = 0x760 // 1888
	SYS___GETPASS_A                     = 0x761 // 1889
	SYS___GETPWENT_A                    = 0x762 // 1890
	SYS___GETPWNAM_A                    = 0x763 // 1891
	SYS___GETPWUID_A                    = 0x764 // 1892
	SYS_____CHECK_RESOURCE_AUTH_NP_A    = 0x765 // 1893
	SYS___CHECKSCHENV_A                 = 0x766 // 1894
	SYS___CONNECTSERVER_A               = 0x767 // 1895
	SYS___CONNECTWORKMGR_A              = 0x768 // 1896
	SYS_____CONSOLE_A                   = 0x769 // 1897
	SYS___CREATEWORKUNIT_A              = 0x76A // 1898
	SYS___CTERMID_A                     = 0x76B // 1899
	SYS___FMTMSG_A                      = 0x76C // 1900
	SYS___INITGROUPS_A                  = 0x76D // 1901
	SYS_____LOGIN_A                     = 0x76E // 1902
	SYS___MSGRCV_A                      = 0x76F // 1903
	SYS___MSGSND_A                      = 0x770 // 1904
	SYS___MSGXRCV_A                     = 0x771 // 1905
	SYS___NFTW_A                        = 0x772 // 1906
	SYS_____PASSWD_A                    = 0x773 // 1907
	SYS___PTHREAD_SECURITY_NP_A         = 0x774 // 1908
	SYS___QUERYMETRICS_A                = 0x775 // 1909
	SYS___QUERYSCHENV                   = 0x776 // 1910
	SYS___READV_A                       = 0x777 // 1911
	SYS_____SERVER_CLASSIFY_A           = 0x778 // 1912
	SYS_____SERVER_INIT_A               = 0x779 // 1913
	SYS_____SERVER_PWU_A                = 0x77A // 1914
	SYS___STRCASECMP_A                  = 0x77B // 1915
	SYS___STRNCASECMP_A                 = 0x77C // 1916
	SYS___TTYNAME_A                     = 0x77D // 1917
	SYS___UNAME_A                       = 0x77E // 1918
	SYS___UTIMES_A                      = 0x77F // 1919
	SYS___W_GETPSENT_A                  = 0x780 // 1920
	SYS___WRITEV_A                      = 0x781 // 1921
	SYS___W_STATFS_A                    = 0x782 // 1922
	SYS___W_STATVFS_A                   = 0x783 // 1923
	SYS___FPUTC_A                       = 0x784 // 1924
	SYS___PUTCHAR_A                     = 0x785 // 1925
	SYS___PUTS_A                        = 0x786 // 1926
	SYS___FGETS_A                       = 0x787 // 1927
	SYS___GETS_A                        = 0x788 // 1928
	SYS___FPUTS_A                       = 0x789 // 1929
	SYS___FREAD_A                       = 0x78A // 1930
	SYS___FWRITE_A                      = 0x78B // 1931
	SYS___OPEN_O_A                      = 0x78C // 1932
	SYS___ISASCII                       = 0x78D // 1933
	SYS___CREAT_O_A                     = 0x78E // 1934
	SYS___ENVNA                         = 0x78F // 1935
	SYS___PUTC_A                        = 0x790 // 1936
	SYS___AE_THREAD_SETMODE             = 0x791 // 1937
	SYS___AE_THREAD_SWAPMODE            = 0x792 // 1938
	SYS___GETNETBYADDR_A                = 0x793 // 1939
	SYS___GETNETBYNAME_A                = 0x794 // 1940
	SYS___GETNETENT_A                   = 0x795 // 1941
	SYS___GETPROTOBYNAME_A              = 0x796 // 1942
	SYS___GETPROTOBYNUMBER_A            = 0x797 // 1943
	SYS___GETPROTOENT_A                 = 0x798 // 1944
	SYS___GETSERVBYNAME_A               = 0x799 // 1945
	SYS___GETSERVBYPORT_A               = 0x79A // 1946
	SYS___GETSERVENT_A                  = 0x79B // 1947
	SYS___ASCTIME_A                     = 0x79C // 1948
	SYS___CTIME_A                       = 0x79D // 1949
	SYS___GETDATE_A                     = 0x79E // 1950
	SYS___TZSET_A                       = 0x79F // 1951
	SYS___UTIME_A                       = 0x7A0 // 1952
	SYS___ASCTIME_R_A                   = 0x7A1 // 1953
	SYS___CTIME_R_A                     = 0x7A2 // 1954
	SYS___STRTOLL_A                     = 0x7A3 // 1955
	SYS___STRTOULL_A                    = 0x7A4 // 1956
	SYS___FPUTWC_A                      = 0x7A5 // 1957
	SYS___PUTWC_A                       = 0x7A6 // 1958
	SYS___PUTWCHAR_A                    = 0x7A7 // 1959
	SYS___FPUTWS_A                      = 0x7A8 // 1960
	SYS___UNGETWC_A                     = 0x7A9 // 1961
	SYS___FGETWC_A                      = 0x7AA // 1962
	SYS___GETWC_A                       = 0x7AB // 1963
	SYS___GETWCHAR_A                    = 0x7AC // 1964
	SYS___FGETWS_A                      = 0x7AD // 1965
	SYS___GETTIMEOFDAY_A                = 0x7AE // 1966
	SYS___GMTIME_A                      = 0x7AF // 1967
	SYS___GMTIME_R_A                    = 0x7B0 // 1968
	SYS___LOCALTIME_A                   = 0x7B1 // 1969
	SYS___LOCALTIME_R_A                 = 0x7B2 // 1970
	SYS___MKTIME_A                      = 0x7B3 // 1971
	SYS___TZZNA                         = 0x7B4 // 1972
	SYS_UNATEXIT                        = 0x7B5 // 1973
	SYS___CEE3DMP_A                     = 0x7B6 // 1974
	SYS___CDUMP_A                       = 0x7B7 // 1975
	SYS___CSNAP_A                       = 0x7B8 // 1976
	SYS___CTEST_A                       = 0x7B9 // 1977
	SYS___CTRACE_A                      = 0x7BA // 1978
	SYS___VSWPRNTF2_A                   = 0x7BB // 1979
	SYS___INET_PTON_A                   = 0x7BC // 1980
	SYS___SYSLOG_A                      = 0x7BD // 1981
	SYS___CRYPT_A                       = 0x7BE // 1982
	SYS_____OPENDIR2_A                  = 0x7BF // 1983
	SYS_____READDIR2_A                  = 0x7C0 // 1984
	SYS___OPENDIR_A                     = 0x7C2 // 1986
	SYS___READDIR_A                     = 0x7C3 // 1987
	SYS_PREAD                           = 0x7C7 // 1991
	SYS_PWRITE                          = 0x7C8 // 1992
	SYS_M_CREATE_LAYOUT                 = 0x7C9 // 1993
	SYS_M_DESTROY_LAYOUT                = 0x7CA // 1994
	SYS_M_GETVALUES_LAYOUT              = 0x7CB // 1995
	SYS_M_SETVALUES_LAYOUT              = 0x7CC // 1996
	SYS_M_TRANSFORM_LAYOUT              = 0x7CD // 1997
	SYS_M_WTRANSFORM_LAYOUT             = 0x7CE // 1998
	SYS_FWPRINTF                        = 0x7D1 // 2001
	SYS_WPRINTF                         = 0x7D2 // 2002
	SYS_VFWPRINT                        = 0x7D3 // 2003
	SYS_VFWPRINTF                       = 0x7D3 // 2003
	SYS_VWPRINTF                        = 0x7D4 // 2004
	SYS_FWSCANF                         = 0x7D5 // 2005
	SYS_WSCANF                          = 0x7D6 // 2006
	SYS_WCTRANS                         = 0x7D7 // 2007
	SYS_TOWCTRAN                        = 0x7D8 // 2008
	SYS_TOWCTRANS                       = 0x7D8 // 2008
	SYS___WCSTOD_A                      = 0x7D9 // 2009
	SYS___WCSTOL_A                      = 0x7DA // 2010
	SYS___WCSTOUL_A                     = 0x7DB // 2011
	SYS___BASENAME_A                    = 0x7DC // 2012
	SYS___DIRNAME_A                     = 0x7DD // 2013
	SYS___GLOB_A                        = 0x7DE // 2014
	SYS_FWIDE                           = 0x7DF // 2015
	SYS___OSNAME                        = 0x7E0 // 2016
	SYS_____OSNAME_A                    = 0x7E1 // 2017
	SYS___BTOWC_A                       = 0x7E4 // 2020
	SYS___WCTOB_A                       = 0x7E5 // 2021
	SYS___DBM_OPEN_A                    = 0x7E6 // 2022
	SYS___VFPRINTF2_A                   = 0x7E7 // 2023
	SYS___VPRINTF2_A                    = 0x7E8 // 2024
	SYS___VSPRINTF2_A                   = 0x7E9 // 2025
	SYS___CEIL_H                        = 0x7EA // 2026
	SYS___FLOOR_H                       = 0x7EB // 2027
	SYS___MODF_H                        = 0x7EC // 2028
	SYS___FABS_H                        = 0x7ED // 2029
	SYS___J0_H                          = 0x7EE // 2030
	SYS___J1_H                          = 0x7EF // 2031
	SYS___JN_H                          = 0x7F0 // 2032
	SYS___Y0_H                          = 0x7F1 // 2033
	SYS___Y1_H                          = 0x7F2 // 2034
	SYS___YN_H                          = 0x7F3 // 2035
	SYS___CEILF_H                       = 0x7F4 // 2036
	SYS___CEILL_H                       = 0x7F5 // 2037
	SYS___FLOORF_H                      = 0x7F6 // 2038
	SYS___FLOORL_H                      = 0x7F7 // 2039
	SYS___MODFF_H                       = 0x7F8 // 2040
	SYS___MODFL_H                       = 0x7F9 // 2041
	SYS___FABSF_H                       = 0x7FA // 2042
	SYS___FABSL_H                       = 0x7FB // 2043
	SYS___MALLOC24                      = 0x7FC // 2044
	SYS___MALLOC31                      = 0x7FD // 2045
	SYS_ACL_INIT                        = 0x7FE // 2046
	SYS_ACL_FREE                        = 0x7FF // 2047
	SYS_ACL_FIRST_ENTRY                 = 0x800 // 2048
	SYS_ACL_GET_ENTRY                   = 0x801 // 2049
	SYS_ACL_VALID                       = 0x802 // 2050
	SYS_ACL_CREATE_ENTRY                = 0x803 // 2051
	SYS_ACL_DELETE_ENTRY                = 0x804 // 2052
	SYS_ACL_UPDATE_ENTRY                = 0x805 // 2053
	SYS_ACL_DELETE_FD                   = 0x806 // 2054
	SYS_ACL_DELETE_FILE                 = 0x807 // 2055
	SYS_ACL_GET_FD                      = 0x808 // 2056
	SYS_ACL_GET_FILE                    = 0x809 // 2057
	SYS_ACL_SET_FD                      = 0x80A // 2058
	SYS_ACL_SET_FILE                    = 0x80B // 2059
	SYS_ACL_FROM_TEXT                   = 0x80C // 2060
	SYS_ACL_TO_TEXT                     = 0x80D // 2061
	SYS_ACL_SORT                        = 0x80E // 2062
	SYS___SHUTDOWN_REGISTRATION         = 0x80F // 2063
	SYS___ERFL_B                        = 0x810 // 2064
	SYS___ERFCL_B                       = 0x811 // 2065
	SYS___LGAMMAL_B                     = 0x812 // 2066
	SYS___SETHOOKEVENTS                 = 0x813 // 2067
	SYS_IF_NAMETOINDEX                  = 0x814 // 2068
	SYS_IF_INDEXTONAME                  = 0x815 // 2069
	SYS_IF_NAMEINDEX                    = 0x816 // 2070
	SYS_IF_FREENAMEINDEX                = 0x817 // 2071
	SYS_GETADDRINFO                     = 0x818 // 2072
	SYS_GETNAMEINFO                     = 0x819 // 2073
	SYS_FREEADDRINFO                    = 0x81A // 2074
	SYS_GAI_STRERROR                    = 0x81B // 2075
	SYS_REXEC_AF                        = 0x81C // 2076
	SYS___POE                           = 0x81D // 2077
	SYS___DYNALLOC_A                    = 0x81F // 2079
	SYS___DYNFREE_A                     = 0x820 // 2080
	SYS___RES_QUERY_A                   = 0x821 // 2081
	SYS___RES_SEARCH_A                  = 0x822 // 2082
	SYS___RES_QUERYDOMAIN_A             = 0x823 // 2083
	SYS___RES_MKQUERY_A                 = 0x824 // 2084
	SYS___RES_SEND_A                    = 0x825 // 2085
	SYS___DN_EXPAND_A                   = 0x826 // 2086
	SYS___DN_SKIPNAME_A                 = 0x827 // 2087
	SYS___DN_COMP_A                     = 0x828 // 2088
	SYS___DN_FIND_A                     = 0x829 // 2089
	SYS___NLIST_A                       = 0x82A // 2090
	SYS_____TCGETCP_A                   = 0x82B // 2091
	SYS_____TCSETCP_A                   = 0x82C // 2092
	SYS_____W_PIOCTL_A                  = 0x82E // 2094
	SYS___INET_ADDR_A                   = 0x82F // 2095
	SYS___INET_NTOA_A                   = 0x830 // 2096
	SYS___INET_NETWORK_A                = 0x831 // 2097
	SYS___ACCEPT_A                      = 0x832 // 2098
	SYS___ACCEPT_AND_RECV_A             = 0x833 // 2099
	SYS___BIND_A                        = 0x834 // 2100
	SYS___CONNECT_A                     = 0x835 // 2101
	SYS___GETPEERNAME_A                 = 0x836 // 2102
	SYS___GETSOCKNAME_A                 = 0x837 // 2103
	SYS___RECVFROM_A                    = 0x838 // 2104
	SYS___SENDTO_A                      = 0x839 // 2105
	SYS___SENDMSG_A                     = 0x83A // 2106
	SYS___RECVMSG_A                     = 0x83B // 2107
	SYS_____LCHATTR_A                   = 0x83C // 2108
	SYS___CABEND                        = 0x83D // 2109
	SYS___LE_CIB_GET                    = 0x83E // 2110
	SYS___SET_LAA_FOR_JIT               = 0x83F // 2111
	SYS___LCHATTR                       = 0x840 // 2112
	SYS___WRITEDOWN                     = 0x841 // 2113
	SYS_PTHREAD_MUTEX_INIT2             = 0x842 // 2114
	SYS___ACOSHF_B                      = 0x843 // 2115
	SYS___ACOSHL_B                      = 0x844 // 2116
	SYS___ASINHF_B                      = 0x845 // 2117
	SYS___ASINHL_B                      = 0x846 // 2118
	SYS___ATANHF_B                      = 0x847 // 2119
	SYS___ATANHL_B                      = 0x848 // 2120
	SYS___CBRTF_B                       = 0x849 // 2121
	SYS___CBRTL_B                       = 0x84A // 2122
	SYS___COPYSIGNF_B                   = 0x84B // 2123
	SYS___COPYSIGNL_B                   = 0x84C // 2124
	SYS___COTANF_B                      = 0x84D // 2125
	SYS___COTAN_B                       = 0x84E // 2126
	SYS___COTANL_B                      = 0x84F // 2127
	SYS___EXP2F_B                       = 0x850 // 2128
	SYS___EXP2L_B                       = 0x851 // 2129
	SYS___EXPM1F_B                      = 0x852 // 2130
	SYS___EXPM1L_B                      = 0x853 // 2131
	SYS___FDIMF_B                       = 0x854 // 2132
	SYS___FDIM_B                        = 0x855 // 2133
	SYS___FDIML_B                       = 0x856 // 2134
	SYS___HYPOTF_B                      = 0x857 // 2135
	SYS___HYPOTL_B                      = 0x858 // 2136
	SYS___LOG1PF_B                      = 0x859 // 2137
	SYS___LOG1PL_B                      = 0x85A // 2138
	SYS___LOG2F_B                       = 0x85B // 2139
	SYS___LOG2_B                        = 0x85C // 2140
	SYS___LOG2L_B                       = 0x85D // 2141
	SYS___REMAINDERF_B                  = 0x85E // 2142
	SYS___REMAINDERL_B                  = 0x85F // 2143
	SYS___REMQUOF_B                     = 0x860 // 2144
	SYS___REMQUO_B                      = 0x861 // 2145
	SYS___REMQUOL_B                     = 0x862 // 2146
	SYS___TGAMMAF_B                     = 0x863 // 2147
	SYS___TGAMMA_B                      = 0x864 // 2148
	SYS___TGAMMAL_B                     = 0x865 // 2149
	SYS___TRUNCF_B                      = 0x866 // 2150
	SYS___TRUNC_B                       = 0x867 // 2151
	SYS___TRUNCL_B                      = 0x868 // 2152
	SYS___LGAMMAF_B                     = 0x869 // 2153
	SYS___LROUNDF_B                     = 0x86A // 2154
	SYS___LROUND_B                      = 0x86B // 2155
	SYS___ERFF_B                        = 0x86C // 2156
	SYS___ERFCF_B                       = 0x86D // 2157
	SYS_ACOSHF                          = 0x86E // 2158
	SYS_ACOSHL                          = 0x86F // 2159
	SYS_ASINHF                          = 0x870 // 2160
	SYS_ASINHL                          = 0x871 // 2161
	SYS_ATANHF                          = 0x872 // 2162
	SYS_ATANHL                          = 0x873 // 2163
	SYS_CBRTF                           = 0x874 // 2164
	SYS_CBRTL                           = 0x875 // 2165
	SYS_COPYSIGNF                       = 0x876 // 2166
	SYS_CPYSIGNF                        = 0x876 // 2166
	SYS_COPYSIGNL                       = 0x877 // 2167
	SYS_CPYSIGNL                        = 0x877 // 2167
	SYS_COTANF                          = 0x878 // 2168
	SYS___COTANF                        = 0x878 // 2168
	SYS_COTAN                           = 0x879 // 2169
	SYS___COTAN                         = 0x879 // 2169
	SYS_COTANL                          = 0x87A // 2170
	SYS___COTANL                        = 0x87A // 2170
	SYS_EXP2F                           = 0x87B // 2171
	SYS_EXP2L                           = 0x87C // 2172
	SYS_EXPM1F                          = 0x87D // 2173
	SYS_EXPM1L                          = 0x87E // 2174
	SYS_FDIMF                           = 0x87F // 2175
	SYS_FDIM                            = 0x881 // 2177
	SYS_FDIML                           = 0x882 // 2178
	SYS_HYPOTF                          = 0x883 // 2179
	SYS_HYPOTL                          = 0x884 // 2180
	SYS_LOG1PF                          = 0x885 // 2181
	SYS_LOG1PL                          = 0x886 // 2182
	SYS_LOG2F                           = 0x887 // 2183
	SYS_LOG2                            = 0x888 // 2184
	SYS_LOG2L                           = 0x889 // 2185
	SYS_REMAINDERF                      = 0x88A // 2186
	SYS_REMAINDF                        = 0x88A // 2186
	SYS_REMAINDERL                      = 0x88B // 2187
	SYS_REMAINDL                        = 0x88B // 2187
	SYS_REMQUOF                         = 0x88C // 2188
	SYS_REMQUO                          = 0x88D // 2189
	SYS_REMQUOL                         = 0x88E // 2190
	SYS_TGAMMAF                         = 0x88F // 2191
	SYS_TGAMMA                          = 0x890 // 2192
	SYS_TGAMMAL                         = 0x891 // 2193
	SYS_TRUNCF                          = 0x892 // 2194
	SYS_TRUNC                           = 0x893 // 2195
	SYS_TRUNCL                          = 0x894 // 2196
	SYS_LGAMMAF                         = 0x895 // 2197
	SYS_LGAMMAL                         = 0x896 // 2198
	SYS_LROUNDF                         = 0x897 // 2199
	SYS_LROUND                          = 0x898 // 2200
	SYS_ERFF                            = 0x899 // 2201
	SYS_ERFL                            = 0x89A // 2202
	SYS_ERFCF                           = 0x89B // 2203
	SYS_ERFCL                           = 0x89C // 2204
	SYS___EXP2_B                        = 0x89D // 2205
	SYS_EXP2                            = 0x89E // 2206
	SYS___FAR_JUMP                      = 0x89F // 2207
	SYS___TCGETATTR_A                   = 0x8A1 // 2209
	SYS___TCSETATTR_A                   = 0x8A2 // 2210
	SYS___SUPERKILL                     = 0x8A4 // 2212
	SYS___LE_CONDITION_TOKEN_BUILD      = 0x8A5 // 2213
	SYS___LE_MSG_ADD_INSERT             = 0x8A6 // 2214
	SYS___LE_MSG_GET                    = 0x8A7 // 2215
	SYS___LE_MSG_GET_AND_WRITE          = 0x8A8 // 2216
	SYS___LE_MSG_WRITE                  = 0x8A9 // 2217
	SYS___ITOA                          = 0x8AA // 2218
	SYS___UTOA                          = 0x8AB // 2219
	SYS___LTOA                          = 0x8AC // 2220
	SYS___ULTOA                         = 0x8AD // 2221
	SYS___LLTOA                         = 0x8AE // 2222
	SYS___ULLTOA                        = 0x8AF // 2223
	SYS___ITOA_A                        = 0x8B0 // 2224
	SYS___UTOA_A                        = 0x8B1 // 2225
	SYS___LTOA_A                        = 0x8B2 // 2226
	SYS___ULTOA_A                       = 0x8B3 // 2227
	SYS___LLTOA_A                       = 0x8B4 // 2228
	SYS___ULLTOA_A                      = 0x8B5 // 2229
	SYS_____GETENV_A                    = 0x8C3 // 2243
	SYS___REXEC_A                       = 0x8C4 // 2244
	SYS___REXEC_AF_A                    = 0x8C5 // 2245
	SYS___GETUTXENT_A                   = 0x8C6 // 2246
	SYS___GETUTXID_A                    = 0x8C7 // 2247
	SYS___GETUTXLINE_A                  = 0x8C8 // 2248
	SYS___PUTUTXLINE_A                  = 0x8C9 // 2249
	SYS_____UTMPXNAME_A                 = 0x8CA // 2250
	SYS___PUTC_UNLOCKED_A               = 0x8CB // 2251
	SYS___PUTCHAR_UNLOCKED_A            = 0x8CC // 2252
	SYS___SNPRINTF_A                    = 0x8CD // 2253
	SYS___VSNPRINTF_A                   = 0x8CE // 2254
	SYS___DLOPEN_A                      = 0x8D0 // 2256
	SYS___DLSYM_A                       = 0x8D1 // 2257
	SYS___DLERROR_A                     = 0x8D2 // 2258
	SYS_FLOCKFILE                       = 0x8D3 // 2259
	SYS_FTRYLOCKFILE                    = 0x8D4 // 2260
	SYS_FUNLOCKFILE                     = 0x8D5 // 2261
	SYS_GETC_UNLOCKED                   = 0x8D6 // 2262
	SYS_GETCHAR_UNLOCKED                = 0x8D7 // 2263
	SYS_PUTC_UNLOCKED                   = 0x8D8 // 2264
	SYS_PUTCHAR_UNLOCKED                = 0x8D9 // 2265
	SYS_SNPRINTF                        = 0x8DA // 2266
	SYS_VSNPRINTF                       = 0x8DB // 2267
	SYS_DLOPEN                          = 0x8DD // 2269
	SYS_DLSYM                           = 0x8DE // 2270
	SYS_DLCLOSE                         = 0x8DF // 2271
	SYS_DLERROR                         = 0x8E0 // 2272
	SYS___SET_EXCEPTION_HANDLER         = 0x8E2 // 2274
	SYS___RESET_EXCEPTION_HANDLER       = 0x8E3 // 2275
	SYS___VHM_EVENT                     = 0x8E4 // 2276
	SYS___ABS_H                         = 0x8E6 // 2278
	SYS___ABSF_H                        = 0x8E7 // 2279
	SYS___ABSL_H                        = 0x8E8 // 2280
	SYS___ACOS_H                        = 0x8E9 // 2281
	SYS___ACOSF_H                       = 0x8EA // 2282
	SYS___ACOSL_H                       = 0x8EB // 2283
	SYS___ACOSH_H                       = 0x8EC // 2284
	SYS___ASIN_H                        = 0x8ED // 2285
	SYS___ASINF_H                       = 0x8EE // 2286
	SYS___ASINL_H                       = 0x8EF // 2287
	SYS___ASINH_H                       = 0x8F0 // 2288
	SYS___ATAN_H                        = 0x8F1 // 2289
	SYS___ATANF_H                       = 0x8F2 // 2290
	SYS___ATANL_H                       = 0x8F3 // 2291
	SYS___ATANH_H                       = 0x8F4 // 2292
	SYS___ATANHF_H                      = 0x8F5 // 2293
	SYS___ATANHL_H                      = 0x8F6 // 2294
	SYS___ATAN2_H                       = 0x8F7 // 2295
	SYS___ATAN2F_H                      = 0x8F8 // 2296
	SYS___ATAN2L_H                      = 0x8F9 // 2297
	SYS___CBRT_H                        = 0x8FA // 2298
	SYS___COPYSIGNF_H                   = 0x8FB // 2299
	SYS___COPYSIGNL_H                   = 0x8FC // 2300
	SYS___COS_H                         = 0x8FD // 2301
	SYS___COSF_H                        = 0x8FE // 2302
	SYS___COSL_H                        = 0x8FF // 2303
	SYS___COSHF_H                       = 0x900 // 2304
	SYS___COSHL_H                       = 0x901 // 2305
	SYS___COTAN_H                       = 0x902 // 2306
	SYS___COTANF_H                      = 0x903 // 2307
	SYS___COTANL_H                      = 0x904 // 2308
	SYS___ERF_H                         = 0x905 // 2309
	SYS___ERFF_H                        = 0x906 // 2310
	SYS___ERFL_H                        = 0x907 // 2311
	SYS___ERFC_H                        = 0x908 // 2312
	SYS___ERFCF_H                       = 0x909 // 2313
	SYS___ERFCL_H                       = 0x90A // 2314
	SYS___EXP_H                         = 0x90B // 2315
	SYS___EXPF_H                        = 0x90C // 2316
	SYS___EXPL_H                        = 0x90D // 2317
	SYS___EXPM1_H                       = 0x90E // 2318
	SYS___FDIM_H                        = 0x90F // 2319
	SYS___FDIMF_H                       = 0x910 // 2320
	SYS___FDIML_H                       = 0x911 // 2321
	SYS___FMOD_H                        = 0x912 // 2322
	SYS___FMODF_H                       = 0x913 // 2323
	SYS___FMODL_H                       = 0x914 // 2324
	SYS___GAMMA_H                       = 0x915 // 2325
	SYS___HYPOT_H                       = 0x916 // 2326
	SYS___ILOGB_H                       = 0x917 // 2327
	SYS___LGAMMA_H                      = 0x918 // 2328
	SYS___LGAMMAF_H                     = 0x919 // 2329
	SYS___LOG_H                         = 0x91A // 2330
	SYS___LOGF_H                        = 0x91B // 2331
	SYS___LOGL_H                        = 0x91C // 2332
	SYS___LOGB_H                        = 0x91D // 2333
	SYS___LOG2_H                        = 0x91E // 2334
	SYS___LOG2F_H                       = 0x91F // 2335
	SYS___LOG2L_H                       = 0x920 // 2336
	SYS___LOG1P_H                       = 0x921 // 2337
	SYS___LOG10_H                       = 0x922 // 2338
	SYS___LOG10F_H                      = 0x923 // 2339
	SYS___LOG10L_H                      = 0x924 // 2340
	SYS___LROUND_H                      = 0x925 // 2341
	SYS___LROUNDF_H                     = 0x926 // 2342
	SYS___NEXTAFTER_H                   = 0x927 // 2343
	SYS___POW_H                         = 0x928 // 2344
	SYS___POWF_H                        = 0x929 // 2345
	SYS___POWL_H                        = 0x92A // 2346
	SYS___REMAINDER_H                   = 0x92B // 2347
	SYS___RINT_H                        = 0x92C // 2348
	SYS___SCALB_H                       = 0x92D // 2349
	SYS___SIN_H                         = 0x92E // 2350
	SYS___SINF_H                        = 0x92F // 2351
	SYS___SINL_H                        = 0x930 // 2352
	SYS___SINH_H                        = 0x931 // 2353
	SYS___SINHF_H                       = 0x932 // 2354
	SYS___SINHL_H                       = 0x933 // 2355
	SYS___SQRT_H                        = 0x934 // 2356
	SYS___SQRTF_H                       = 0x935 // 2357
	SYS___SQRTL_H                       = 0x936 // 2358
	SYS___TAN_H                         = 0x937 // 2359
	SYS___TANF_H                        = 0x938 // 2360
	SYS___TANL_H                        = 0x939 // 2361
	SYS___TANH_H                        = 0x93A // 2362
	SYS___TANHF_H                       = 0x93B // 2363
	SYS___TANHL_H                       = 0x93C // 2364
	SYS___TGAMMA_H                      = 0x93D // 2365
	SYS___TGAMMAF_H                     = 0x93E // 2366
	SYS___TRUNC_H                       = 0x93F // 2367
	SYS___TRUNCF_H                      = 0x940 // 2368
	SYS___TRUNCL_H                      = 0x941 // 2369
	SYS___COSH_H                        = 0x942 // 2370
	SYS___LE_DEBUG_SET_RESUME_MCH       = 0x943 // 2371
	SYS_VFSCANF                         = 0x944 // 2372
	SYS_VSCANF                          = 0x946 // 2374
	SYS_VSSCANF                         = 0x948 // 2376
	SYS_VFWSCANF                        = 0x94A // 2378
	SYS_VWSCANF                         = 0x94C // 2380
	SYS_VSWSCANF                        = 0x94E // 2382
	SYS_IMAXABS                         = 0x950 // 2384
	SYS_IMAXDIV                         = 0x951 // 2385
	SYS_STRTOIMAX                       = 0x952 // 2386
	SYS_STRTOUMAX                       = 0x953 // 2387
	SYS_WCSTOIMAX                       = 0x954 // 2388
	SYS_WCSTOUMAX                       = 0x955 // 2389
	SYS_ATOLL                           = 0x956 // 2390
	SYS_STRTOF                          = 0x957 // 2391
	SYS_STRTOLD                         = 0x958 // 2392
	SYS_WCSTOF                          = 0x959 // 2393
	SYS_WCSTOLD                         = 0x95A // 2394
	SYS_INET6_RTH_SPACE                 = 0x95B // 2395
	SYS_INET6_RTH_INIT                  = 0x95C // 2396
	SYS_INET6_RTH_ADD                   = 0x95D // 2397
	SYS_INET6_RTH_REVERSE               = 0x95E // 2398
	SYS_INET6_RTH_SEGMENTS              = 0x95F // 2399
	SYS_INET6_RTH_GETADDR               = 0x960 // 2400
	SYS_INET6_OPT_INIT                  = 0x961 // 2401
	SYS_INET6_OPT_APPEND                = 0x962 // 2402
	SYS_INET6_OPT_FINISH                = 0x963 // 2403
	SYS_INET6_OPT_SET_VAL               = 0x964 // 2404
	SYS_INET6_OPT_NEXT                  = 0x965 // 2405
	SYS_INET6_OPT_FIND                  = 0x966 // 2406
	SYS_INET6_OPT_GET_VAL               = 0x967 // 2407
	SYS___POW_I                         = 0x987 // 2439
	SYS___POW_I_B                       = 0x988 // 2440
	SYS___POW_I_H                       = 0x989 // 2441
	SYS___POW_II                        = 0x98A // 2442
	SYS___POW_II_B                      = 0x98B // 2443
	SYS___POW_II_H                      = 0x98C // 2444
	SYS_CABS                            = 0x98E // 2446
	SYS___CABS_B                        = 0x98F // 2447
	SYS___CABS_H                        = 0x990 // 2448
	SYS_CABSF                           = 0x991 // 2449
	SYS___CABSF_B                       = 0x992 // 2450
	SYS___CABSF_H                       = 0x993 // 2451
	SYS_CABSL                           = 0x994 // 2452
	SYS___CABSL_B                       = 0x995 // 2453
	SYS___CABSL_H                       = 0x996 // 2454
	SYS_CACOS                           = 0x997 // 2455
	SYS___CACOS_B                       = 0x998 // 2456
	SYS___CACOS_H                       = 0x999 // 2457
	SYS_CACOSF                          = 0x99A // 2458
	SYS___CACOSF_B                      = 0x99B // 2459
	SYS___CACOSF_H                      = 0x99C // 2460
	SYS_CACOSL                          = 0x99D // 2461
	SYS___CACOSL_B                      = 0x99E // 2462
	SYS___CACOSL_H                      = 0x99F // 2463
	SYS_CACOSH                          = 0x9A0 // 2464
	SYS___CACOSH_B                      = 0x9A1 // 2465
	SYS___CACOSH_H                      = 0x9A2 // 2466
	SYS_CACOSHF                         = 0x9A3 // 2467
	SYS___CACOSHF_B                     = 0x9A4 // 2468
	SYS___CACOSHF_H                     = 0x9A5 // 2469
	SYS_CACOSHL                         = 0x9A6 // 2470
	SYS___CACOSHL_B                     = 0x9A7 // 2471
	SYS___CACOSHL_H                     = 0x9A8 // 2472
	SYS_CARG                            = 0x9A9 // 2473
	SYS___CARG_B                        = 0x9AA // 2474
	SYS___CARG_H                        = 0x9AB // 2475
	SYS_CARGF                           = 0x9AC // 2476
	SYS___CARGF_B                       = 0x9AD // 2477
	SYS___CARGF_H                       = 0x9AE // 2478
	SYS_CARGL                           = 0x9AF // 2479
	SYS___CARGL_B                       = 0x9B0 // 2480
	SYS___CARGL_H                       = 0x9B1 // 2481
	SYS_CASIN                           = 0x9B2 // 2482
	SYS___CASIN_B                       = 0x9B3 // 2483
	SYS___CASIN_H                       = 0x9B4 // 2484
	SYS_CASINF                          = 0x9B5 // 2485
	SYS___CASINF_B                      = 0x9B6 // 2486
	SYS___CASINF_H                      = 0x9B7 // 2487
	SYS_CASINL                          = 0x9B8 // 2488
	SYS___CASINL_B                      = 0x9B9 // 2489
	SYS___CASINL_H                      = 0x9BA // 2490
	SYS_CASINH                          = 0x9BB // 2491
	SYS___CASINH_B                      = 0x9BC // 2492
	SYS___CASINH_H                      = 0x9BD // 2493
	SYS_CASINHF                         = 0x9BE // 2494
	SYS___CASINHF_B                     = 0x9BF // 2495
	SYS___CASINHF_H                     = 0x9C0 // 2496
	SYS_CASINHL                         = 0x9C1 // 2497
	SYS___CASINHL_B                     = 0x9C2 // 2498
	SYS___CASINHL_H                     = 0x9C3 // 2499
	SYS_CATAN                           = 0x9C4 // 2500
	SYS___CATAN_B                       = 0x9C5 // 2501
	SYS___CATAN_H                       = 0x9C6 // 2502
	SYS_CATANF                          = 0x9C7 // 2503
	SYS___CATANF_B                      = 0x9C8 // 2504
	SYS___CATANF_H                      = 0x9C9 // 2505
	SYS_CATANL                          = 0x9CA // 2506
	SYS___CATANL_B                      = 0x9CB // 2507
	SYS___CATANL_H                      = 0x9CC // 2508
	SYS_CATANH                          = 0x9CD // 2509
	SYS___CATANH_B                      = 0x9CE // 2510
	SYS___CATANH_H                      = 0x9CF // 2511
	SYS_CATANHF                         = 0x9D0 // 2512
	SYS___CATANHF_B                     = 0x9D1 // 2513
	SYS___CATANHF_H                     = 0x9D2 // 2514
	SYS_CATANHL                         = 0x9D3 // 2515
	SYS___CATANHL_B                     = 0x9D4 // 2516
	SYS___CATANHL_H                     = 0x9D5 // 2517
	SYS_CCOS                            = 0x9D6 // 2518
	SYS___CCOS_B                        = 0x9D7 // 2519
	SYS___CCOS_H                        = 0x9D8 // 2520
	SYS_CCOSF                           = 0x9D9 // 2521
	SYS___CCOSF_B                       = 0x9DA // 2522
	SYS___CCOSF_H                       = 0x9DB // 2523
	SYS_CCOSL                           = 0x9DC // 2524
	SYS___CCOSL_B                       = 0x9DD // 2525
	SYS___CCOSL_H                       = 0x9DE // 2526
	SYS_CCOSH                           = 0x9DF // 2527
	SYS___CCOSH_B                       = 0x9E0 // 2528
	SYS___CCOSH_H                       = 0x9E1 // 2529
	SYS_CCOSHF                          = 0x9E2 // 2530
	SYS___CCOSHF_B                      = 0x9E3 // 2531
	SYS___CCOSHF_H                      = 0x9E4 // 2532
	SYS_CCOSHL                          = 0x9E5 // 2533
	SYS___CCOSHL_B                      = 0x9E6 // 2534
	SYS___CCOSHL_H                      = 0x9E7 // 2535
	SYS_CEXP                            = 0x9E8 // 2536
	SYS___CEXP_B                        = 0x9E9 // 2537
	SYS___CEXP_H                        = 0x9EA // 2538
	SYS_CEXPF                           = 0x9EB // 2539
	SYS___CEXPF_B                       = 0x9EC // 2540
	SYS___CEXPF_H                       = 0x9ED // 2541
	SYS_CEXPL                           = 0x9EE // 2542
	SYS___CEXPL_B                       = 0x9EF // 2543
	SYS___CEXPL_H                       = 0x9F0 // 2544
	SYS_CIMAG                           = 0x9F1 // 2545
	SYS___CIMAG_B                       = 0x9F2 // 2546
	SYS___CIMAG_H                       = 0x9F3 // 2547
	SYS_CIMAGF                          = 0x9F4 // 2548
	SYS___CIMAGF_B                      = 0x9F5 // 2549
	SYS___CIMAGF_H                      = 0x9F6 // 2550
	SYS_CIMAGL                          = 0x9F7 // 2551
	SYS___CIMAGL_B                      = 0x9F8 // 2552
	SYS___CIMAGL_H                      = 0x9F9 // 2553
	SYS___CLOG                          = 0x9FA // 2554
	SYS___CLOG_B                        = 0x9FB // 2555
	SYS___CLOG_H                        = 0x9FC // 2556
	SYS_CLOGF                           = 0x9FD // 2557
	SYS___CLOGF_B                       = 0x9FE // 2558
	SYS___CLOGF_H                       = 0x9FF // 2559
	SYS_CLOGL                           = 0xA00 // 2560
	SYS___CLOGL_B                       = 0xA01 // 2561
	SYS___CLOGL_H                       = 0xA02 // 2562
	SYS_CONJ                            = 0xA03 // 2563
	SYS___CONJ_B                        = 0xA04 // 2564
	SYS___CONJ_H                        = 0xA05 // 2565
	SYS_CONJF                           = 0xA06 // 2566
	SYS___CONJF_B                       = 0xA07 // 2567
	SYS___CONJF_H                       = 0xA08 // 2568
	SYS_CONJL                           = 0xA09 // 2569
	SYS___CONJL_B                       = 0xA0A // 2570
	SYS___CONJL_H                       = 0xA0B // 2571
	SYS_CPOW                            = 0xA0C // 2572
	SYS___CPOW_B                        = 0xA0D // 2573
	SYS___CPOW_H                        = 0xA0E // 2574
	SYS_CPOWF                           = 0xA0F // 2575
	SYS___CPOWF_B                       = 0xA10 // 2576
	SYS___CPOWF_H                       = 0xA11 // 2577
	SYS_CPOWL                           = 0xA12 // 2578
	SYS___CPOWL_B                       = 0xA13 // 2579
	SYS___CPOWL_H                       = 0xA14 // 2580
	SYS_CPROJ                           = 0xA15 // 2581
	SYS___CPROJ_B                       = 0xA16 // 2582
	SYS___CPROJ_H                       = 0xA17 // 2583
	SYS_CPROJF                          = 0xA18 // 2584
	SYS___CPROJF_B                      = 0xA19 // 2585
	SYS___CPROJF_H                      = 0xA1A // 2586
	SYS_CPROJL                          = 0xA1B // 2587
	SYS___CPROJL_B                      = 0xA1C // 2588
	SYS___CPROJL_H                      = 0xA1D // 2589
	SYS_CREAL                           = 0xA1E // 2590
	SYS___CREAL_B                       = 0xA1F // 2591
	SYS___CREAL_H                       = 0xA20 // 2592
	SYS_CREALF                          = 0xA21 // 2593
	SYS___CREALF_B                      = 0xA22 // 2594
	SYS___CREALF_H                      = 0xA23 // 2595
	SYS_CREALL                          = 0xA24 // 2596
	SYS___CREALL_B                      = 0xA25 // 2597
	SYS___CREALL_H                      = 0xA26 // 2598
	SYS_CSIN                            = 0xA27 // 2599
	SYS___CSIN_B                        = 0xA28 // 2600
	SYS___CSIN_H                        = 0xA29 // 2601
	SYS_CSINF                           = 0xA2A // 2602
	SYS___CSINF_B                       = 0xA2B // 2603
	SYS___CSINF_H                       = 0xA2C // 2604
	SYS_CSINL                           = 0xA2D // 2605
	SYS___CSINL_B                       = 0xA2E // 2606
	SYS___CSINL_H                       = 0xA2F // 2607
	SYS_CSINH                           = 0xA30 // 2608
	SYS___CSINH_B                       = 0xA31 // 2609
	SYS___CSINH_H                       = 0xA32 // 2610
	SYS_CSINHF                          = 0xA33 // 2611
	SYS___CSINHF_B                      = 0xA34 // 2612
	SYS___CSINHF_H                      = 0xA35 // 2613
	SYS_CSINHL                          = 0xA36 // 2614
	SYS___CSINHL_B                      = 0xA37 // 2615
	SYS___CSINHL_H                      = 0xA38 // 2616
	SYS_CSQRT                           = 0xA39 // 2617
	SYS___CSQRT_B                       = 0xA3A // 2618
	SYS___CSQRT_H                       = 0xA3B // 2619
	SYS_CSQRTF                          = 0xA3C // 2620
	SYS___CSQRTF_B                      = 0xA3D // 2621
	SYS___CSQRTF_H                      = 0xA3E // 2622
	SYS_CSQRTL                          = 0xA3F // 2623
	SYS___CSQRTL_B                      = 0xA40 // 2624
	SYS___CSQRTL_H                      = 0xA41 // 2625
	SYS_CTAN                            = 0xA42 // 2626
	SYS___CTAN_B                        = 0xA43 // 2627
	SYS___CTAN_H                        = 0xA44 // 2628
	SYS_CTANF                           = 0xA45 // 2629
	SYS___CTANF_B                       = 0xA46 // 2630
	SYS___CTANF_H                       = 0xA47 // 2631
	SYS_CTANL                           = 0xA48 // 2632
	SYS___CTANL_B                       = 0xA49 // 2633
	SYS___CTANL_H                       = 0xA4A // 2634
	SYS_CTANH                           = 0xA4B // 2635
	SYS___CTANH_B                       = 0xA4C // 2636
	SYS___CTANH_H                       = 0xA4D // 2637
	SYS_CTANHF                          = 0xA4E // 2638
	SYS___CTANHF_B                      = 0xA4F // 2639
	SYS___CTANHF_H                      = 0xA50 // 2640
	SYS_CTANHL                          = 0xA51 // 2641
	SYS___CTANHL_B                      = 0xA52 // 2642
	SYS___CTANHL_H                      = 0xA53 // 2643
	SYS___ACOSHF_H                      = 0xA54 // 2644
	SYS___ACOSHL_H                      = 0xA55 // 2645
	SYS___ASINHF_H                      = 0xA56 // 2646
	SYS___ASINHL_H                      = 0xA57 // 2647
	SYS___CBRTF_H                       = 0xA58 // 2648
	SYS___CBRTL_H                       = 0xA59 // 2649
	SYS___COPYSIGN_B                    = 0xA5A // 2650
	SYS___EXPM1F_H                      = 0xA5B // 2651
	SYS___EXPM1L_H                      = 0xA5C // 2652
	SYS___EXP2_H                        = 0xA5D // 2653
	SYS___EXP2F_H                       = 0xA5E // 2654
	SYS___EXP2L_H                       = 0xA5F // 2655
	SYS___LOG1PF_H                      = 0xA60 // 2656
	SYS___LOG1PL_H                      = 0xA61 // 2657
	SYS___LGAMMAL_H                     = 0xA62 // 2658
	SYS_FMA                             = 0xA63 // 2659
	SYS___FMA_B                         = 0xA64 // 2660
	SYS___FMA_H                         = 0xA65 // 2661
	SYS_FMAF                            = 0xA66 // 2662
	SYS___FMAF_B                        = 0xA67 // 2663
	SYS___FMAF_H                        = 0xA68 // 2664
	SYS_FMAL                            = 0xA69 // 2665
	SYS___FMAL_B                        = 0xA6A // 2666
	SYS___FMAL_H                        = 0xA6B // 2667
	SYS_FMAX                            = 0xA6C // 2668
	SYS___FMAX_B                        = 0xA6D // 2669
	SYS___FMAX_H                        = 0xA6E // 2670
	SYS_FMAXF                           = 0xA6F // 2671
	SYS___FMAXF_B                       = 0xA70 // 2672
	SYS___FMAXF_H                       = 0xA71 // 2673
	SYS_FMAXL                           = 0xA72 // 2674
	SYS___FMAXL_B                       = 0xA73 // 2675
	SYS___FMAXL_H                       = 0xA74 // 2676
	SYS_FMIN                            = 0xA75 // 2677
	SYS___FMIN_B                        = 0xA76 // 2678
	SYS___FMIN_H                        = 0xA77 // 2679
	SYS_FMINF                           = 0xA78 // 2680
	SYS___FMINF_B                       = 0xA79 // 2681
	SYS___FMINF_H                       = 0xA7A // 2682
	SYS_FMINL                           = 0xA7B // 2683
	SYS___FMINL_B                       = 0xA7C // 2684
	SYS___FMINL_H                       = 0xA7D // 2685
	SYS_ILOGBF                          = 0xA7E // 2686
	SYS___ILOGBF_B                      = 0xA7F // 2687
	SYS___ILOGBF_H                      = 0xA80 // 2688
	SYS_ILOGBL                          = 0xA81 // 2689
	SYS___ILOGBL_B                      = 0xA82 // 2690
	SYS___ILOGBL_H                      = 0xA83 // 2691
	SYS_LLRINT                          = 0xA84 // 2692
	SYS___LLRINT_B                      = 0xA85 // 2693
	SYS___LLRINT_H                      = 0xA86 // 2694
	SYS_LLRINTF                         = 0xA87 // 2695
	SYS___LLRINTF_B                     = 0xA88 // 2696
	SYS___LLRINTF_H                     = 0xA89 // 2697
	SYS_LLRINTL                         = 0xA8A // 2698
	SYS___LLRINTL_B                     = 0xA8B // 2699
	SYS___LLRINTL_H                     = 0xA8C // 2700
	SYS_LLROUND                         = 0xA8D // 2701
	SYS___LLROUND_B                     = 0xA8E // 2702
	SYS___LLROUND_H                     = 0xA8F // 2703
	SYS_LLROUNDF                        = 0xA90 // 2704
	SYS___LLROUNDF_B                    = 0xA91 // 2705
	SYS___LLROUNDF_H                    = 0xA92 // 2706
	SYS_LLROUNDL                        = 0xA93 // 2707
	SYS___LLROUNDL_B                    = 0xA94 // 2708
	SYS___LLROUNDL_H                    = 0xA95 // 2709
	SYS_LOGBF                           = 0xA96 // 2710
	SYS___LOGBF_B                       = 0xA97 // 2711
	SYS___LOGBF_H                       = 0xA98 // 2712
	SYS_LOGBL                           = 0xA99 // 2713
	SYS___LOGBL_B                       = 0xA9A // 2714
	SYS___LOGBL_H                       = 0xA9B // 2715
	SYS_LRINT                           = 0xA9C // 2716
	SYS___LRINT_B                       = 0xA9D // 2717
	SYS___LRINT_H                       = 0xA9E // 2718
	SYS_LRINTF                          = 0xA9F // 2719
	SYS___LRINTF_B                      = 0xAA0 // 2720
	SYS___LRINTF_H                      = 0xAA1 // 2721
	SYS_LRINTL                          = 0xAA2 // 2722
	SYS___LRINTL_B                      = 0xAA3 // 2723
	SYS___LRINTL_H                      = 0xAA4 // 2724
	SYS_LROUNDL                         = 0xAA5 // 2725
	SYS___LROUNDL_B                     = 0xAA6 // 2726
	SYS___LROUNDL_H                     = 0xAA7 // 2727
	SYS_NAN                             = 0xAA8 // 2728
	SYS___NAN_B                         = 0xAA9 // 2729
	SYS_NANF                            = 0xAAA // 2730
	SYS___NANF_B                        = 0xAAB // 2731
	SYS_NANL                            = 0xAAC // 2732
	SYS___NANL_B                        = 0xAAD // 2733
	SYS_NEARBYINT                       = 0xAAE // 2734
	SYS___NEARBYINT_B                   = 0xAAF // 2735
	SYS___NEARBYINT_H                   = 0xAB0 // 2736
	SYS_NEARBYINTF                      = 0xAB1 // 2737
	SYS___NEARBYINTF_B                  = 0xAB2 // 2738
	SYS___NEARBYINTF_H                  = 0xAB3 // 2739
	SYS_NEARBYINTL                      = 0xAB4 // 2740
	SYS___NEARBYINTL_B                  = 0xAB5 // 2741
	SYS___NEARBYINTL_H                  = 0xAB6 // 2742
	SYS_NEXTAFTERF                      = 0xAB7 // 2743
	SYS___NEXTAFTERF_B                  = 0xAB8 // 2744
	SYS___NEXTAFTERF_H                  = 0xAB9 // 2745
	SYS_NEXTAFTERL                      = 0xABA // 2746
	SYS___NEXTAFTERL_B                  = 0xABB // 2747
	SYS___NEXTAFTERL_H                  = 0xABC // 2748
	SYS_NEXTTOWARD                      = 0xABD // 2749
	SYS___NEXTTOWARD_B                  = 0xABE // 2750
	SYS___NEXTTOWARD_H                  = 0xABF // 2751
	SYS_NEXTTOWARDF                     = 0xAC0 // 2752
	SYS___NEXTTOWARDF_B                 = 0xAC1 // 2753
	SYS___NEXTTOWARDF_H                 = 0xAC2 // 2754
	SYS_NEXTTOWARDL                     = 0xAC3 // 2755
	SYS___NEXTTOWARDL_B                 = 0xAC4 // 2756
	SYS___NEXTTOWARDL_H                 = 0xAC5 // 2757
	SYS___REMAINDERF_H                  = 0xAC6 // 2758
	SYS___REMAINDERL_H                  = 0xAC7 // 2759
	SYS___REMQUO_H                      = 0xAC8 // 2760
	SYS___REMQUOF_H                     = 0xAC9 // 2761
	SYS___REMQUOL_H                     = 0xACA // 2762
	SYS_RINTF                           = 0xACB // 2763
	SYS___RINTF_B                       = 0xACC // 2764
	SYS_RINTL                           = 0xACD // 2765
	SYS___RINTL_B                       = 0xACE // 2766
	SYS_ROUND                           = 0xACF // 2767
	SYS___ROUND_B                       = 0xAD0 // 2768
	SYS___ROUND_H                       = 0xAD1 // 2769
	SYS_ROUNDF                          = 0xAD2 // 2770
	SYS___ROUNDF_B                      = 0xAD3 // 2771
	SYS___ROUNDF_H                      = 0xAD4 // 2772
	SYS_ROUNDL                          = 0xAD5 // 2773
	SYS___ROUNDL_B                      = 0xAD6 // 2774
	SYS___ROUNDL_H                      = 0xAD7 // 2775
	SYS_SCALBLN                         = 0xAD8 // 2776
	SYS___SCALBLN_B                     = 0xAD9 // 2777
	SYS___SCALBLN_H                     = 0xADA // 2778
	SYS_SCALBLNF                        = 0xADB // 2779
	SYS___SCALBLNF_B                    = 0xADC // 2780
	SYS___SCALBLNF_H                    = 0xADD // 2781
	SYS_SCALBLNL                        = 0xADE // 2782
	SYS___SCALBLNL_B                    = 0xADF // 2783
	SYS___SCALBLNL_H                    = 0xAE0 // 2784
	SYS___SCALBN_B                      = 0xAE1 // 2785
	SYS___SCALBN_H                      = 0xAE2 // 2786
	SYS_SCALBNF                         = 0xAE3 // 2787
	SYS___SCALBNF_B                     = 0xAE4 // 2788
	SYS___SCALBNF_H                     = 0xAE5 // 2789
	SYS_SCALBNL                         = 0xAE6 // 2790
	SYS___SCALBNL_B                     = 0xAE7 // 2791
	SYS___SCALBNL_H                     = 0xAE8 // 2792
	SYS___TGAMMAL_H                     = 0xAE9 // 2793
	SYS_FECLEAREXCEPT                   = 0xAEA // 2794
	SYS_FEGETENV                        = 0xAEB // 2795
	SYS_FEGETEXCEPTFLAG                 = 0xAEC // 2796
	SYS_FEGETROUND                      = 0xAED // 2797
	SYS_FEHOLDEXCEPT                    = 0xAEE // 2798
	SYS_FERAISEEXCEPT                   = 0xAEF // 2799
	SYS_FESETENV                        = 0xAF0 // 2800
	SYS_FESETEXCEPTFLAG                 = 0xAF1 // 2801
	SYS_FESETROUND                      = 0xAF2 // 2802
	SYS_FETESTEXCEPT                    = 0xAF3 // 2803
	SYS_FEUPDATEENV                     = 0xAF4 // 2804
	SYS___COPYSIGN_H                    = 0xAF5 // 2805
	SYS___HYPOTF_H                      = 0xAF6 // 2806
	SYS___HYPOTL_H                      = 0xAF7 // 2807
	SYS___CLASS                         = 0xAFA // 2810
	SYS___CLASS_B                       = 0xAFB // 2811
	SYS___CLASS_H                       = 0xAFC // 2812
	SYS___ISBLANK_A                     = 0xB2E // 2862
	SYS___ISWBLANK_A                    = 0xB2F // 2863
	SYS___LROUND_FIXUP                  = 0xB30 // 2864
	SYS___LROUNDF_FIXUP                 = 0xB31 // 2865
	SYS_SCHED_YIELD                     = 0xB32 // 2866
	SYS_STRERROR_R                      = 0xB33 // 2867
	SYS_UNSETENV                        = 0xB34 // 2868
	SYS___LGAMMA_H_C99                  = 0xB38 // 2872
	SYS___LGAMMA_B_C99                  = 0xB39 // 2873
	SYS___LGAMMA_R_C99                  = 0xB3A // 2874
	SYS___FTELL2                        = 0xB3B // 2875
	SYS___FSEEK2                        = 0xB3C // 2876
	SYS___STATIC_REINIT                 = 0xB3D // 2877
	SYS_PTHREAD_ATTR_GETSTACK           = 0xB3E // 2878
	SYS_PTHREAD_ATTR_SETSTACK           = 0xB3F // 2879
	SYS___TGAMMA_H_C99                  = 0xB78 // 2936
	SYS___TGAMMAF_H_C99                 = 0xB79 // 2937
	SYS___LE_TRACEBACK                  = 0xB7A // 2938
	SYS___MUST_STAY_CLEAN               = 0xB7C // 2940
	SYS___O_ENV                         = 0xB7D // 2941
	SYS_ACOSD32                         = 0xB7E // 2942
	SYS_ACOSD64                         = 0xB7F // 2943
	SYS_ACOSD128                        = 0xB80 // 2944
	SYS_ACOSHD32                        = 0xB81 // 2945
	SYS_ACOSHD64                        = 0xB82 // 2946
	SYS_ACOSHD128                       = 0xB83 // 2947
	SYS_ASIND32                         = 0xB84 // 2948
	SYS_ASIND64                         = 0xB85 // 2949
	SYS_ASIND128                        = 0xB86 // 2950
	SYS_ASINHD32                        = 0xB87 // 2951
	SYS_ASINHD64                        = 0xB88 // 2952
	SYS_ASINHD128                       = 0xB89 // 2953
	SYS_ATAND32                         = 0xB8A // 2954
	SYS_ATAND64                         = 0xB8B // 2955
	SYS_ATAND128                        = 0xB8C // 2956
	SYS_ATAN2D32                        = 0xB8D // 2957
	SYS_ATAN2D64                        = 0xB8E // 2958
	SYS_ATAN2D128                       = 0xB8F // 2959
	SYS_ATANHD32                        = 0xB90 // 2960
	SYS_ATANHD64                        = 0xB91 // 2961
	SYS_ATANHD128                       = 0xB92 // 2962
	SYS_CBRTD32                         = 0xB93 // 2963
	SYS_CBRTD64                         = 0xB94 // 2964
	SYS_CBRTD128                        = 0xB95 // 2965
	SYS_CEILD32                         = 0xB96 // 2966
	SYS_CEILD64                         = 0xB97 // 2967
	SYS_CEILD128                        = 0xB98 // 2968
	SYS___CLASS2                        = 0xB99 // 2969
	SYS___CLASS2_B                      = 0xB9A // 2970
	SYS___CLASS2_H                      = 0xB9B // 2971
	SYS_COPYSIGND32                     = 0xB9C // 2972
	SYS_COPYSIGND64                     = 0xB9D // 2973
	SYS_COPYSIGND128                    = 0xB9E // 2974
	SYS_COSD32                          = 0xB9F // 2975
	SYS_COSD64                          = 0xBA0 // 2976
	SYS_COSD128                         = 0xBA1 // 2977
	SYS_COSHD32                         = 0xBA2 // 2978
	SYS_COSHD64                         = 0xBA3 // 2979
	SYS_COSHD128                        = 0xBA4 // 2980
	SYS_ERFD32                          = 0xBA5 // 2981
	SYS_ERFD64                          = 0xBA6 // 2982
	SYS_ERFD128                         = 0xBA7 // 2983
	SYS_ERFCD32                         = 0xBA8 // 2984
	SYS_ERFCD64                         = 0xBA9 // 2985
	SYS_ERFCD128                        = 0xBAA // 2986
	SYS_EXPD32                          = 0xBAB // 2987
	SYS_EXPD64                          = 0xBAC // 2988
	SYS_EXPD128                         = 0xBAD // 2989
	SYS_EXP2D32                         = 0xBAE // 2990
	SYS_EXP2D64                         = 0xBAF // 2991
	SYS_EXP2D128                        = 0xBB0 // 2992
	SYS_EXPM1D32                        = 0xBB1 // 2993
	SYS_EXPM1D64                        = 0xBB2 // 2994
	SYS_EXPM1D128                       = 0xBB3 // 2995
	SYS_FABSD32                         = 0xBB4 // 2996
	SYS_FABSD64                         = 0xBB5 // 2997
	SYS_FABSD128                        = 0xBB6 // 2998
	SYS_FDIMD32                         = 0xBB7 // 2999
	SYS_FDIMD64                         = 0xBB8 // 3000
	SYS_FDIMD128                        = 0xBB9 // 3001
	SYS_FE_DEC_GETROUND                 = 0xBBA // 3002
	SYS_FE_DEC_SETROUND                 = 0xBBB // 3003
	SYS_FLOORD32                        = 0xBBC // 3004
	SYS_FLOORD64                        = 0xBBD // 3005
	SYS_FLOORD128                       = 0xBBE // 3006
	SYS_FMAD32                          = 0xBBF // 3007
	SYS_FMAD64                          = 0xBC0 // 3008
	SYS_FMAD128                         = 0xBC1 // 3009
	SYS_FMAXD32                         = 0xBC2 // 3010
	SYS_FMAXD64                         = 0xBC3 // 3011
	SYS_FMAXD128                        = 0xBC4 // 3012
	SYS_FMIND32                         = 0xBC5 // 3013
	SYS_FMIND64                         = 0xBC6 // 3014
	SYS_FMIND128                        = 0xBC7 // 3015
	SYS_FMODD32                         = 0xBC8 // 3016
	SYS_FMODD64                         = 0xBC9 // 3017
	SYS_FMODD128                        = 0xBCA // 3018
	SYS___FP_CAST_D                     = 0xBCB // 3019
	SYS_FREXPD32                        = 0xBCC // 3020
	SYS_FREXPD64                        = 0xBCD // 3021
	SYS_FREXPD128                       = 0xBCE // 3022
	SYS_HYPOTD32                        = 0xBCF // 3023
	SYS_HYPOTD64                        = 0xBD0 // 3024
	SYS_HYPOTD128                       = 0xBD1 // 3025
	SYS_ILOGBD32                        = 0xBD2 // 3026
	SYS_ILOGBD64                        = 0xBD3 // 3027
	SYS_ILOGBD128                       = 0xBD4 // 3028
	SYS_LDEXPD32                        = 0xBD5 // 3029
	SYS_LDEXPD64                        = 0xBD6 // 3030
	SYS_LDEXPD128                       = 0xBD7 // 3031
	SYS_LGAMMAD32                       = 0xBD8 // 3032
	SYS_LGAMMAD64                       = 0xBD9 // 3033
	SYS_LGAMMAD128                      = 0xBDA // 3034
	SYS_LLRINTD32                       = 0xBDB // 3035
	SYS_LLRINTD64                       = 0xBDC // 3036
	SYS_LLRINTD128                      = 0xBDD // 3037
	SYS_LLROUNDD32                      = 0xBDE // 3038
	SYS_LLROUNDD64                      = 0xBDF // 3039
	SYS_LLROUNDD128                     = 0xBE0 // 3040
	SYS_LOGD32                          = 0xBE1 // 3041
	SYS_LOGD64                          = 0xBE2 // 3042
	SYS_LOGD128                         = 0xBE3 // 3043
	SYS_LOG10D32                        = 0xBE4 // 3044
	SYS_LOG10D64                        = 0xBE5 // 3045
	SYS_LOG10D128                       = 0xBE6 // 3046
	SYS_LOG1PD32                        = 0xBE7 // 3047
	SYS_LOG1PD64                        = 0xBE8 // 3048
	SYS_LOG1PD128                       = 0xBE9 // 3049
	SYS_LOG2D32                         = 0xBEA // 3050
	SYS_LOG2D64                         = 0xBEB // 3051
	SYS_LOG2D128                        = 0xBEC // 3052
	SYS_LOGBD32                         = 0xBED // 3053
	SYS_LOGBD64                         = 0xBEE // 3054
	SYS_LOGBD128                        = 0xBEF // 3055
	SYS_LRINTD32                        = 0xBF0 // 3056
	SYS_LRINTD64                        = 0xBF1 // 3057
	SYS_LRINTD128                       = 0xBF2 // 3058
	SYS_LROUNDD32                       = 0xBF3 // 3059
	SYS_LROUNDD64                       = 0xBF4 // 3060
	SYS_LROUNDD128                      = 0xBF5 // 3061
	SYS_MODFD32                         = 0xBF6 // 3062
	SYS_MODFD64                         = 0xBF7 // 3063
	SYS_MODFD128                        = 0xBF8 // 3064
	SYS_NAND32                          = 0xBF9 // 3065
	SYS_NAND64                          = 0xBFA // 3066
	SYS_NAND128                         = 0xBFB // 3067
	SYS_NEARBYINTD32                    = 0xBFC // 3068
	SYS_NEARBYINTD64                    = 0xBFD // 3069
	SYS_NEARBYINTD128                   = 0xBFE // 3070
	SYS_NEXTAFTERD32                    = 0xBFF // 3071
	SYS_NEXTAFTERD64                    = 0xC00 // 3072
	SYS_NEXTAFTERD128                   = 0xC01 // 3073
	SYS_NEXTTOWARDD32                   = 0xC02 // 3074
	SYS_NEXTTOWARDD64                   = 0xC03 // 3075
	SYS_NEXTTOWARDD128                  = 0xC04 // 3076
	SYS_POWD32                          = 0xC05 // 3077
	SYS_POWD64                          = 0xC06 // 3078
	SYS_POWD128                         = 0xC07 // 3079
	SYS_QUANTIZED32                     = 0xC08 // 3080
	SYS_QUANTIZED64                     = 0xC09 // 3081
	SYS_QUANTIZED128                    = 0xC0A // 3082
	SYS_REMAINDERD32                    = 0xC0B // 3083
	SYS_REMAINDERD64                    = 0xC0C // 3084
	SYS_REMAINDERD128                   = 0xC0D // 3085
	SYS___REMQUOD32                     = 0xC0E // 3086
	SYS___REMQUOD64                     = 0xC0F // 3087
	SYS___REMQUOD128                    = 0xC10 // 3088
	SYS_RINTD32                         = 0xC11 // 3089
	SYS_RINTD64                         = 0xC12 // 3090
	SYS_RINTD128                        = 0xC13 // 3091
	SYS_ROUNDD32                        = 0xC14 // 3092
	SYS_ROUNDD64                        = 0xC15 // 3093
	SYS_ROUNDD128                       = 0xC16 // 3094
	SYS_SAMEQUANTUMD32                  = 0xC17 // 3095
	SYS_SAMEQUANTUMD64                  = 0xC18 // 3096
	SYS_SAMEQUANTUMD128                 = 0xC19 // 3097
	SYS_SCALBLND32                      = 0xC1A // 3098
	SYS_SCALBLND64                      = 0xC1B // 3099
	SYS_SCALBLND128                     = 0xC1C // 3100
	SYS_SCALBND32                       = 0xC1D // 3101
	SYS_SCALBND64                       = 0xC1E // 3102
	SYS_SCALBND128                      = 0xC1F // 3103
	SYS_SIND32                          = 0xC20 // 3104
	SYS_SIND64                          = 0xC21 // 3105
	SYS_SIND128                         = 0xC22 // 3106
	SYS_SINHD32                         = 0xC23 // 3107
	SYS_SINHD64                         = 0xC24 // 3108
	SYS_SINHD128                        = 0xC25 // 3109
	SYS_SQRTD32                         = 0xC26 // 3110
	SYS_SQRTD64                         = 0xC27 // 3111
	SYS_SQRTD128                        = 0xC28 // 3112
	SYS_STRTOD32                        = 0xC29 // 3113
	SYS_STRTOD64                        = 0xC2A // 3114
	SYS_STRTOD128                       = 0xC2B // 3115
	SYS_TAND32                          = 0xC2C // 3116
	SYS_TAND64                          = 0xC2D // 3117
	SYS_TAND128                         = 0xC2E // 3118
	SYS_TANHD32                         = 0xC2F // 3119
	SYS_TANHD64                         = 0xC30 // 3120
	SYS_TANHD128                        = 0xC31 // 3121
	SYS_TGAMMAD32                       = 0xC32 // 3122
	SYS_TGAMMAD64                       = 0xC33 // 3123
	SYS_TGAMMAD128                      = 0xC34 // 3124
	SYS_TRUNCD32                        = 0xC3E // 3134
	SYS_TRUNCD64                        = 0xC3F // 3135
	SYS_TRUNCD128                       = 0xC40 // 3136
	SYS_WCSTOD32                        = 0xC41 // 3137
	SYS_WCSTOD64                        = 0xC42 // 3138
	SYS_WCSTOD128                       = 0xC43 // 3139
	SYS___CODEPAGE_INFO                 = 0xC64 // 3172
	SYS_POSIX_OPENPT                    = 0xC66 // 3174
	SYS_PSELECT                         = 0xC67 // 3175
	SYS_SOCKATMARK                      = 0xC68 // 3176
	SYS_AIO_FSYNC                       = 0xC69 // 3177
	SYS_LIO_LISTIO                      = 0xC6A // 3178
	SYS___ATANPID32                     = 0xC6B // 3179
	SYS___ATANPID64                     = 0xC6C // 3180
	SYS___ATANPID128                    = 0xC6D // 3181
	SYS___COSPID32                      = 0xC6E // 3182
	SYS___COSPID64                      = 0xC6F // 3183
	SYS___COSPID128                     = 0xC70 // 3184
	SYS___SINPID32                      = 0xC71 // 3185
	SYS___SINPID64                      = 0xC72 // 3186
	SYS___SINPID128                     = 0xC73 // 3187
	SYS_SETIPV4SOURCEFILTER             = 0xC76 // 3190
	SYS_GETIPV4SOURCEFILTER             = 0xC77 // 3191
	SYS_SETSOURCEFILTER                 = 0xC78 // 3192
	SYS_GETSOURCEFILTER                 = 0xC79 // 3193
	SYS_FWRITE_UNLOCKED                 = 0xC7A // 3194
	SYS_FREAD_UNLOCKED                  = 0xC7B // 3195
	SYS_FGETS_UNLOCKED                  = 0xC7C // 3196
	SYS_GETS_UNLOCKED                   = 0xC7D // 3197
	SYS_FPUTS_UNLOCKED                  = 0xC7E // 3198
	SYS_PUTS_UNLOCKED                   = 0xC7F // 3199
	SYS_FGETC_UNLOCKED                  = 0xC80 // 3200
	SYS_FPUTC_UNLOCKED                  = 0xC81 // 3201
	SYS_DLADDR                          = 0xC82 // 3202
	SYS_SHM_OPEN                        = 0xC8C // 3212
	SYS_SHM_UNLINK                      = 0xC8D // 3213
	SYS___CLASS2F                       = 0xC91 // 3217
	SYS___CLASS2L                       = 0xC92 // 3218
	SYS___CLASS2F_B                     = 0xC93 // 3219
	SYS___CLASS2F_H                     = 0xC94 // 3220
	SYS___CLASS2L_B                     = 0xC95 // 3221
	SYS___CLASS2L_H                     = 0xC96 // 3222
	SYS___CLASS2D32                     = 0xC97 // 3223
	SYS___CLASS2D64                     = 0xC98 // 3224
	SYS___CLASS2D128                    = 0xC99 // 3225
	SYS___TOCSNAME2                     = 0xC9A // 3226
	SYS___D1TOP                         = 0xC9B // 3227
	SYS___D2TOP                         = 0xC9C // 3228
	SYS___D4TOP                         = 0xC9D // 3229
	SYS___PTOD1                         = 0xC9E // 3230
	SYS___PTOD2                         = 0xC9F // 3231
	SYS___PTOD4                         = 0xCA0 // 3232
	SYS_CLEARERR_UNLOCKED               = 0xCA1 // 3233
	SYS_FDELREC_UNLOCKED                = 0xCA2 // 3234
	SYS_FEOF_UNLOCKED                   = 0xCA3 // 3235
	SYS_FERROR_UNLOCKED                 = 0xCA4 // 3236
	SYS_FFLUSH_UNLOCKED                 = 0xCA5 // 3237
	SYS_FGETPOS_UNLOCKED                = 0xCA6 // 3238
	SYS_FGETWC_UNLOCKED                 = 0xCA7 // 3239
	SYS_FGETWS_UNLOCKED                 = 0xCA8 // 3240
	SYS_FILENO_UNLOCKED                 = 0xCA9 // 3241
	SYS_FLDATA_UNLOCKED                 = 0xCAA // 3242
	SYS_FLOCATE_UNLOCKED                = 0xCAB // 3243
	SYS_FPRINTF_UNLOCKED                = 0xCAC // 3244
	SYS_FPUTWC_UNLOCKED                 = 0xCAD // 3245
	SYS_FPUTWS_UNLOCKED                 = 0xCAE // 3246
	SYS_FSCANF_UNLOCKED                 = 0xCAF // 3247
	SYS_FSEEK_UNLOCKED                  = 0xCB0 // 3248
	SYS_FSEEKO_UNLOCKED                 = 0xCB1 // 3249
	SYS_FSETPOS_UNLOCKED                = 0xCB3 // 3251
	SYS_FTELL_UNLOCKED                  = 0xCB4 // 3252
	SYS_FTELLO_UNLOCKED                 = 0xCB5 // 3253
	SYS_FUPDATE_UNLOCKED                = 0xCB7 // 3255
	SYS_FWIDE_UNLOCKED                  = 0xCB8 // 3256
	SYS_FWPRINTF_UNLOCKED               = 0xCB9 // 3257
	SYS_FWSCANF_UNLOCKED                = 0xCBA // 3258
	SYS_GETWC_UNLOCKED                  = 0xCBB // 3259
	SYS_GETWCHAR_UNLOCKED               = 0xCBC // 3260
	SYS_PERROR_UNLOCKED                 = 0xCBD // 3261
	SYS_PRINTF_UNLOCKED                 = 0xCBE // 3262
	SYS_PUTWC_UNLOCKED                  = 0xCBF // 3263
	SYS_PUTWCHAR_UNLOCKED               = 0xCC0 // 3264
	SYS_REWIND_UNLOCKED                 = 0xCC1 // 3265
	SYS_SCANF_UNLOCKED                  = 0xCC2 // 3266
	SYS_UNGETC_UNLOCKED                 = 0xCC3 // 3267
	SYS_UNGETWC_UNLOCKED                = 0xCC4 // 3268
	SYS_VFPRINTF_UNLOCKED               = 0xCC5 // 3269
	SYS_VFSCANF_UNLOCKED                = 0xCC7 // 3271
	SYS_VFWPRINTF_UNLOCKED              = 0xCC9 // 3273
	SYS_VFWSCANF_UNLOCKED               = 0xCCB // 3275
	SYS_VPRINTF_UNLOCKED                = 0xCCD // 3277
	SYS_VSCANF_UNLOCKED                 = 0xCCF // 3279
	SYS_VWPRINTF_UNLOCKED               = 0xCD1 // 3281
	SYS_VWSCANF_UNLOCKED                = 0xCD3 // 3283
	SYS_WPRINTF_UNLOCKED                = 0xCD5 // 3285
	SYS_WSCANF_UNLOCKED                 = 0xCD6 // 3286
	SYS_ASCTIME64                       = 0xCD7 // 3287
	SYS_ASCTIME64_R                     = 0xCD8 // 3288
	SYS_CTIME64                         = 0xCD9 // 3289
	SYS_CTIME64_R                       = 0xCDA // 3290
	SYS_DIFFTIME64                      = 0xCDB // 3291
	SYS_GMTIME64                        = 0xCDC // 3292
	SYS_GMTIME64_R                      = 0xCDD // 3293
	SYS_LOCALTIME64                     = 0xCDE // 3294
	SYS_LOCALTIME64_R                   = 0xCDF // 3295
	SYS_MKTIME64                        = 0xCE0 // 3296
	SYS_TIME64                          = 0xCE1 // 3297
	SYS___LOGIN_APPLID                  = 0xCE2 // 3298
	SYS___PASSWD_APPLID                 = 0xCE3 // 3299
	SYS_PTHREAD_SECURITY_APPLID_NP      = 0xCE4 // 3300
	SYS___GETTHENT                      = 0xCE5 // 3301
	SYS_FREEIFADDRS                     = 0xCE6 // 3302
	SYS_GETIFADDRS                      = 0xCE7 // 3303
	SYS_POSIX_FALLOCATE                 = 0xCE8 // 3304
	SYS_POSIX_MEMALIGN                  = 0xCE9 // 3305
	SYS_SIZEOF_ALLOC                    = 0xCEA // 3306
	SYS_RESIZE_ALLOC                    = 0xCEB // 3307
	SYS_FREAD_NOUPDATE                  = 0xCEC // 3308
	SYS_FREAD_NOUPDATE_UNLOCKED         = 0xCED // 3309
	SYS_FGETPOS64                       = 0xCEE // 3310
	SYS_FSEEK64                         = 0xCEF // 3311
	SYS_FSEEKO64                        = 0xCF0 // 3312
	SYS_FSETPOS64                       = 0xCF1 // 3313
	SYS_FTELL64                         = 0xCF2 // 3314
	SYS_FTELLO64                        = 0xCF3 // 3315
	SYS_FGETPOS64_UNLOCKED              = 0xCF4 // 3316
	SYS_FSEEK64_UNLOCKED                = 0xCF5 // 3317
	SYS_FSEEKO64_UNLOCKED               = 0xCF6 // 3318
	SYS_FSETPOS64_UNLOCKED              = 0xCF7 // 3319
	SYS_FTELL64_UNLOCKED                = 0xCF8 // 3320
	SYS_FTELLO64_UNLOCKED               = 0xCF9 // 3321
	SYS_FOPEN_UNLOCKED                  = 0xCFA // 3322
	SYS_FREOPEN_UNLOCKED                = 0xCFB // 3323
	SYS_FDOPEN_UNLOCKED                 = 0xCFC // 3324
	SYS_TMPFILE_UNLOCKED                = 0xCFD // 3325
	SYS___MOSERVICES                    = 0xD3D // 3389
	SYS___GETTOD                        = 0xD3E // 3390
	SYS_C16RTOMB                        = 0xD40 // 3392
	SYS_C32RTOMB                        = 0xD41 // 3393
	SYS_MBRTOC16                        = 0xD42 // 3394
	SYS_MBRTOC32                        = 0xD43 // 3395
	SYS_QUANTEXPD32                     = 0xD44 // 3396
	SYS_QUANTEXPD64                     = 0xD45 // 3397
	SYS_QUANTEXPD128                    = 0xD46 // 3398
	SYS___LOCALE_CTL                    = 0xD47 // 3399
	SYS___SMF_RECORD2                   = 0xD48 // 3400
	SYS_FOPEN64                         = 0xD49 // 3401
	SYS_FOPEN64_UNLOCKED                = 0xD4A // 3402
	SYS_FREOPEN64                       = 0xD4B // 3403
	SYS_FREOPEN64_UNLOCKED              = 0xD4C // 3404
	SYS_TMPFILE64                       = 0xD4D // 3405
	SYS_TMPFILE64_UNLOCKED              = 0xD4E // 3406
	SYS_GETDATE64                       = 0xD4F // 3407
	SYS_GETTIMEOFDAY64                  = 0xD50 // 3408
	SYS_BIND2ADDRSEL                    = 0xD59 // 3417
	SYS_INET6_IS_SRCADDR                = 0xD5A // 3418
	SYS___GETGRGID1                     = 0xD5B // 3419
	SYS___GETGRNAM1                     = 0xD5C // 3420
	SYS___FBUFSIZE                      = 0xD60 // 3424
	SYS___FPENDING                      = 0xD61 // 3425
	SYS___FLBF                          = 0xD62 // 3426
	SYS___FREADABLE                     = 0xD63 // 3427
	SYS___FWRITABLE                     = 0xD64 // 3428
	SYS___FREADING                      = 0xD65 // 3429
	SYS___FWRITING                      = 0xD66 // 3430
	SYS___FSETLOCKING                   = 0xD67 // 3431
	SYS__FLUSHLBF                       = 0xD68 // 3432
	SYS___FPURGE                        = 0xD69 // 3433
	SYS___FREADAHEAD                    = 0xD6A // 3434
	SYS___FSETERR                       = 0xD6B // 3435
	SYS___FPENDING_UNLOCKED             = 0xD6C // 3436
	SYS___FREADING_UNLOCKED             = 0xD6D // 3437
	SYS___FWRITING_UNLOCKED             = 0xD6E // 3438
	SYS__FLUSHLBF_UNLOCKED              = 0xD6F // 3439
	SYS___FPURGE_UNLOCKED               = 0xD70 // 3440
	SYS___FREADAHEAD_UNLOCKED           = 0xD71 // 3441
	SYS___LE_CEEGTJS                    = 0xD72 // 3442
	SYS___LE_RECORD_DUMP                = 0xD73 // 3443
	SYS_FSTAT64                         = 0xD74 // 3444
	SYS_LSTAT64                         = 0xD75 // 3445
	SYS_STAT64                          = 0xD76 // 3446
	SYS___READDIR2_64                   = 0xD77 // 3447
	SYS___OPEN_STAT64                   = 0xD78 // 3448
	SYS_FTW64                           = 0xD79 // 3449
	SYS_NFTW64                          = 0xD7A // 3450
	SYS_UTIME64                         = 0xD7B // 3451
	SYS_UTIMES64                        = 0xD7C // 3452
	SYS___GETIPC64                      = 0xD7D // 3453
	SYS_MSGCTL64                        = 0xD7E // 3454
	SYS_SEMCTL64                        = 0xD7F // 3455
	SYS_SHMCTL64                        = 0xD80 // 3456
	SYS_MSGXRCV64                       = 0xD81 // 3457
	SYS___MGXR64                        = 0xD81 // 3457
	SYS_W_GETPSENT64                    = 0xD82 // 3458
	SYS_PTHREAD_COND_TIMEDWAIT64        = 0xD83 // 3459
	SYS_FTIME64                         = 0xD85 // 3461
	SYS_GETUTXENT64                     = 0xD86 // 3462
	SYS_GETUTXID64                      = 0xD87 // 3463
	SYS_GETUTXLINE64                    = 0xD88 // 3464
	SYS_PUTUTXLINE64                    = 0xD89 // 3465
	SYS_NEWLOCALE                       = 0xD8A // 3466
	SYS_FREELOCALE                      = 0xD8B // 3467
	SYS_USELOCALE                       = 0xD8C // 3468
	SYS_DUPLOCALE                       = 0xD8D // 3469
	SYS___CHATTR64                      = 0xD9C // 3484
	SYS___LCHATTR64                     = 0xD9D // 3485
	SYS___FCHATTR64                     = 0xD9E // 3486
	SYS_____CHATTR64_A                  = 0xD9F // 3487
	SYS_____LCHATTR64_A                 = 0xDA0 // 3488
	SYS___LE_CEEUSGD                    = 0xDA1 // 3489
	SYS___LE_IFAM_CON                   = 0xDA2 // 3490
	SYS___LE_IFAM_DSC                   = 0xDA3 // 3491
	SYS___LE_IFAM_GET                   = 0xDA4 // 3492
	SYS___LE_IFAM_QRY                   = 0xDA5 // 3493
	SYS_ALIGNED_ALLOC                   = 0xDA6 // 3494
	SYS_ACCEPT4                         = 0xDA7 // 3495
	SYS___ACCEPT4_A                     = 0xDA8 // 3496
	SYS_COPYFILERANGE                   = 0xDA9 // 3497
	SYS_GETLINE                         = 0xDAA // 3498
	SYS___GETLINE_A                     = 0xDAB // 3499
	SYS_DIRFD                           = 0xDAC // 3500
	SYS_CLOCK_GETTIME                   = 0xDAD // 3501
	SYS_DUP3                            = 0xDAE // 3502
	SYS_EPOLL_CREATE                    = 0xDAF // 3503
	SYS_EPOLL_CREATE1                   = 0xDB0 // 3504
	SYS_EPOLL_CTL                       = 0xDB1 // 3505
	SYS_EPOLL_WAIT                      = 0xDB2 // 3506
	SYS_EPOLL_PWAIT                     = 0xDB3 // 3507
	SYS_EVENTFD                         = 0xDB4 // 3508
	SYS_STATFS                          = 0xDB5 // 3509
	SYS___STATFS_A                      = 0xDB6 // 3510
	SYS_FSTATFS                         = 0xDB7 // 3511
	SYS_INOTIFY_INIT                    = 0xDB8 // 3512
	SYS_INOTIFY_INIT1                   = 0xDB9 // 3513
	SYS_INOTIFY_ADD_WATCH               = 0xDBA // 3514
	SYS___INOTIFY_ADD_WATCH_A           = 0xDBB // 3515
	SYS_INOTIFY_RM_WATCH                = 0xDBC // 3516
	SYS_PIPE2                           = 0xDBD // 3517
	SYS_PIVOT_ROOT                      = 0xDBE // 3518
	SYS___PIVOT_ROOT_A                  = 0xDBF // 3519
	SYS_PRCTL                           = 0xDC0 // 3520
	SYS_PRLIMIT                         = 0xDC1 // 3521
	SYS_SETHOSTNAME                     = 0xDC2 // 3522
	SYS___SETHOSTNAME_A                 = 0xDC3 // 3523
	SYS_SETRESUID                       = 0xDC4 // 3524
	SYS_SETRESGID                       = 0xDC5 // 3525
	SYS_PTHREAD_CONDATTR_GETCLOCK       = 0xDC6 // 3526
	SYS_FLOCK                           = 0xDC7 // 3527
	SYS_FGETXATTR                       = 0xDC8 // 3528
	SYS___FGETXATTR_A                   = 0xDC9 // 3529
	SYS_FLISTXATTR                      = 0xDCA // 3530
	SYS___FLISTXATTR_A                  = 0xDCB // 3531
	SYS_FREMOVEXATTR                    = 0xDCC // 3532
	SYS___FREMOVEXATTR_A                = 0xDCD // 3533
	SYS_FSETXATTR                       = 0xDCE // 3534
	SYS___FSETXATTR_A                   = 0xDCF // 3535
	SYS_GETXATTR                        = 0xDD0 // 3536
	SYS___GETXATTR_A                    = 0xDD1 // 3537
	SYS_LGETXATTR                       = 0xDD2 // 3538
	SYS___LGETXATTR_A                   = 0xDD3 // 3539
	SYS_LISTXATTR                       = 0xDD4 // 3540
	SYS___LISTXATTR_A                   = 0xDD5 // 3541
	SYS_LLISTXATTR                      = 0xDD6 // 3542
	SYS___LLISTXATTR_A                  = 0xDD7 // 3543
	SYS_LREMOVEXATTR                    = 0xDD8 // 3544
	SYS___LREMOVEXATTR_A                = 0xDD9 // 3545
	SYS_LSETXATTR                       = 0xDDA // 3546
	SYS___LSETXATTR_A                   = 0xDDB // 3547
	SYS_REMOVEXATTR                     = 0xDDC // 3548
	SYS___REMOVEXATTR_A                 = 0xDDD // 3549
	SYS_SETXATTR                        = 0xDDE // 3550
	SYS___SETXATTR_A                    = 0xDDF // 3551
	SYS_FDATASYNC                       = 0xDE0 // 3552
	SYS_SYNCFS                          = 0xDE1 // 3553
	SYS_FUTIMES                         = 0xDE2 // 3554
	SYS_FUTIMESAT                       = 0xDE3 // 3555
	SYS___FUTIMESAT_A                   = 0xDE4 // 3556
	SYS_LUTIMES                         = 0xDE5 // 3557
	SYS___LUTIMES_A                     = 0xDE6 // 3558
	SYS_INET_ATON                       = 0xDE7 // 3559
	SYS_GETRANDOM                       = 0xDE8 // 3560
	SYS_GETTID                          = 0xDE9 // 3561
	SYS_MEMFD_CREATE                    = 0xDEA // 3562
	SYS___MEMFD_CREATE_A                = 0xDEB // 3563
	SYS_FACCESSAT                       = 0xDEC // 3564
	SYS___FACCESSAT_A                   = 0xDED // 3565
	SYS_FCHMODAT                        = 0xDEE // 3566
	SYS___FCHMODAT_A                    = 0xDEF // 3567
	SYS_FCHOWNAT                        = 0xDF0 // 3568
	SYS___FCHOWNAT_A                    = 0xDF1 // 3569
	SYS_FSTATAT                         = 0xDF2 // 3570
	SYS___FSTATAT_A                     = 0xDF3 // 3571
	SYS_LINKAT                          = 0xDF4 // 3572
	SYS___LINKAT_A                      = 0xDF5 // 3573
	SYS_MKDIRAT                         = 0xDF6 // 3574
	SYS___MKDIRAT_A                     = 0xDF7 // 3575
	SYS_MKFIFOAT                        = 0xDF8 // 3576
	SYS___MKFIFOAT_A                    = 0xDF9 // 3577
	SYS_MKNODAT                         = 0xDFA // 3578
	SYS___MKNODAT_A                     = 0xDFB // 3579
	SYS_OPENAT                          = 0xDFC // 3580
	SYS___OPENAT_A                      = 0xDFD // 3581
	SYS_READLINKAT                      = 0xDFE // 3582
	SYS___READLINKAT_A                  = 0xDFF // 3583
	SYS_RENAMEAT                        = 0xE00 // 3584
	SYS___RENAMEAT_A                    = 0xE01 // 3585
	SYS_RENAMEAT2                       = 0xE02 // 3586
	SYS___RENAMEAT2_A                   = 0xE03 // 3587
	SYS_SYMLINKAT                       = 0xE04 // 3588
	SYS___SYMLINKAT_A                   = 0xE05 // 3589
	SYS_UNLINKAT                        = 0xE06 // 3590
	SYS___UNLINKAT_A                    = 0xE07 // 3591
	SYS_SYSINFO                         = 0xE08 // 3592
	SYS_WAIT4                           = 0xE0A // 3594
	SYS_CLONE                           = 0xE0B // 3595
	SYS_UNSHARE                         = 0xE0C // 3596
	SYS_SETNS                           = 0xE0D // 3597
	SYS_CAPGET                          = 0xE0E // 3598
	SYS_CAPSET                          = 0xE0F // 3599
	SYS_STRCHRNUL                       = 0xE10 // 3600
	SYS_PTHREAD_CONDATTR_SETCLOCK       = 0xE12 // 3602
	SYS_OPEN_BY_HANDLE_AT               = 0xE13 // 3603
	SYS___OPEN_BY_HANDLE_AT_A           = 0xE14 // 3604
	SYS___INET_ATON_A                   = 0xE15 // 3605
	SYS_MOUNT1                          = 0xE16 // 3606
	SYS___MOUNT1_A                      = 0xE17 // 3607
	SYS_UMOUNT1                         = 0xE18 // 3608
	SYS___UMOUNT1_A                     = 0xE19 // 3609
	SYS_UMOUNT2                         = 0xE1A // 3610
	SYS___UMOUNT2_A                     = 0xE1B // 3611
	SYS___PRCTL_A                       = 0xE1C // 3612
	SYS_LOCALTIME_R2                    = 0xE1D // 3613
	SYS___LOCALTIME_R2_A                = 0xE1E // 3614
	SYS_OPENAT2                         = 0xE1F // 3615
	SYS___OPENAT2_A                     = 0xE20 // 3616
	SYS___LE_CEEMICT                    = 0xE21 // 3617
	SYS_GETENTROPY                      = 0xE22 // 3618
	SYS_NANOSLEEP                       = 0xE23 // 3619
	SYS_UTIMENSAT                       = 0xE24 // 3620
	SYS___UTIMENSAT_A                   = 0xE25 // 3621
	SYS_ASPRINTF                        = 0xE26 // 3622
	SYS___ASPRINTF_A                    = 0xE27 // 3623
	SYS_VASPRINTF                       = 0xE28 // 3624
	SYS___VASPRINTF_A                   = 0xE29 // 3625
	SYS_DPRINTF                         = 0xE2A // 3626
	SYS___DPRINTF_A                     = 0xE2B // 3627
	SYS_GETOPT_LONG                     = 0xE2C // 3628
	SYS___GETOPT_LONG_A                 = 0xE2D // 3629
	SYS_PSIGNAL                         = 0xE2E // 3630
	SYS___PSIGNAL_A                     = 0xE2F // 3631
	SYS_PSIGNAL_UNLOCKED                = 0xE30 // 3632
	SYS___PSIGNAL_UNLOCKED_A            = 0xE31 // 3633
	SYS_FSTATAT_O                       = 0xE32 // 3634
	SYS___FSTATAT_O_A                   = 0xE33 // 3635
	SYS_FSTATAT64                       = 0xE34 // 3636
	SYS___FSTATAT64_A                   = 0xE35 // 3637
	SYS___CHATTRAT                      = 0xE36 // 3638
	SYS_____CHATTRAT_A                  = 0xE37 // 3639
	SYS___CHATTRAT64                    = 0xE38 // 3640
	SYS_____CHATTRAT64_A                = 0xE39 // 3641
	SYS_MADVISE                         = 0xE3A // 3642
	SYS___AUTHENTICATE                  = 0xE3B // 3643

)
